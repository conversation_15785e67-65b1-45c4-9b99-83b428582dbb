import React from 'react';
import { View, StyleSheet, SafeAreaView } from 'react-native';
import { InAppUpdateDebugger } from '@/components/InAppUpdateDebugger';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function DebugUpdatesScreen() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const colors = {
    background: isDark ? '#000000' : '#ffffff',
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <InAppUpdateDebugger />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
});
