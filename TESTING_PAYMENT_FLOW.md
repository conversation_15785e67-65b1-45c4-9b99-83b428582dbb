# Testing Payment Flow - Infratask App

This document outlines how to test the complete payment flow in the Infratask app using the new Razorpay integration with Supabase Edge Functions.

## 🏗️ Architecture Overview

The payment system consists of:

1. **Frontend (React Native)**: Subscription screen with payment buttons
2. **Edge Function**: `create-razorpay-order` - Creates secure Razorpay orders
3. **Razorpay API**: Handles actual payment processing
4. **Database**: Logs orders and updates subscriptions

## 🔧 Setup Requirements

### 1. Environment Variables

Ensure these are set in your Supabase project:

```bash
RAZORPAY_KEY_ID=rzp_test_NQ26LCch0J1GHa
RAZORPAY_KEY_SECRET=gsedvop5Yvz7fKnilyqy8L37
```

### 2. Database Tables

Run the migration to create the `razorpay_orders` table:

```bash
supabase db push
```

### 3. Edge Function Deployment

Deploy the edge function:

```bash
supabase functions deploy create-razorpay-order
```

## 🧪 Testing Steps

### Step 1: Test Edge Function Directly

Test the edge function using curl or Postman:

```bash
curl -X POST 'https://your-project.supabase.co/functions/v1/create-razorpay-order' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "amount": 24900,
    "currency": "INR",
    "receipt": "test_receipt_123",
    "notes": {
      "plan_id": "standard",
      "test": "true"
    }
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "order": {
    "id": "order_ABC123...",
    "amount": 24900,
    "currency": "INR",
    "receipt": "test_receipt_123",
    "status": "created",
    ...
  },
  "message": "Order created successfully"
}
```

### Step 2: Test Client Library

Test the TypeScript client functions:

```typescript
import { createSubscriptionOrder } from '@/lib/razorpay-orders';

// Test standard plan order creation
const testOrderCreation = async () => {
  try {
    const order = await createSubscriptionOrder('standard');
    console.log('Order created:', order);
  } catch (error) {
    console.error('Order creation failed:', error);
  }
};
```

### Step 3: Test Complete Payment Flow

1. **Open the app** and navigate to the subscription screen
2. **Login** with a test user account
3. **Click** on either "Pay with Razorpay (Standard Plan)" or "Pay with Razorpay (Premium Plan)"
4. **Verify** the following sequence:

   a. Order creation request is made to edge function
   b. Razorpay checkout opens with correct details
   c. Payment can be completed (use test cards)
   d. Success callback updates the database
   e. User subscription is activated

## 💳 Test Payment Cards

Use these Razorpay test cards:

### Successful Payments
- **Card Number**: 4111 1111 1111 1111
- **Expiry**: Any future date
- **CVV**: Any 3 digits
- **Name**: Any name

### Failed Payments
- **Card Number**: 4000 0000 0000 0002
- **Expiry**: Any future date
- **CVV**: Any 3 digits

## 🔍 Debugging & Monitoring

### 1. Check Edge Function Logs

```bash
supabase functions logs create-razorpay-order
```

### 2. Monitor Database Changes

Check the `razorpay_orders` table:

```sql
SELECT * FROM razorpay_orders ORDER BY created_at DESC LIMIT 10;
```

Check the `user_subscriptions` table:

```sql
SELECT * FROM user_subscriptions ORDER BY created_at DESC LIMIT 10;
```

### 3. React Native Debugging

Enable console logging in the app:

```typescript
// In subscription.tsx
console.log('Creating Razorpay order for plan:', planId);
console.log('Order created successfully:', orderResponse.order.id);
console.log('Payment successful:', paymentData);
```

## ✅ Test Scenarios

### Scenario 1: Successful Standard Plan Purchase

1. User clicks "Pay with Razorpay (Standard Plan)"
2. Order is created with amount: 24900 (₹249)
3. Razorpay checkout opens
4. User completes payment with test card
5. Database is updated with:
   - New entry in `razorpay_orders`
   - Updated `user_subscriptions` with active status

### Scenario 2: Successful Premium Plan Purchase

1. User clicks "Pay with Razorpay (Premium Plan)"
2. Order is created with amount: 99900 (₹999)
3. Payment flow completes successfully
4. User gets premium subscription

### Scenario 3: Payment Failure

1. User initiates payment
2. Uses failed test card
3. Payment fails gracefully
4. User sees error message
5. No database changes occur

### Scenario 4: Payment Cancellation

1. User initiates payment
2. Closes Razorpay modal without paying
3. User sees cancellation message
4. No database changes occur

## 🚨 Common Issues & Solutions

### Issue 1: Edge Function Not Found
**Solution**: Ensure the function is deployed and the URL is correct

### Issue 2: Razorpay Credentials Error
**Solution**: Verify environment variables are set correctly

### Issue 3: Database Permission Error
**Solution**: Check RLS policies on `razorpay_orders` table

### Issue 4: Order Creation Fails
**Solution**: Check Razorpay API response and network connectivity

## 📊 Success Metrics

A successful test should show:

- ✅ Edge function responds within 2-3 seconds
- ✅ Razorpay order ID is generated (format: `order_ABC123...`)
- ✅ Payment modal opens correctly
- ✅ Test payment completes successfully
- ✅ Database records are created/updated
- ✅ User subscription status changes to 'active'
- ✅ App UI reflects the new subscription status

## 🔄 Cleanup After Testing

After testing, clean up test data:

```sql
-- Remove test orders
DELETE FROM razorpay_orders WHERE notes->>'test' = 'true';

-- Reset test user subscriptions (optional)
DELETE FROM user_subscriptions WHERE user_id = 'test-user-id';
```

## 📝 Notes

- Always test in Razorpay's test mode first
- Verify webhook handling if implemented
- Test on both Android and iOS if applicable
- Monitor for memory leaks during repeated testing
- Test with different network conditions