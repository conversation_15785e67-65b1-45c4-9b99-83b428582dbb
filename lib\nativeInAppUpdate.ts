import { NativeEventEmitter, NativeModules, Platform } from 'react-native';

interface InAppUpdateModuleInterface {
  initialize(): Promise<boolean>;
  checkForUpdate(): Promise<{
    updateAvailable: boolean;
    availableVersionCode?: number;
    immediateUpdateAllowed?: boolean;
    flexibleUpdateAllowed?: boolean;
    updatePriority?: number;
    clientVersionStalenessDays?: number;
    updateInProgress?: boolean;
  }>;
  startImmediateUpdate(): Promise<boolean>;
  startFlexibleUpdate(): Promise<boolean>;
  completeFlexibleUpdate(): Promise<boolean>;
  getUpdateInfo(): Promise<{
    availableVersionCode: number;
    updateAvailability: number;
    immediateUpdateAllowed: boolean;
    flexibleUpdateAllowed: boolean;
    updatePriority: number;
    clientVersionStalenessDays: number;
  }>;
}

// Native module interface
const InAppUpdateModule: InAppUpdateModuleInterface = NativeModules.InAppUpdateModule;

// Event emitter for update events
const eventEmitter = Platform.OS === 'android' ? new NativeEventEmitter(InAppUpdateModule) : null;

export interface NativeUpdateInfo {
  updateAvailable: boolean;
  availableVersionCode?: number;
  immediateUpdateAllowed?: boolean;
  flexibleUpdateAllowed?: boolean;
  updatePriority?: number;
  clientVersionStalenessDays?: number;
  updateInProgress?: boolean;
}

export interface UpdateDownloadProgress {
  progress: number; // 0 to 1
}

export type UpdateEventType = 
  | 'onUpdateDownloaded'
  | 'onUpdateDownloading'
  | 'onUpdateFailed'
  | 'onUpdateInstalled';

class NativeInAppUpdateManager {
  private static instance: NativeInAppUpdateManager;
  private initialized = false;
  private listeners: Map<string, (data: any) => void> = new Map();

  private constructor() {}

  static getInstance(): NativeInAppUpdateManager {
    if (!NativeInAppUpdateManager.instance) {
      NativeInAppUpdateManager.instance = new NativeInAppUpdateManager();
    }
    return NativeInAppUpdateManager.instance;
  }

  /**
   * Initialize the native in-app update module
   */
  async initialize(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      console.log('Native in-app updates only supported on Android');
      return false;
    }

    // Don't initialize in development builds
    if (__DEV__) {
      console.log('Native in-app updates not available in development builds');
      return false;
    }

    if (!InAppUpdateModule) {
      console.error('InAppUpdateModule not found. Make sure the native module is properly linked.');
      return false;
    }

    try {
      const result = await InAppUpdateModule.initialize();
      this.initialized = result;
      return result;
    } catch (error) {
      console.error('Failed to initialize InAppUpdateModule:', error);
      return false;
    }
  }

  /**
   * Check if the module is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Check for updates using native Play Core
   */
  async checkForUpdate(): Promise<NativeUpdateInfo> {
    if (!this.initialized) {
      throw new Error('InAppUpdateModule not initialized. Call initialize() first.');
    }

    try {
      return await InAppUpdateModule.checkForUpdate();
    } catch (error) {
      console.error('Error checking for native updates:', error);
      throw error;
    }
  }

  /**
   * Start immediate update flow
   */
  async startImmediateUpdate(): Promise<boolean> {
    if (!this.initialized) {
      throw new Error('InAppUpdateModule not initialized. Call initialize() first.');
    }

    try {
      return await InAppUpdateModule.startImmediateUpdate();
    } catch (error) {
      console.error('Error starting immediate update:', error);
      throw error;
    }
  }

  /**
   * Start flexible update flow
   */
  async startFlexibleUpdate(): Promise<boolean> {
    if (!this.initialized) {
      throw new Error('InAppUpdateModule not initialized. Call initialize() first.');
    }

    try {
      return await InAppUpdateModule.startFlexibleUpdate();
    } catch (error) {
      console.error('Error starting flexible update:', error);
      throw error;
    }
  }

  /**
   * Complete flexible update (restart app)
   */
  async completeFlexibleUpdate(): Promise<boolean> {
    if (!this.initialized) {
      throw new Error('InAppUpdateModule not initialized. Call initialize() first.');
    }

    try {
      return await InAppUpdateModule.completeFlexibleUpdate();
    } catch (error) {
      console.error('Error completing flexible update:', error);
      throw error;
    }
  }

  /**
   * Get detailed update information
   */
  async getUpdateInfo(): Promise<any> {
    if (!this.initialized) {
      throw new Error('InAppUpdateModule not initialized. Call initialize() first.');
    }

    try {
      return await InAppUpdateModule.getUpdateInfo();
    } catch (error) {
      console.error('Error getting update info:', error);
      throw error;
    }
  }

  /**
   * Add event listener for update events
   */
  addEventListener(eventType: UpdateEventType, listener: (data: any) => void): void {
    if (Platform.OS !== 'android' || !eventEmitter) return;

    const subscription = eventEmitter.addListener(eventType, listener);
    this.listeners.set(`${eventType}_${Date.now()}`, () => subscription.remove());
  }

  /**
   * Remove all event listeners
   */
  removeAllListeners(): void {
    this.listeners.forEach(removeListener => removeListener());
    this.listeners.clear();
  }

  /**
   * Check if native updates are supported
   */
  isSupported(): boolean {
    return Platform.OS === 'android' && !!InAppUpdateModule;
  }
}

// Export singleton instance
export const nativeInAppUpdateManager = NativeInAppUpdateManager.getInstance();
