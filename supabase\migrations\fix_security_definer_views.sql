-- Fix Security Definer Views Migration
-- This migration removes SECURITY DEFINER from views and ensures proper RLS policies

-- Drop and recreate sub_contractor_laborers_with_attendance view without SECURITY DEFINER
DROP VIEW IF EXISTS public.sub_contractor_laborers_with_attendance;

CREATE VIEW public.sub_contractor_laborers_with_attendance AS
SELECT 
    scl.*,
    sc.name as contractor_name,
    sc.category as contractor_category
FROM sub_contractor_laborers scl
JOIN sub_contractors sc ON scl.sub_contractor_id = sc.id
WHERE scl.is_active = true;

-- Drop and recreate sub_contractor_attendance_summary view without SECURITY DEFINER
DROP VIEW IF EXISTS public.sub_contractor_attendance_summary;

CREATE VIEW public.sub_contractor_attendance_summary AS
SELECT 
    sc.id as sub_contractor_id,
    sc.name as contractor_name,
    sc.site_id,
    scla.attendance_date,
    COUNT(CASE WHEN scla.status = 'present' THEN 1 END) as present_count,
    COUNT(CASE WHEN scla.status = 'absent' THEN 1 END) as absent_count,
    COUNT(CASE WHEN scla.status = 'half_day' THEN 1 END) as half_day_count,
    COUNT(CASE WHEN scla.status = 'overtime' THEN 1 END) as overtime_count,
    SUM(CASE WHEN scla.status = 'overtime' THEN scla.overtime_hours ELSE 0 END) as total_overtime_hours,
    COUNT(scl.id) as total_laborers,
    COUNT(scla.id) as marked_laborers,
    CASE 
        WHEN COUNT(scl.id) = 0 THEN 'no_laborers'
        WHEN COUNT(scla.id) = 0 THEN 'not_marked'
        WHEN COUNT(CASE WHEN scla.status = 'present' OR scla.status = 'overtime' THEN 1 END) = COUNT(scl.id) THEN 'present'
        WHEN COUNT(CASE WHEN scla.status = 'absent' THEN 1 END) = COUNT(scl.id) THEN 'absent'
        ELSE 'mixed'
    END as overall_status
FROM sub_contractors sc
LEFT JOIN sub_contractor_laborers scl ON sc.id = scl.sub_contractor_id AND scl.is_active = true
LEFT JOIN sub_contractor_laborer_attendance scla ON scl.id = scla.laborer_id
GROUP BY sc.id, sc.name, sc.site_id, scla.attendance_date;

-- Drop and recreate attendance_with_labor view if it exists
DROP VIEW IF EXISTS public.attendance_with_labor;

CREATE VIEW public.attendance_with_labor AS
SELECT 
    a.*,
    l.full_name as laborer_name,
    lc.name as labor_category,
    l.daily_wage,
    s.name as site_name
FROM attendance a
JOIN laborers l ON a.laborer_id = l.id
LEFT JOIN labor_categories lc ON l.category_id = lc.id
JOIN sites s ON a.site_id = s.id;

-- Drop and recreate transaction_view if it exists
DROP VIEW IF EXISTS public.transaction_view;

CREATE VIEW public.transaction_view AS
SELECT 
    t.*,
    s.name as site_name,
    CASE 
        WHEN t.linked_transaction_id IS NOT NULL THEN 'linked'
        ELSE 'standalone'
    END as transaction_type
FROM transactions t
LEFT JOIN sites s ON t.site_id = s.id;

-- Grant appropriate permissions to authenticated users
GRANT SELECT ON public.sub_contractor_laborers_with_attendance TO authenticated;
GRANT SELECT ON public.sub_contractor_attendance_summary TO authenticated;
GRANT SELECT ON public.attendance_with_labor TO authenticated;
GRANT SELECT ON public.transaction_view TO authenticated;

-- Ensure RLS is enabled for the views (inherits from underlying tables)
-- Views automatically inherit RLS from their underlying tables when not using SECURITY DEFINER

-- Add comments to document the security approach
COMMENT ON VIEW public.sub_contractor_laborers_with_attendance IS 'View for sub-contractor laborers with attendance info. RLS inherited from underlying tables.';
COMMENT ON VIEW public.sub_contractor_attendance_summary IS 'Summary view for sub-contractor attendance. RLS inherited from underlying tables.';
COMMENT ON VIEW public.attendance_with_labor IS 'View combining attendance with laborer details. RLS inherited from underlying tables.';
COMMENT ON VIEW public.transaction_view IS 'Enhanced transaction view with site details. RLS inherited from underlying tables.'; 