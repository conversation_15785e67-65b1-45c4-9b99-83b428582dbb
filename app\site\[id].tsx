import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { exportAttendanceAsExcel, exportAttendanceAsPDF, exportTaskReportAsPDF } from '@/lib/export';
import { canExportReports, canViewReports, UserRole } from '@/lib/permissions';
import { supabase } from '@/lib/supabase';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useCallback, useEffect, useLayoutEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Dimensions,
    Image,
    Platform,
    RefreshControl,
    ScrollView,
    StyleSheet,
    ToastAndroid,
    TouchableOpacity,
    useColorScheme,
    View
} from 'react-native';

const { width } = Dimensions.get('window');

type Site = {
  id: string;
  name: string;
  organization_name: string;
  site_image_url: string | null;
  created_at: string;
  updated_at: string;
  status: 'active' | 'completed';
  owner_id: string;
};

type SiteMember = {
  id: string;
  user_id: string;
  role: 'Super Admin' | 'Admin' | 'Member';
  profile: {
    full_name: string;
    profile_image_url: string | null;
  };
};

// Analytics data
type AnalyticsData = {
  totalTasksCount: number;
  totalTasksPercentage: number;
  activeTasksCount: number;
  workersCount: number;
  activeWorkersCount: number; // Workers with attendance
  tasksInProgressPercentage: number;
};

// Implement report generation 
const generateReport = async (
  type: string, 
  siteId: string, 
  dateRange: { start: string; end: string },
  selectedCategory: string
) => {
  if (!siteId) return null;
  
  // Base query for each report type
  let query: any = null;
  
  try {
    // Build query based on report type
    if (type === 'tasks') {
      query = supabase
        .from('tasks')
        .select('*')
        .eq('site_id', siteId);
        
      // Filter by task status if category is selected
      if (selectedCategory && selectedCategory !== 'All Tasks') {
        switch (selectedCategory) {
          case 'Pending':
            query = query.eq('status', 'pending');
            break;
          case 'In Progress':
            query = query.eq('status', 'in progress');
            break;
          case 'Completed':
            query = query.eq('status', 'completed');
            break;
          case 'High Priority':
            query = query.eq('priority', 'high');
            break;
          case 'Low Priority':
            query = query.eq('priority', 'low');
            break;
        }
      }
      
    } else if (type === 'attendance') {
      try {
        console.log('Building attendance query');
        query = supabase
          .from('attendance_with_labor')
          .select(`
            id,
            laborer_id,
            site_id,
            status,
            attendance_date,
            created_at,
            updated_at,
            laborer_name,
            labor_category,
            daily_wage,
            site_name
          `)
          .eq('site_id', siteId);
          
        console.log('Attendance query structure:', query);
          
        // Updated filtering logic for attendance
        if (selectedCategory) {
          // Handle status categories
          if (['Present', 'Absent', 'Half-day', 'Late', 'Leave'].includes(selectedCategory)) {
            // Filter by attendance status
            const status = selectedCategory.toLowerCase();
            query = query.eq('status', status);
          } else if (selectedCategory === 'All Statuses') {
            // No status filter - will show all statuses
            console.log('Showing all statuses - no status filter applied');
          } else if (selectedCategory !== 'All Categories') {
            // It's a labor category, so we need to post-process the data after fetching
            console.log(`Will post-filter by labor category: ${selectedCategory}`);
          }
        }
      } catch (error) {
        console.error('Error building attendance query:', error);
        return null;
      }
      
    } else if (type === 'materials') {
      query = supabase
        .from('materials')
        .select('*')
        .eq('site_id', siteId);
        
      // Filter by material status if category is selected
      if (selectedCategory && selectedCategory !== 'All Materials') {
        switch (selectedCategory) {
          case 'Available':
            query = query.gt('quantity', 10); // Arbitrary threshold
            break;
          case 'Low Stock':
            query = query.lte('quantity', 10).gt('quantity', 0);
            break;
          case 'Out of Stock':
            query = query.eq('quantity', 0);
            break;
        }
      }
    }
    
    if (!query) return null;
    
    // Apply date range filters if provided
    if (dateRange.start && dateRange.start.trim() !== '' && query) {
      try {
        const startDate = new Date(dateRange.start);
        // Check if date is valid before using it
        if (!isNaN(startDate.getTime())) {
          const startDateString = startDate.toISOString().split('T')[0];

          if (type === 'tasks') {
            query = query.gte('due_date', startDateString);
          } else if (type === 'attendance') {
            query = query.gte('attendance_date', startDateString);
          } else if (type === 'materials') {
            query = query.gte('updated_at', startDateString);
          }
        }
      } catch (error) {
        console.error('Invalid start date:', dateRange.start, error);
      }
    }
    
    if (dateRange.end && dateRange.end.trim() !== '' && query) {
      try {
        const endDate = new Date(dateRange.end);
        // Check if date is valid before using it
        if (!isNaN(endDate.getTime())) {
          // For end date, we want to include the entire day, so we use lte with the same date
          const endDateString = endDate.toISOString().split('T')[0];

          if (type === 'tasks') {
            query = query.lte('due_date', endDateString);
          } else if (type === 'attendance') {
            query = query.lte('attendance_date', endDateString);
          } else if (type === 'materials') {
            query = query.lte('updated_at', endDateString);
          }
        }
      } catch (error) {
        console.error('Invalid end date:', dateRange.end, error);
      }
    }
    
    // Execute the query
    if (!query) return null;

    const { data, error } = await query;

    if (error) {
      console.error(`Error fetching ${type} report:`, error);
      return null;
    }

    // Debug the data structure
    if (type === 'attendance' && data && data.length > 0) {
      console.log('First attendance record structure:', JSON.stringify(data[0], null, 2));
    }
    
    // Post-process data if needed
    if (type === 'attendance' && data && selectedCategory && 
        selectedCategory !== 'All Categories' && 
        selectedCategory !== 'All Statuses' &&
        !['Present', 'Absent', 'Half-day', 'Late', 'Leave'].includes(selectedCategory)) {
      // Filter by labor category
      console.log(`Post-processing to filter by labor category: ${selectedCategory}`);
      console.log(`Before filtering: ${data.length} records`);
      
      const filtered = data.filter((record: any) => {
        // Use the labor_category field directly from the view
        const laborCategory = record.labor_category;

        if (!laborCategory) {
          console.log(`Record ${record.id} has no labor category`);
          return false;
        }

        const matches = laborCategory === selectedCategory;

        if (matches) {
          console.log(`Found matching record: laborer_id=${record.laborer_id}, category=${laborCategory}`);
        }

        return matches;
      });
      
      console.log(`After filtering: ${filtered.length} records`);
      return filtered;
    }
    
    return data;
    
  } catch (error) {
    console.error(`Error generating ${type} report:`, error);
    return null;
  }
};

export default function SiteDetailsScreen() {
  const { id } = useLocalSearchParams();
  const [site, setSite] = useState<Site | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [members, setMembers] = useState<SiteMember[]>([]);
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [isOwner, setIsOwner] = useState(false);
  const colorScheme = useColorScheme();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    totalTasksCount: 0,
    totalTasksPercentage: 0,
    activeTasksCount: 0,
    workersCount: 0,
    activeWorkersCount: 0,
    tasksInProgressPercentage: 0,
  });
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [taskCategory, setTaskCategory] = useState('');
  const [attendanceCategory, setAttendanceCategory] = useState('');
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);
  const [reportType, setReportType] = useState('tasks'); // 'tasks', 'attendance', or 'materials'
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [laborCategories, setLaborCategories] = useState<string[]>([]);
  
  // Check permission for reports
  const userHasReportAccess = canViewReports(userRole);
  
  // Set navigation title immediately to prevent "site/[id]" from showing
  useLayoutEffect(() => {
    router.setParams({ title: site?.name || 'Site Details' });
  }, [site?.name]);
  
  // Available categories based on report type
  const getCategories = () => {
    switch (reportType) {
      case 'tasks':
        return ['All Tasks', 'Pending', 'In Progress', 'Completed', 'High Priority', 'Low Priority'];
      case 'attendance':
        // Change to use status and labor categories separately for better filtering
        const statusCategories = ['All Categories', 'All Statuses', 'Present', 'Absent', 'Half-day', 'Late', 'Leave'];
        
        // Add labor categories if they've been loaded
        if (laborCategories.length > 0) {
          return [...statusCategories, ...laborCategories];
        }
        return statusCategories;
      case 'materials':
        return ['All Materials', 'Available', 'Low Stock', 'Out of Stock'];
      default:
        return ['All'];
    }
  };
  
  // Fetch site data function - reusable for both initial load and refresh
  const fetchSiteData = useCallback(async (isRefreshing = false) => {
    if (!id) return;
    
    try {
      if (!isRefreshing) {
        setLoading(true);
      }
      
      // Get current user
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) return;
      
      // Get user profile
      const { data: profileData } = await supabase
        .from('profiles')
        .select('id')
        .eq('user_id', userData.user.id)
        .single();
      
      if (!profileData) return;
      
      // Get site details
      const { data: siteData, error: siteError } = await supabase
        .from('sites')
        .select('*')
        .eq('id', id)
        .single();
      
      if (siteError || !siteData) {
        console.error('Error fetching site:', siteError);
        Alert.alert('Error', 'Failed to load site details');
        router.back();
        return;
      }
      
      setSite(siteData);
      setIsOwner(siteData.owner_id === profileData.id);
      
      // Get site members with profiles
      const { data: membersData, error: membersError } = await supabase
        .from('site_members')
        .select(`
          id,
          user_id,
          role,
          profile:profiles(full_name, profile_image_url)
        `)
        .eq('site_id', id);
      
      // Get direct count of site members for accuracy
      const { count: membersCount, error: countError } = await supabase
        .from('site_members')
        .select('*', { count: 'exact', head: true })
        .eq('site_id', id);
      
      let siteMembers: SiteMember[] = [];
      
      if (!membersError && membersData) {
        // Type assertion to ensure proper format
        const typedMembersData = membersData.map(member => ({
          ...member,
          profile: {
            full_name: Array.isArray(member.profile) && member.profile[0]?.full_name || '',
            profile_image_url: Array.isArray(member.profile) && member.profile[0]?.profile_image_url || null
          }
        })) as SiteMember[];
        
        setMembers(typedMembersData);
        siteMembers = typedMembersData;
        
        // Find current user's role
        const userMember = typedMembersData.find(m => m.user_id === userData.user.id);
        if (userMember) {
          setUserRole(userMember.role as UserRole);
        }

        // Set workers count separately to ensure it updates correctly
        // Use direct count if available, otherwise fall back to array length
        const workersCount = !countError && membersCount !== null ? membersCount : typedMembersData.length;
        
        setAnalyticsData(prev => ({
          ...prev,
          workersCount
        }));
      } else if (!countError && membersCount !== null) {
        // Even if we couldn't get member details, we still have the count
        setAnalyticsData(prev => ({
          ...prev,
          workersCount: membersCount
        }));
      }

      // Get workers with attendance records (active workers)
      try {
        // First try to get count from attendance records for today
        const today = new Date().toISOString().split('T')[0];
        
        const { data: attendanceData, error: attendanceError } = await supabase
          .from('attendance')
          .select('laborer_id')
          .eq('site_id', id)
          .eq('attendance_date', today)
          .eq('status', 'present');
          
        if (!attendanceError && attendanceData) {
          // Get unique laborer ids from attendance
          const uniqueWorkers = new Set(attendanceData.map(record => record.laborer_id));
          console.log('Workers present today:', uniqueWorkers.size);
          
          setAnalyticsData(prev => ({
            ...prev,
            activeWorkersCount: uniqueWorkers.size
          }));
        } else {
          // Fallback to getting all laborers if no attendance records
          const { data: laborersData, error: laborersError } = await supabase
            .from('laborers')
            .select('id')
            .eq('site_id', id)
            .eq('is_active', true);
            
          if (!laborersError && laborersData) {
            console.log('Active workers (from laborers table):', laborersData.length);
            setAnalyticsData(prev => ({
              ...prev,
              activeWorkersCount: laborersData.length
            }));
          } else {
            // Ultimate fallback - use team members count
            console.log('Using team members as fallback for active workers count');
            setAnalyticsData(prev => ({
              ...prev,
              activeWorkersCount: prev.workersCount
            }));
          }
        }
      } catch (error) {
        console.error('Error fetching active workers:', error);
        // Fallback to member count if there's an error
        setAnalyticsData(prev => ({
          ...prev,
          activeWorkersCount: siteMembers.length
        }));
      }

      // Fetch tasks for analytics data
      const { data: tasksData, error: tasksError } = await supabase
        .from('tasks')
        .select('*')
        .eq('site_id', id);
      
      if (!tasksError && tasksData) {
        const totalTasks = tasksData.length;
        const completedTasks = tasksData.filter(task => task.status === 'completed').length;
        const activeTasks = tasksData.filter(task => task.status === 'pending' || task.status === 'in progress').length;
        const inProgressTasks = tasksData.filter(task => task.status === 'in progress').length;
        
        const totalTasksPercentage = totalTasks > 0 
          ? Math.round((completedTasks / totalTasks) * 100) 
          : 0;
        
        const tasksInProgressPercentage = totalTasks > 0
          ? Math.round((inProgressTasks / totalTasks) * 100)
          : 0;
        
        setAnalyticsData(prev => ({
          ...prev,
          totalTasksCount: totalTasks,
          totalTasksPercentage,
          activeTasksCount: activeTasks,
          tasksInProgressPercentage,
        }));
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
      if (isRefreshing) {
        setRefreshing(false);
      }
    }
  }, [id]);
  
  // Initial data fetch
  useEffect(() => {
    fetchSiteData();
  }, [fetchSiteData]);
  
  // Handle refresh
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchSiteData(true);
  }, [fetchSiteData]);
  
  // Toggle site status
  const toggleSiteStatus = async () => {
    if (!site || !isOwner) return;
    
    try {
      const newStatus = site.status === 'active' ? 'completed' : 'active';
      
      const { error } = await supabase
        .from('sites')
        .update({ status: newStatus })
        .eq('id', site.id);
      
      if (error) {
        console.error('Error updating site status:', error);
        Alert.alert('Error', 'Failed to update site status');
        return;
      }
      
      setSite({
        ...site,
        status: newStatus,
      });
      
    } catch (error) {
      console.error('Error:', error);
    }
  };

  const handleDownloadReport = async (type: string, format: 'pdf' | 'excel' = 'pdf') => {
    // Check if user has permission to export reports
    if (!canExportReports(userRole)) {
      Alert.alert('Permission Denied', 'You do not have permission to download reports.');
      return;
    }
    
    // Set report type based on button clicked
    const reportType = type.toLowerCase();
    setReportType(reportType);
    
    if (!site?.id) return;
    
    setIsGeneratingReport(true);
    
    try {
      // Use the appropriate category based on report type
      const selectedCategory = reportType === 'tasks' ? taskCategory : 
                             reportType === 'attendance' ? attendanceCategory : '';
      
      // Create a standardized date range object for the query
      let processedDateRange = { ...dateRange };
      
      // If it's attendance report and no date range is specified, default to current month
      if (reportType === 'attendance' && (!dateRange.start || !dateRange.end || dateRange.start.trim() === '' || dateRange.end.trim() === '')) {
        const now = new Date();
        const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

        if (!dateRange.start || dateRange.start.trim() === '') {
          processedDateRange.start = formatDate(firstDayOfMonth);
          setStartDate(firstDayOfMonth);
          // Update UI to reflect the default date
          setDateRange(prev => ({
            ...prev,
            start: formatDate(firstDayOfMonth)
          }));
        }

        if (!dateRange.end || dateRange.end.trim() === '') {
          processedDateRange.end = formatDate(now);
          setEndDate(now);
          // Update UI to reflect the default date
          setDateRange(prev => ({
            ...prev,
            end: formatDate(now)
          }));
        }
      }


      
      // Process the report data with validated date range
      const reportData = await generateReport(
        reportType, 
        site.id, 
        processedDateRange, 
        selectedCategory
      );
      
      if (!reportData || reportData.length === 0) {
        Alert.alert('No Data', `No ${type} found for the selected filters. Please try different filters.`);
        setIsGeneratingReport(false);
        return;
      }
      
      if (reportType === 'tasks') {
        // For task reports, generate PDF
        try {
          // Fetch subcategories for all tasks
          const taskSubcategories: Record<string, any[]> = {};
          
          // Process only if there are tasks
          if (reportData && reportData.length > 0) {
            // Create an array of task IDs
            const taskIds = reportData.map((task: any) => task.id);
            
            // Fetch subcategories for all tasks in one query
            const { data: subcategoriesData, error: subcategoriesError } = await supabase
              .from('task_subcategories')
              .select('*')
              .in('task_id', taskIds);
            
            if (!subcategoriesError && subcategoriesData) {
              // Group subcategories by task ID
              subcategoriesData.forEach((subcat: any) => {
                if (!taskSubcategories[subcat.task_id]) {
                  taskSubcategories[subcat.task_id] = [];
                }
                taskSubcategories[subcat.task_id].push(subcat);
              });
            }
          }
          
          // Generate and share the PDF
          await exportTaskReportAsPDF(
            reportData,
            taskSubcategories,
            site.name,
            processedDateRange,
            selectedCategory
          );
          
          // Show success message
          if (Platform.OS === 'android') {
            ToastAndroid.show('Task report PDF created successfully', ToastAndroid.SHORT);
          } else {
            Alert.alert('Success', 'Task report PDF created successfully');
          }
        } catch (pdfError) {
          console.error('PDF generation error:', pdfError);
          Alert.alert('PDF Error', 'There was a problem generating the PDF. Please try again.');
        }
      } else if (reportType === 'attendance') {
        // For attendance reports, generate PDF or Excel
        try {
          // Debug attendance data 
          console.log('Attendance report data count:', reportData.length);
          if (reportData.length > 0) {
            console.log('First record labor info:', reportData[0].labor);
          }
          
          // Process selected category for attendance reports
          let categoryFilter = undefined;
          if (selectedCategory && selectedCategory !== 'All Categories') {
            if (['Present', 'Absent', 'Half-day', 'Late', 'Leave'].includes(selectedCategory)) {
              categoryFilter = `Status: ${selectedCategory}`;
            } else if (selectedCategory !== 'All Statuses') {
              categoryFilter = `Labor Category: ${selectedCategory}`;
            }
          }
          
          // Pass the already filtered data to the export functions
          if (format === 'pdf') {
            await exportAttendanceAsPDF(
              reportData,
              site.name,
              processedDateRange,
              categoryFilter
            );
            
            // Show success message
            if (Platform.OS === 'android') {
              ToastAndroid.show('Attendance report PDF created successfully', ToastAndroid.SHORT);
            } else {
              Alert.alert('Success', 'Attendance report PDF created successfully');
            }
          } else {
            await exportAttendanceAsExcel(
              reportData,
              site.name,
              processedDateRange,
              categoryFilter
            );
            
            // Show success message
            if (Platform.OS === 'android') {
              ToastAndroid.show('Attendance Excel report created successfully', ToastAndroid.SHORT);
            } else {
              Alert.alert('Success', 'Attendance Excel report created successfully');
            }
          }
        } catch (exportError) {
          console.error('Attendance report generation error:', exportError);
          Alert.alert('Export Error', 'There was a problem generating the attendance report. Please try again.');
        }
      } else {
        // For other report types, just show a success message for now
        const message = `Generated ${type} report with ${reportData.length} entries`;
        console.log(message);
        
        // Show toast on Android or alert on iOS
        if (Platform.OS === 'android') {
          ToastAndroid.show(message, ToastAndroid.SHORT);
        } else {
          Alert.alert('Report Generated', message);
        }
      }
      
    } catch (error) {
      console.error('Report generation error:', error);
      Alert.alert('Error', `Failed to generate ${type} report. Please try again.`);
    } finally {
      setIsGeneratingReport(false);
    }
  };

  // Format date for database queries (YYYY-MM-DD format)
  const formatDate = (date: Date) => {
    try {
      // Make sure the date is valid
      if (isNaN(date.getTime())) {
        return '';
      }
      return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD format
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  // Format date for display
  const formatDateForDisplay = (date: Date) => {
    try {
      // Make sure the date is valid
      if (isNaN(date.getTime())) {
        return '';
      }
      return date.toLocaleDateString();
    } catch (error) {
      console.error('Error formatting date for display:', error);
      return '';
    }
  };

  // Handle start date change
  const onStartDateChange = (event: any, selectedDate?: Date) => {
    setShowStartDatePicker(false);
    if (selectedDate && !isNaN(selectedDate.getTime())) {
      // Store the selected date for future reference
      setStartDate(selectedDate);
      
      // Format the date for display
      const formattedDate = formatDate(selectedDate);
      
      // Update date range
      setDateRange(prev => ({
        ...prev,
        start: formattedDate
      }));
      
      // If end date is not set, open the end date picker automatically
      if (!dateRange.end) {
        // Set end date to same as start date initially
        setEndDate(selectedDate);
        // Wait a moment before showing the next picker
        setTimeout(() => {
          setShowEndDatePicker(true);
        }, 500);
      } else {
        // Make sure start date is not after end date
        const endDateObj = new Date(dateRange.end);
        if (selectedDate > endDateObj) {
          // Update end date to match start date
          setEndDate(selectedDate);
          setDateRange(prev => ({
            ...prev,
            end: formattedDate
          }));
        }
      }
    }
  };

  // Handle end date change
  const onEndDateChange = (event: any, selectedDate?: Date) => {
    setShowEndDatePicker(false);
    if (selectedDate && !isNaN(selectedDate.getTime())) {
      // Store the selected date for future reference
      setEndDate(selectedDate);
      
      // Format the date for display
      const formattedDate = formatDate(selectedDate);
      
      // Make sure end date is not before start date
      if (dateRange.start) {
        const startDateObj = new Date(dateRange.start);
        if (selectedDate < startDateObj) {
          // If user selects an end date before start date,
          // update both dates (swap them)
          setStartDate(selectedDate);
          setEndDate(startDateObj);
          setDateRange({
            start: formattedDate,
            end: formatDate(startDateObj)
          });
        } else {
          // Normal case - end date is after start date
          setDateRange(prev => ({
            ...prev,
            end: formattedDate
          }));
        }
      } else {
        // If no start date is set, set both to the same date
        setStartDate(selectedDate);
        setDateRange({
          start: formattedDate,
          end: formattedDate
        });
      }
    }
  };

  // Quick export for today's attendance
  const handleTodayAttendanceExport = (format: 'pdf' | 'excel' = 'pdf') => {
    // Set attendance as report type
    setReportType('attendance');
    
    // Set date range to today
    const today = new Date();
    const formattedToday = formatDate(today);
    
    // Update the date range in state
    setStartDate(today);
    setEndDate(today);
    setDateRange({
      start: formattedToday,
      end: formattedToday
    });
    
    // Call the export function with today's date
    handleDownloadReport('Attendance', format);
  };

  // Quick export for this week's attendance
  const handleThisWeekAttendanceExport = (format: 'pdf' | 'excel' = 'pdf') => {
    // Set attendance as report type
    setReportType('attendance');
    
    // Calculate start of week (Sunday) and end of week (Saturday)
    const today = new Date();
    const dayOfWeek = today.getDay(); // 0 = Sunday, 6 = Saturday
    
    // Find the previous Sunday (or today if it's Sunday)
    const startOfWeek = new Date(today);
    startOfWeek.setDate(today.getDate() - dayOfWeek);
    
    // Update the date range in state
    setStartDate(startOfWeek);
    setEndDate(today);
    setDateRange({
      start: formatDate(startOfWeek),
      end: formatDate(today)
    });
    
    // Call the export function with the date range
    handleDownloadReport('Attendance', format);
  };

  // Quick export for this month's attendance
  const handleThisMonthAttendanceExport = (format: 'pdf' | 'excel' = 'pdf') => {
    // Set attendance as report type
    setReportType('attendance');
    
    // Calculate start of month and current date
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    
    // Update the date range in state
    setStartDate(startOfMonth);
    setEndDate(today);
    setDateRange({
      start: formatDate(startOfMonth),
      end: formatDate(today)
    });
    
    // Call the export function with the date range
    handleDownloadReport('Attendance', format);
  };

  const renderAnalyticsSection = () => {
    // For debugging, log the actual workers count
    console.log('Rendering analytics with workers count:', analyticsData.workersCount);
    
    return (
      <View style={styles.analyticsContainer}>
        {refreshing ? (
          <View style={styles.analyticsRefreshingContainer}>
            <ActivityIndicator size="small" color="#f97316" />
            <ThemedText style={styles.analyticsRefreshingText}>Updating data...</ThemedText>
          </View>
        ) : (
          <>
            <View style={styles.analyticsCard}>
              <ThemedView style={styles.analyticsItem}>
                <View style={styles.analyticsIconContainer}>
                  <MaterialIcons name="pie-chart" size={24} color="#f97316" />
                </View>
                <View style={{flex: 1}}>
                  <ThemedText style={styles.analyticsValue}>{analyticsData.totalTasksCount}</ThemedText>
                  <ThemedText style={styles.analyticsLabel}>Total Tasks</ThemedText>
                  <View style={styles.progressBarContainer}>
                    <View 
                      style={[
                        styles.progressBarFill, 
                        {width: `${analyticsData.totalTasksPercentage}%`}
                      ]} 
                    />
                    <ThemedText style={styles.progressBarText}>
                      {analyticsData.totalTasksPercentage}% complete
                    </ThemedText>
                  </View>
                </View>
              </ThemedView>
              <View style={styles.divider} />
              <ThemedView style={styles.analyticsItem}>
                <View style={styles.analyticsIconContainer}>
                  <MaterialIcons name="check-circle" size={24} color="#10b981" />
                </View>
                <View style={{flex: 1}}>
                  <ThemedText style={styles.analyticsValue}>{analyticsData.activeTasksCount}</ThemedText>
                  <ThemedText style={styles.analyticsLabel}>Active Tasks</ThemedText>
                  {analyticsData.activeTasksCount > 0 && analyticsData.totalTasksCount > 0 && (
                    <ThemedText style={styles.analyticsSubtext}>
                      {Math.round((analyticsData.activeTasksCount / analyticsData.totalTasksCount) * 100)}% of all tasks
                    </ThemedText>
                  )}
                </View>
              </ThemedView>
            </View>

            <View style={styles.analyticsCard}>
              <ThemedView style={styles.analyticsItem}>
                <View style={styles.analyticsIconContainer}>
                  <MaterialIcons name="people" size={24} color="#6366f1" />
                </View>
                <View style={{flex: 1}}>
                  <View style={styles.workerCountContainer}>
                    <ThemedText style={styles.analyticsValue}>
                      {analyticsData.activeWorkersCount}
                    </ThemedText>
                    <MaterialIcons name="person" size={16} color="#6366f1" style={styles.workerCountIcon} />
                  </View>
                  <ThemedText style={styles.analyticsLabel}>Active Workers</ThemedText>
                  <View style={styles.progressBarContainer}>
                    <View 
                      style={[
                        styles.progressBarFill, 
                        {
                          width: analyticsData.workersCount > 0 
                            ? `${(analyticsData.activeWorkersCount / analyticsData.workersCount) * 100}%` 
                            : '0%',
                          backgroundColor: '#6366f1'
                        }
                      ]} 
                    />
                  </View>
                  <ThemedText style={styles.analyticsSubtext}>
                    {analyticsData.activeWorkersCount} present of {analyticsData.workersCount} total
                  </ThemedText>
                  {members.length > 0 && (
                    <TouchableOpacity 
                      style={styles.viewTeamButton}
                      onPress={() => router.push(`/site/${site?.id}/attendance`)}
                    >
                      <ThemedText style={styles.viewTeamButtonText}>Manage Attendance</ThemedText>
                    </TouchableOpacity>
                  )}
                </View>
              </ThemedView>
              <View style={styles.divider} />
              <ThemedView style={styles.analyticsItem}>
                <View style={styles.analyticsIconContainer}>
                  <MaterialIcons name="trending-up" size={24} color="#f59e0b" />
                </View>
                <View style={{flex: 1}}>
                  <ThemedText style={styles.analyticsValue}>{analyticsData.tasksInProgressPercentage}%</ThemedText>
                  <ThemedText style={styles.analyticsLabel}>In Progress</ThemedText>
                  <View style={styles.progressBarContainer}>
                    <View 
                      style={[
                        styles.progressBarFill, 
                        {width: `${analyticsData.tasksInProgressPercentage}%`, backgroundColor: '#f59e0b'}
                      ]} 
                    />
                  </View>
                </View>
              </ThemedView>
            </View>
          </>
        )}
      </View>
    );
  };

  const renderTabsSection = () => (
    <View style={styles.tabsContainer}>
      <ThemedText style={styles.sectionTitle}>Site Management</ThemedText>
      
      <View style={styles.gridContainer}>
        <TouchableOpacity 
          style={styles.gridItem}
          onPress={() => router.push(`/site/${site?.id}/tasks`)}
        >
          <View style={[styles.gridIconContainer, { backgroundColor: 'rgba(249, 115, 22, 0.1)' }]}>
            <MaterialIcons name="assignment" size={24} color="#f97316" />
          </View>
          <ThemedText style={styles.gridItemText}>Task Management</ThemedText>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.gridItem}
          onPress={() => router.push(`/site/${site?.id}/attendance`)}
        >
          <View style={[styles.gridIconContainer, { backgroundColor: 'rgba(16, 185, 129, 0.1)' }]}>
            <MaterialIcons name="event-available" size={24} color="#10b981" />
          </View>
          <ThemedText style={styles.gridItemText}>Attendance</ThemedText>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.gridItem}
          onPress={() => router.push(`/site/${site?.id}/materials`)}
        >
          <View style={[styles.gridIconContainer, { backgroundColor: 'rgba(99, 102, 241, 0.1)' }]}>
            <MaterialIcons name="inventory" size={24} color="#6366f1" />
          </View>
          <ThemedText style={styles.gridItemText}>Materials</ThemedText>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.gridItem}
          onPress={() => router.push(`/site/${site?.id}/members`)}
        >
          <View style={[styles.gridIconContainer, { backgroundColor: 'rgba(245, 158, 11, 0.1)' }]}>
            <MaterialIcons name="people" size={24} color="#f59e0b" />
          </View>
          <ThemedText style={styles.gridItemText}>Team Members</ThemedText>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.gridItem}
          onPress={() => router.push(`/site/${site?.id}/transactions`)}
        >
          <View style={[styles.gridIconContainer, { backgroundColor: 'rgba(236, 72, 153, 0.1)' }]}>
            <MaterialIcons name="account-balance-wallet" size={24} color="#ec4899" />
          </View>
          <ThemedText style={styles.gridItemText}>Transactions</ThemedText>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.gridItem}
          onPress={() => router.push(`/site/${site?.id}/sub-contractors`)}
        >
          <View style={[styles.gridIconContainer, { backgroundColor: 'rgba(139, 92, 246, 0.1)' }]}>
            <MaterialIcons name="engineering" size={24} color="#8b5cf6" />
          </View>
          <ThemedText style={styles.gridItemText}>Sub-Contractors</ThemedText>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderReportSection = () => {
    // Skip rendering if user doesn't have report access
    if (!userHasReportAccess) {
      return null;
    }
    
    // Get the active category based on the current report type
    const activeCategory = reportType === 'tasks' ? taskCategory : 
                         reportType === 'attendance' ? attendanceCategory : '';
                         
    const availableCategories = getCategories();
    
    // Determine which color to use based on report type
    const getReportTypeColor = () => {
      switch(reportType) {
        case 'tasks':
          return '#f97316';
        case 'attendance':
          return '#10b981';
        case 'materials':
          return '#6366f1';
        default:
          return '#64748b';
      }
    };
    
    const reportColor = getReportTypeColor();
    
    return (
    <View style={styles.reportSection}>
      <ThemedText style={styles.sectionTitle}>Reports</ThemedText>
      
      <View style={styles.reportFilters}>
        <View style={styles.reportTypeTabs}>
          <TouchableOpacity 
            style={[
              styles.reportTypeTab, 
              reportType === 'tasks' && { borderColor: '#f97316', backgroundColor: 'rgba(249, 115, 22, 0.05)', borderWidth: 2 }
            ]}
            onPress={() => setReportType('tasks')}
          >
            <MaterialIcons 
              name="assignment" 
              size={16} 
              color={reportType === 'tasks' ? "#f97316" : "#64748b"} 
            />
            <ThemedText style={[
              styles.reportTypeText,
              reportType === 'tasks' && { color: '#f97316', fontWeight: 'bold' }
            ]}>
              Tasks
            </ThemedText>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[
              styles.reportTypeTab, 
              reportType === 'attendance' && { borderColor: '#10b981', backgroundColor: 'rgba(16, 185, 129, 0.05)', borderWidth: 2 }
            ]}
            onPress={() => setReportType('attendance')}
          >
            <MaterialIcons 
              name="event-available" 
              size={16} 
              color={reportType === 'attendance' ? "#10b981" : "#64748b"} 
            />
            <ThemedText style={[
              styles.reportTypeText,
              reportType === 'attendance' && { color: '#10b981', fontWeight: 'bold' }
            ]}>
              Attendance
            </ThemedText>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[
              styles.reportTypeTab, 
              reportType === 'materials' && { borderColor: '#6366f1', backgroundColor: 'rgba(99, 102, 241, 0.05)', borderWidth: 2 }
            ]}
            onPress={() => setReportType('materials')}
          >
            <MaterialIcons 
              name="inventory" 
              size={16} 
              color={reportType === 'materials' ? "#6366f1" : "#64748b"} 
            />
            <ThemedText style={[
              styles.reportTypeText,
              reportType === 'materials' && { color: '#6366f1', fontWeight: 'bold' }
            ]}>
              Materials
            </ThemedText>
          </TouchableOpacity>
        </View>
        
        <View style={styles.filterGroup}>
          <View style={styles.filterLabelContainer}>
            <ThemedText style={styles.filterLabel}>Date Range</ThemedText>
            {(dateRange.start || dateRange.end) && (
              <TouchableOpacity 
                style={styles.clearButton}
                onPress={() => setDateRange({ start: '', end: '' })}
              >
                <MaterialIcons name="clear" size={12} color="#64748b" />
                <ThemedText style={styles.clearButtonText}>Clear</ThemedText>
              </TouchableOpacity>
            )}
          </View>
          <View style={styles.dateRangeContainer}>
            <TouchableOpacity
              style={[
                styles.dateInput, 
                dateRange.start ? styles.dateInputSelected : {},
                dateRange.start && dateRange.end ? styles.dateInputRangeSelected : {}
              ]}
              onPress={() => setShowStartDatePicker(true)}
            >
              <ThemedText style={[styles.dateText, dateRange.start ? styles.dateTextSelected : {}]}>
                {dateRange.start || 'Start Date'}
              </ThemedText>
              <MaterialIcons 
                name="calendar-today" 
                size={18} 
                color={dateRange.start ? reportColor : "#64748b"} 
              />
            </TouchableOpacity>
            
            {showStartDatePicker && (
              <DateTimePicker
                value={startDate}
                mode="date"
                display="default"
                onChange={onStartDateChange}
              />
            )}
            
            <TouchableOpacity
              style={[
                styles.dateInput, 
                dateRange.end ? styles.dateInputSelected : {},
                dateRange.start && dateRange.end ? styles.dateInputRangeSelected : {}
              ]}
              onPress={() => setShowEndDatePicker(true)}
            >
              <ThemedText style={[styles.dateText, dateRange.end ? styles.dateTextSelected : {}]}>
                {dateRange.end || 'End Date'}
              </ThemedText>
              <MaterialIcons 
                name="calendar-today" 
                size={18} 
                color={dateRange.end ? reportColor : "#64748b"} 
              />
            </TouchableOpacity>
            
            {showEndDatePicker && (
              <DateTimePicker
                value={endDate}
                mode="date"
                display="default"
                onChange={onEndDateChange}
              />
            )}
          </View>
        </View>
        
        <View style={styles.filterGroup}>
          <View style={styles.filterLabelContainer}>
            <ThemedText style={styles.filterLabel}>
              {reportType === 'attendance' ? 'Category Filter' : `${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Category`}
            </ThemedText>
            {activeCategory && (
              <TouchableOpacity 
                style={styles.clearButton}
                onPress={() => {
                  if (reportType === 'tasks') {
                    setTaskCategory('');
                  } else if (reportType === 'attendance') {
                    setAttendanceCategory('');
                  }
                }}
              >
                <MaterialIcons name="clear" size={12} color="#64748b" />
                <ThemedText style={styles.clearButtonText}>Clear</ThemedText>
              </TouchableOpacity>
            )}
          </View>
          <TouchableOpacity
            style={[
              styles.categoryDropdown,
              activeCategory ? {
                borderColor: reportColor,
                backgroundColor: reportType === 'tasks' 
                  ? 'rgba(249, 115, 22, 0.05)' 
                  : reportType === 'attendance' 
                    ? 'rgba(16, 185, 129, 0.05)'
                    : 'rgba(99, 102, 241, 0.05)',
                borderWidth: activeCategory ? 2 : 1
              } : {}
            ]}
            onPress={() => setShowCategoryPicker(!showCategoryPicker)}
            disabled={isGeneratingReport}
          >
            <ThemedText style={[
              styles.categoryText,
              activeCategory ? { color: reportColor, fontWeight: '600' } : {}
            ]}>
              {activeCategory || (reportType === 'attendance' ? 'Select Category Filter' : `Select ${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Category`)}
            </ThemedText>
            <MaterialIcons 
              name={showCategoryPicker ? "keyboard-arrow-up" : "keyboard-arrow-down"} 
              size={20} 
              color={activeCategory ? reportColor : "#64748b"} 
            />
          </TouchableOpacity>
          
          {showCategoryPicker && (
            <View style={styles.categoryPickerContainer}>
              {availableCategories.map((item, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.categoryItem,
                    activeCategory === item && {
                      backgroundColor: reportType === 'tasks' 
                        ? 'rgba(249, 115, 22, 0.1)' 
                        : reportType === 'attendance' 
                          ? 'rgba(16, 185, 129, 0.1)'
                          : 'rgba(99, 102, 241, 0.1)'
                    }
                  ]}
                  onPress={() => {
                    if (reportType === 'tasks') {
                      setTaskCategory(item);
                    } else if (reportType === 'attendance') {
                      setAttendanceCategory(item);
                    }
                    setShowCategoryPicker(false);
                  }}
                >
                  <ThemedText 
                    style={[
                      styles.categoryItemText,
                      activeCategory === item && { color: reportColor, fontWeight: '600' }
                    ]}
                  >
                    {item}
                  </ThemedText>
                  {activeCategory === item && (
                    <MaterialIcons name="check" size={16} color={reportColor} />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
      </View>
      
      <View style={styles.reportButtonsContainer}>
        {reportType === 'tasks' && (
          <TouchableOpacity 
            style={[
              styles.reportButton, 
              {backgroundColor: '#f97316'},
              isGeneratingReport && styles.reportButtonDisabled
            ]}
            onPress={() => handleDownloadReport('Tasks')}
            disabled={isGeneratingReport}
          >
            {isGeneratingReport && reportType === 'tasks' ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <MaterialIcons name="picture-as-pdf" size={20} color="#fff" />
            )}
            <ThemedText style={styles.reportButtonText}>
              {isGeneratingReport && reportType === 'tasks' ? 'Generating...' : 'Task Report PDF'}
            </ThemedText>
          </TouchableOpacity>
        )}
        
        {reportType === 'attendance' && (
          <>
            <TouchableOpacity 
              style={[
                styles.reportButton, 
                {backgroundColor: '#10b981'},
                isGeneratingReport && styles.reportButtonDisabled
              ]}
              onPress={() => handleDownloadReport('Attendance', 'pdf')}
              disabled={isGeneratingReport}
            >
              {isGeneratingReport && reportType === 'attendance' ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <MaterialIcons name="picture-as-pdf" size={20} color="#fff" />
              )}
              <ThemedText style={styles.reportButtonText}>
                {isGeneratingReport && reportType === 'attendance' ? 'Generating...' : 'Attendance PDF'}  
              </ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[
                styles.reportButton, 
                {backgroundColor: '#22c55e'},
                isGeneratingReport && styles.reportButtonDisabled
              ]}
              onPress={() => handleDownloadReport('Attendance', 'excel')}
              disabled={isGeneratingReport}
            >
              {isGeneratingReport && reportType === 'attendance' ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <MaterialIcons name="table-chart" size={20} color="#fff" />
              )}
              <ThemedText style={styles.reportButtonText}>
                {isGeneratingReport && reportType === 'attendance' ? 'Generating...' : 'Attendance Excel'}  
              </ThemedText>
            </TouchableOpacity>
          </>
        )}
        
        {reportType === 'materials' && (
          <TouchableOpacity 
            style={[
              styles.reportButton, 
              {backgroundColor: '#6366f1'},
              isGeneratingReport && styles.reportButtonDisabled
            ]}
            onPress={() => handleDownloadReport('Materials')}
            disabled={isGeneratingReport}
          >
            {isGeneratingReport && reportType === 'materials' ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <MaterialIcons name="inventory" size={20} color="#fff" />
            )}
            <ThemedText style={styles.reportButtonText}>
              {isGeneratingReport && reportType === 'materials' ? 'Generating...' : 'Materials Reports'}
            </ThemedText>
          </TouchableOpacity>
        )}

        {/* Quick attendance export buttons - only show for attendance section */}
        {reportType === 'attendance' && (
          <View style={styles.quickExportContainer}>
            <ThemedText style={styles.quickExportTitle}>Quick Export:</ThemedText>
            <View style={styles.quickExportButtonsRow}>
              <TouchableOpacity 
                style={[
                  styles.quickExportButton, 
                  {backgroundColor: '#059669'}
                ]}
                onPress={() => handleTodayAttendanceExport('pdf')}
                disabled={isGeneratingReport}
              >
                <MaterialIcons name="today" size={16} color="#fff" />
                <ThemedText style={styles.quickExportButtonText}>Today's PDF</ThemedText>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[
                  styles.quickExportButton, 
                  {backgroundColor: '#0ea5e9'}
                ]}
                onPress={() => handleTodayAttendanceExport('excel')}
                disabled={isGeneratingReport}
              >
                <MaterialIcons name="today" size={16} color="#fff" />
                <ThemedText style={styles.quickExportButtonText}>Today's Excel</ThemedText>
              </TouchableOpacity>
            </View>
            
            <View style={styles.quickExportButtonsRow}>
              <TouchableOpacity 
                style={[
                  styles.quickExportButton, 
                  {backgroundColor: '#8b5cf6'}
                ]}
                onPress={() => handleThisWeekAttendanceExport('pdf')}
                disabled={isGeneratingReport}
              >
                <MaterialIcons name="date-range" size={16} color="#fff" />
                <ThemedText style={styles.quickExportButtonText}>This Week PDF</ThemedText>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[
                  styles.quickExportButton, 
                  {backgroundColor: '#ec4899'}
                ]}
                onPress={() => handleThisWeekAttendanceExport('excel')}
                disabled={isGeneratingReport}
              >
                <MaterialIcons name="date-range" size={16} color="#fff" />
                <ThemedText style={styles.quickExportButtonText}>This Week Excel</ThemedText>
              </TouchableOpacity>
            </View>
            
            <View style={styles.quickExportButtonsRow}>
              <TouchableOpacity 
                style={[
                  styles.quickExportButton, 
                  {backgroundColor: '#f59e0b'}
                ]}
                onPress={() => handleThisMonthAttendanceExport('pdf')}
                disabled={isGeneratingReport}
              >
                <MaterialIcons name="calendar-today" size={16} color="#fff" />
                <ThemedText style={styles.quickExportButtonText}>This Month PDF</ThemedText>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[
                  styles.quickExportButton, 
                  {backgroundColor: '#f43f5e'}
                ]}
                onPress={() => handleThisMonthAttendanceExport('excel')}
                disabled={isGeneratingReport}
              >
                <MaterialIcons name="calendar-today" size={16} color="#fff" />
                <ThemedText style={styles.quickExportButtonText}>This Month Excel</ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>
      
      {/* Display active filters */}
      {(dateRange.start || dateRange.end || activeCategory) && (
        <View style={styles.activeFiltersContainer}>
          <ThemedText style={styles.activeFiltersTitle}>Active Filters:</ThemedText>
          {dateRange.start && (
            <View style={styles.activeFilterItem}>
              <MaterialIcons name="date-range" size={14} color="#64748b" />
              <ThemedText style={styles.activeFilterText}>
                From: {dateRange.start}
              </ThemedText>
            </View>
          )}
          {dateRange.end && (
            <View style={styles.activeFilterItem}>
              <MaterialIcons name="date-range" size={14} color="#64748b" />
              <ThemedText style={styles.activeFilterText}>
                To: {dateRange.end}
              </ThemedText>
            </View>
          )}
          {activeCategory && activeCategory !== 'All Categories' && activeCategory !== 'All Statuses' && (
            <View style={styles.activeFilterItem}>
              <MaterialIcons name="filter-list" size={14} color="#64748b" />
              <ThemedText style={styles.activeFilterText}>
                {['Present', 'Absent', 'Half-day', 'Late', 'Leave'].includes(activeCategory) 
                  ? `Status: ${activeCategory}`
                  : `${reportType === 'attendance' ? 'Labor Category' : reportType.charAt(0).toUpperCase() + reportType.slice(1) + ' Category'}: ${activeCategory}`}
              </ThemedText>
            </View>
          )}
        </View>
      )}
    </View>
  );
  };
  
  // For debugging purposes, add a console log to check the analytics data values
  useEffect(() => {
    console.log('Analytics data:', analyticsData);
  }, [analyticsData]);
  
  // Load site members if they're not already loaded
  useEffect(() => {
    if (site && members.length === 0 && !loading) {
      const loadSiteMembers = async () => {
        try {
          const { data: membersData, error: membersError } = await supabase
            .from('site_members')
            .select(`
              id,
              user_id,
              role,
              profile:profiles(full_name, profile_image_url)
            `)
            .eq('site_id', site.id);
          
          if (!membersError && membersData && membersData.length > 0) {
            // Type assertion to ensure proper format
            const typedMembersData = membersData.map(member => ({
              ...member,
              profile: {
                full_name: Array.isArray(member.profile) && member.profile[0]?.full_name || '',
                profile_image_url: Array.isArray(member.profile) && member.profile[0]?.profile_image_url || null
              }
            })) as SiteMember[];
            
            setMembers(typedMembersData);
            console.log('Loaded site members separately:', typedMembersData.length);
          }
        } catch (error) {
          console.error('Error loading site members:', error);
        }
      };
      
      loadSiteMembers();
    }
  }, [site, members.length, loading]);
  
  // Fetch labor categories when attendance report is selected
  useEffect(() => {
    if (reportType === 'attendance' && site?.id) {
      const fetchLaborCategories = async () => {
        try {
          // Debug current attendance structure with new view
          const { data: attendanceData, error: attendanceError } = await supabase
            .from('attendance_with_labor')
            .select('*')
            .eq('site_id', site.id)
            .limit(1);
            
          if (!attendanceError && attendanceData && attendanceData.length > 0) {
            console.log('Sample attendance record:', JSON.stringify(attendanceData[0], null, 2));
          } else if (attendanceError) {
            console.error('Error fetching sample attendance:', attendanceError);
          }
          
          // Fetch labor categories from the dedicated table
          const { data, error } = await supabase
            .from('labor_categories')
            .select('id, name')
            .order('name');
          
          if (!error && data) {
            // Extract unique categories and filter out any null or empty values
            const categories = data
              .map(item => item.name)
              .filter(category => category && category.trim() !== '');
              
            console.log('Labor categories found:', categories);
            setLaborCategories(categories);
          } else if (error) {
            console.error('Error fetching labor categories:', error);
          }
        } catch (error) {
          console.error('Error in fetchLaborCategories:', error);
        }
      };
      
      fetchLaborCategories();
    }
  }, [reportType, site?.id]);
  
  // In useEffect or any function where you set the userRole, cast it to the UserRole type:
  useEffect(() => {
    const fetchUserRole = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser();
        
        if (user) {
          const { data: memberData, error } = await supabase
            .from('site_members')
            .select('role')
            .eq('site_id', id)
            .eq('user_id', user.id)
            .single();
            
          if (memberData) {
            setUserRole(memberData.role as UserRole);
          }
        }
      } catch (error) {
        console.error('Error fetching user role:', error);
      }
    };
    
    fetchUserRole();
  }, [id]);
  
  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#f97316" />
      </ThemedView>
    );
  }
  
  if (!site) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText>Site not found</ThemedText>
      </ThemedView>
    );
  }
  
  const defaultImage = 'https://images.unsplash.com/photo-1541888946425-d81bb19240f5?q=80&w=2070&auto=format&fit=crop';
  
  return (
    <ThemedView style={styles.container}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      
      <Stack.Screen
        options={{
          headerTitle: site?.name || 'Loading...',
          headerRight: isOwner ? () => (
            <TouchableOpacity 
              onPress={toggleSiteStatus}
              style={styles.statusToggleButton}
            >
              <ThemedText style={styles.statusToggleText}>
                {site?.status === 'active' ? 'Mark Complete' : 'Reactivate'}
              </ThemedText>
            </TouchableOpacity>
          ) : undefined
        }}
      />
      
      <ScrollView 
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#f97316']}
            tintColor={colorScheme === 'dark' ? '#f97316' : '#f97316'}
          />
        }
      >
        <View style={styles.heroContainer}>
          <Image
            source={{ uri: site.site_image_url || defaultImage }}
            style={styles.siteImage}
            resizeMode="cover"
          />
          <View style={styles.heroOverlay}>
            <View style={styles.heroContent}>
              <ThemedText style={styles.siteName}>{site.name}</ThemedText>
              <ThemedText style={styles.projectSubheading}>Project Dashboard</ThemedText>
              
              <View style={[
                styles.statusBadge,
                { backgroundColor: site.status === 'active' ? '#dcfce7' : '#f1f5f9' }
              ]}>
                <ThemedText style={[
                  styles.statusText,
                  { color: site.status === 'active' ? '#166534' : '#475569' }
                ]}>
                  {site.status === 'active' ? 'Active' : 'Completed'}
                </ThemedText>
              </View>
            </View>
          </View>
        </View>
        
        <View style={styles.contentContainer}>
          {renderAnalyticsSection()}
          {renderTabsSection()}  
          {userHasReportAccess && renderReportSection()}
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  heroContainer: {
    position: 'relative',
    height: 200,
  },
  siteImage: {
    width: '100%',
    height: 200,
  },
  heroOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'flex-end',
  },
  heroContent: {
    padding: 16,
  },
  contentContainer: {
    padding: 16,
  },
  siteName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 2,
  },
  projectSubheading: {
    fontSize: 16,
    color: '#f1f5f9',
    marginBottom: 12,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  organizationName: {
    fontSize: 16,
    marginBottom: 24,
    opacity: 0.7,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  analyticsContainer: {
    flexDirection: 'row',
    marginBottom: 24,
    gap: 12,
  },
  analyticsCard: {
    flex: 1,
    borderRadius: 12,
    backgroundColor: 'rgba(100, 100, 100, 0.06)',
    overflow: 'hidden',
  },
  analyticsItem: {
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  analyticsIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: 'rgba(100, 100, 100, 0.08)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  analyticsValue: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  analyticsLabel: {
    fontSize: 13,
    opacity: 0.7,
  },
  analyticsRefreshingContainer: {
    flex: 1,
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(100, 100, 100, 0.06)',
    borderRadius: 12,
  },
  analyticsRefreshingText: {
    marginTop: 8,
    fontSize: 14,
    opacity: 0.7,
  },
  divider: {
    height: 1,
    backgroundColor: 'rgba(100, 100, 100, 0.1)',
  },
  tabsContainer: {
    marginBottom: 24,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  gridItem: {
    width: '48%',
    minHeight: 120,
    padding: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(100, 100, 100, 0.06)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
  },
  gridIconContainer: {
    width: '100%',
    height: 40,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  gridItemText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#64748b',
    textAlign: 'center',
    marginTop: 8,
  },
  reportSection: {
    marginBottom: 24,
  },
  reportFilters: {
    marginBottom: 16,
  },
  reportTypeTabs: {
    flexDirection: 'row',
    gap: 10,
    marginBottom: 12,
  },
  reportTypeTab: {
    flex: 1,
    padding: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    backgroundColor: 'rgba(100, 100, 100, 0.06)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  reportTypeTabActive: {
    borderWidth: 2,
  },
  reportTypeText: {
    fontSize: 14,
    color: '#64748b',
    marginTop: 4,
  },
  reportTypeTextActive: {
    fontWeight: 'bold',
  },
  reportTypeTasksActive: {
    borderColor: '#f97316',
    backgroundColor: 'rgba(249, 115, 22, 0.05)',
    borderWidth: 2,
  },
  reportTypeTasksText: {
    color: '#f97316',
    fontWeight: 'bold',
  },
  reportTypeAttendanceActive: {
    borderColor: '#10b981',
    backgroundColor: 'rgba(16, 185, 129, 0.05)',
    borderWidth: 2,
  },
  reportTypeAttendanceText: {
    color: '#10b981',
    fontWeight: 'bold',
  },
  reportTypeMaterialsActive: {
    borderColor: '#6366f1',
    backgroundColor: 'rgba(99, 102, 241, 0.05)',
    borderWidth: 2,
  },
  reportTypeMaterialsText: {
    color: '#6366f1',
    fontWeight: 'bold',
  },
  filterGroup: {
    marginBottom: 12,
  },
  filterLabelContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 6,
  },
  dateRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: -4,
  },
  dateInput: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    padding: 12,
    marginHorizontal: 4,
  },
  dateText: {
    fontSize: 14,
    color: '#0f172a',
  },
  categoryDropdown: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    padding: 12,
  },
  categoryText: {
    fontSize: 14,
    color: '#0f172a',
    flex: 1,
  },
  categoryPickerContainer: {
    maxHeight: 200,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    marginTop: 4,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  categoryItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f1f5f9',
  },
  categoryItemText: {
    fontSize: 14,
    color: '#0f172a',
  },
  categoryItemTextSelected: {
    fontWeight: 'bold',
    color: '#f97316',
  },
  reportButtonsContainer: {
    gap: 10,
  },
  reportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#f97316',
    gap: 8,
    marginBottom: 8,
  },
  reportButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  reportButtonDisabled: {
    opacity: 0.6,
  },
  statusToggleButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: '#f97316',
  },
  statusToggleText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 12,
  },
  progressBarContainer: {
    height: 6,
    width: '100%',
    backgroundColor: 'rgba(100, 100, 100, 0.1)',
    borderRadius: 3,
    marginTop: 8,
    position: 'relative',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: '#f97316',
    borderRadius: 3,
    position: 'absolute',
    left: 0,
    top: 0,
  },
  progressBarText: {
    fontSize: 10,
    marginTop: 4,
    opacity: 0.7,
  },
  analyticsSubtext: {
    fontSize: 10,
    marginTop: 4,
    opacity: 0.7,
  },
  workerCountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  workerCountIcon: {
    marginLeft: 4,
    marginTop: 2,
  },
  viewTeamButton: {
    marginTop: 6,
    paddingVertical: 4,
    paddingHorizontal: 8,
    backgroundColor: 'rgba(99, 102, 241, 0.1)',
    borderRadius: 4,
    alignSelf: 'flex-start',
  },
  viewTeamButtonText: {
    fontSize: 10,
    color: '#6366f1',
    fontWeight: '600',
  },
  // Date picker styles
  dateInputSelected: {
    borderColor: '#10b981',
    backgroundColor: '#f0fdf4',
  },
  dateInputRangeSelected: {
    borderWidth: 2,
  },
  dateTextSelected: {
    color: '#10b981',
    fontWeight: '600',
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 4,
    borderRadius: 4,
    backgroundColor: 'rgba(100, 100, 100, 0.1)',
  },
  clearButtonText: {
    fontSize: 12,
    color: '#64748b',
    marginLeft: 4,
  },
  activeFiltersContainer: {
    marginTop: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
  },
  activeFiltersTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  activeFilterItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  activeFilterText: {
    fontSize: 12,
    color: '#64748b',
    marginLeft: 4,
  },
  categoryDropdownSelected: {
    borderColor: '#10b981',
    backgroundColor: '#f0fdf4',
    borderWidth: 2,
  },
  categoryTextSelected: {
    color: '#10b981',
    fontWeight: '600',
  },
  categoryItemSelected: {
    backgroundColor: 'rgba(16, 185, 129, 0.1)',
  },
  quickExportContainer: {
    marginTop: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
  },
  quickExportTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  quickExportButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
    gap: 8,
  },
  quickExportButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#f97316',
    gap: 8,
  },
  quickExportButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 12,
  },
}); 