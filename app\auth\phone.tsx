import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '@react-navigation/native';
import { router, useNavigation } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { ActivityIndicator, Alert, FlatList, Modal, SafeAreaView, StyleSheet, TextInput, TouchableOpacity, View, useColorScheme } from 'react-native';
import { ThemedText } from '../../components/ThemedText';
import { ThemedView } from '../../components/ThemedView';
import { supabase } from '../../lib/supabase';

// Country data with codes
const countries = [
  { name: 'India', code: '+91', flag: '🇮🇳' },
  { name: 'United Arab Emirates', code: '+971', flag: '🇦🇪' },
  { name: 'Afghanistan', code: '+93', flag: '🇦🇫' },
  { name: 'Albania', code: '+355', flag: '🇦🇱' },
  { name: 'Algeria', code: '+213', flag: '🇩🇿' },
  { name: 'Argentina', code: '+54', flag: '🇦🇷' },
  { name: 'Australia', code: '+61', flag: '🇦🇺' },
  { name: 'Bangladesh', code: '+880', flag: '🇧🇩' },
  { name: 'Belgium', code: '+32', flag: '🇧🇪' },
  { name: 'Brazil', code: '+55', flag: '🇧🇷' },
  { name: 'Canada', code: '+1', flag: '🇨🇦' },
  { name: 'China', code: '+86', flag: '🇨🇳' },
  { name: 'Egypt', code: '+20', flag: '🇪🇬' },
  { name: 'France', code: '+33', flag: '🇫🇷' },
  { name: 'Germany', code: '+49', flag: '🇩🇪' },
  { name: 'Indonesia', code: '+62', flag: '🇮🇩' },
  { name: 'Italy', code: '+39', flag: '🇮🇹' },
  { name: 'Japan', code: '+81', flag: '🇯🇵' },
  { name: 'Malaysia', code: '+60', flag: '🇲🇾' },
  { name: 'Mexico', code: '+52', flag: '🇲🇽' },
  { name: 'Netherlands', code: '+31', flag: '🇳🇱' },
  { name: 'Pakistan', code: '+92', flag: '🇵🇰' },
  { name: 'Russia', code: '+7', flag: '🇷🇺' },
  { name: 'Saudi Arabia', code: '+966', flag: '🇸🇦' },
  { name: 'Singapore', code: '+65', flag: '🇸🇬' },
  { name: 'South Africa', code: '+27', flag: '🇿🇦' },
  { name: 'South Korea', code: '+82', flag: '🇰🇷' },
  { name: 'Spain', code: '+34', flag: '🇪🇸' },
  { name: 'Sweden', code: '+46', flag: '🇸🇪' },
  { name: 'Switzerland', code: '+41', flag: '🇨🇭' },
  { name: 'Thailand', code: '+66', flag: '🇹🇭' },
  { name: 'Turkey', code: '+90', flag: '🇹🇷' },
  { name: 'United Kingdom', code: '+44', flag: '🇬🇧' },
  { name: 'United States', code: '+1', flag: '🇺🇸' },
  { name: 'Vietnam', code: '+84', flag: '🇻🇳' },
];

type FormData = {
  phone: string;
};

export default function PhoneScreen() {
  const navigation = useNavigation();
  const colorScheme = useColorScheme();
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [initialRender, setInitialRender] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState(countries.find(country => country.name === 'India'));
  
  // Use theme-based colors
  const backgroundColor = colorScheme === 'dark' ? theme.colors.background : '#ffffff';
  const textColor = colorScheme === 'dark' ? theme.colors.text : '#000000';
  const inputBgColor = colorScheme === 'dark' ? theme.colors.card : '#F2F2F2';
  const placeholderColor = colorScheme === 'dark' ? theme.colors.border : '#A0A0A0';
  
  useEffect(() => {
    // Ensure the header is always hidden for this screen
    navigation.setOptions({
      headerShown: false,
    });
    
    // Add focus listener to ensure header stays hidden when navigating back to this screen
    const unsubscribe = navigation.addListener('focus', () => {
      navigation.setOptions({
        headerShown: false,
      });
    });
    
    // Set initial render to false after a short delay to ensure screen loads properly
    const timer = setTimeout(() => {
      setInitialRender(false);
    }, 300);
    
    return () => {
      unsubscribe();
      clearTimeout(timer);
    };
  }, [navigation]);
  
  const { control, handleSubmit, formState: { errors } } = useForm<FormData>({
    defaultValues: {
      phone: ''
    }
  });

  const onSubmit = async (data: FormData) => {
    try {
      setLoading(true);
      
      // Format phone number to E.164 format with country code
      let phoneNumber = data.phone;
      if (phoneNumber.startsWith('+')) {
        phoneNumber = phoneNumber.substring(1);
      }
      
      // Add the selected country code
      phoneNumber = `${selectedCountry?.code}${phoneNumber}`;
      
      const { error } = await supabase.auth.signInWithOtp({
        phone: phoneNumber,
      });

      if (error) throw error;
      
      // Navigate to OTP screen
      router.push({
        pathname: '/auth/verify',
        params: { phone: phoneNumber }
      } as any);
      
    } catch (error: any) {
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  const renderCountryItem = ({ item }: { item: typeof countries[0] }) => (
    <TouchableOpacity
      style={[styles.countryItem, { 
        borderBottomColor: colorScheme === 'dark' ? theme.colors.border : '#EEEEEE', 
        backgroundColor: colorScheme === 'dark' ? theme.colors.card : 'white' 
      }]}
      onPress={() => {
        setSelectedCountry(item);
        setModalVisible(false);
      }}
    >
      <ThemedText style={styles.countryFlag}>{item.flag}</ThemedText>
      <ThemedText style={styles.countryName}>{item.name}</ThemedText>
      <ThemedText style={styles.countryCode}>{item.code}</ThemedText>
    </TouchableOpacity>
  );

  // Show loading indicator during initial render
  if (initialRender) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor }}>
        <ActivityIndicator size="large" color="#f97316" />
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor }}>
      <ThemedView style={[styles.container, { backgroundColor }]}>
        <View style={styles.content}>
          <ThemedText style={styles.title}>Login with Phone</ThemedText>
          <ThemedText style={styles.subtitle}>
            We'll send you a verification code to login
          </ThemedText>

          <View style={styles.form}>
            <ThemedText style={styles.label}>Phone Number</ThemedText>
            
            <View style={styles.phoneInputContainer}>
              <TouchableOpacity 
                style={[styles.countrySelector, { backgroundColor: inputBgColor }]}
                onPress={() => setModalVisible(true)}
              >
                <ThemedText style={styles.countrySelectorText}>
                  {selectedCountry?.flag} {selectedCountry?.code}
                </ThemedText>
                <Ionicons 
                  name="chevron-down" 
                  size={16} 
                  color={colorScheme === 'dark' ? theme.colors.text : '#333'}
                />
              </TouchableOpacity>
              
              <Controller
                control={control}
                rules={{
                  required: 'Phone number is required',
                  pattern: {
                    value: /^[0-9]+$/,
                    message: 'Please enter a valid phone number'
                  }
                }}
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    style={[styles.phoneInput, { 
                      backgroundColor: inputBgColor, 
                      color: textColor
                    }]}
                    placeholder="Phone number"
                    placeholderTextColor={placeholderColor}
                    keyboardType="phone-pad"
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value}
                    autoFocus
                  />
                )}
                name="phone"
              />
            </View>
            
            {errors.phone && (
              <ThemedText style={[styles.errorText, { color: 'red' }]}>{errors.phone.message}</ThemedText>
            )}

            <TouchableOpacity
              style={[styles.button, loading && styles.buttonDisabled, { backgroundColor: '#f97316' }]}
              onPress={handleSubmit(onSubmit)}
              disabled={loading}
            >
              <ThemedText style={[styles.buttonText, { color: 'white' }]}>
                {loading ? 'Sending...' : 'Continue'}
              </ThemedText>
            </TouchableOpacity>
          </View>
        </View>

        {/* Country Selector Modal */}
        <Modal
          animationType="slide"
          transparent={true}
          visible={modalVisible}
          onRequestClose={() => setModalVisible(false)}
        >
          <SafeAreaView style={[styles.modalContainer, { backgroundColor: backgroundColor }]}>
            <View style={[styles.modalHeader, { borderBottomColor: colorScheme === 'dark' ? theme.colors.border : '#EEEEEE' }]}>
              <ThemedText style={styles.modalTitle}>Select Country</ThemedText>
              <TouchableOpacity onPress={() => setModalVisible(false)}>
                <Ionicons 
                  name="close" 
                  size={24} 
                  color={colorScheme === 'dark' ? theme.colors.text : '#333'} 
                />
              </TouchableOpacity>
            </View>
            
            <FlatList
              data={countries}
              renderItem={renderCountryItem}
              keyExtractor={(item) => item.name}
              style={styles.countryList}
            />
          </SafeAreaView>
        </Modal>
      </ThemedView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    maxWidth: 400,
    width: '100%',
    alignSelf: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 30,
    opacity: 0.7,
  },
  form: {
    width: '100%',
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  countrySelector: {
    borderRadius: 8,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
  },
  countrySelectorText: {
    fontSize: 16,
    marginRight: 5,
  },
  phoneInput: {
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    flex: 1,
  },
  button: {
    backgroundColor: '#f97316',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 20,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  errorText: {
    marginTop: -15,
    marginBottom: 10,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  countryList: {
    flex: 1,
  },
  countryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
  },
  countryFlag: {
    fontSize: 20,
    marginRight: 10,
  },
  countryName: {
    flex: 1,
    fontSize: 16,
  },
  countryCode: {
    fontSize: 16,
  },
}); 