import { useState, useEffect, useCallback } from 'react';
import { Platform, AppState, AppStateStatus } from 'react-native';
import { playStoreUpdateManager, PlayStoreUpdateResult, UpdateInfo } from '@/lib/playStoreUpdates';

interface UsePlayStoreUpdatesReturn {
  updateAvailable: boolean;
  updateInfo: UpdateInfo | null;
  isChecking: boolean;
  checkForUpdates: (forceCheck?: boolean) => Promise<void>;
  startUpdateFlow: (updateType?: 'immediate' | 'flexible') => Promise<void>;
  dismissUpdate: () => void;
}

export function usePlayStoreUpdates(autoCheck: boolean = true): UsePlayStoreUpdatesReturn {
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null);
  const [isChecking, setIsChecking] = useState(false);

  const checkForUpdates = useCallback(async (forceCheck: boolean = false) => {
    if (Platform.OS !== 'android') return;
    
    setIsChecking(true);
    try {
      const result: PlayStoreUpdateResult = await playStoreUpdateManager.checkForUpdates(forceCheck);
      setUpdateAvailable(result.updateAvailable);
      setUpdateInfo(result.updateInfo || null);
    } catch (error) {
      console.error('Error checking for updates:', error);
      setUpdateAvailable(false);
      setUpdateInfo(null);
    } finally {
      setIsChecking(false);
    }
  }, []);

  const startUpdateFlow = useCallback(async (updateType: 'immediate' | 'flexible' = 'flexible') => {
    try {
      await playStoreUpdateManager.startUpdateFlow(updateType);
    } catch (error) {
      console.error('Error starting update flow:', error);
    }
  }, []);

  const dismissUpdate = useCallback(() => {
    setUpdateAvailable(false);
    setUpdateInfo(null);
  }, []);

  // Auto-check for updates on mount
  useEffect(() => {
    if (autoCheck && Platform.OS === 'android') {
      checkForUpdates();
    }
  }, [autoCheck, checkForUpdates]);

  // Check for updates when app becomes active
  useEffect(() => {
    if (!autoCheck || Platform.OS !== 'android') return;

    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        // Check for updates when app becomes active
        checkForUpdates();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [autoCheck, checkForUpdates]);

  return {
    updateAvailable,
    updateInfo,
    isChecking,
    checkForUpdates,
    startUpdateFlow,
    dismissUpdate,
  };
}

// Hook for automatic update checking and prompting
export function useAutoPlayStoreUpdates() {
  const [hasPrompted, setHasPrompted] = useState(false);

  useEffect(() => {
    if (Platform.OS !== 'android' || hasPrompted) return;

    const checkAndPrompt = async () => {
      try {
        await playStoreUpdateManager.checkAndPromptForUpdates();
        setHasPrompted(true);
      } catch (error) {
        console.error('Error in auto update check:', error);
      }
    };

    // Check immediately
    checkAndPrompt();

    // Also check when app becomes active
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active' && !hasPrompted) {
        checkAndPrompt();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [hasPrompted]);

  return { hasPrompted };
}
