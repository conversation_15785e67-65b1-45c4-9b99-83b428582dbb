import { useTheme } from '@react-navigation/native';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { ActivityIndicator, Alert, StyleSheet, TextInput, TouchableOpacity, View, useColorScheme } from 'react-native';
import { ThemedText } from '../../components/ThemedText';
import { ThemedView } from '../../components/ThemedView';
import { supabase } from '../../lib/supabase';

type FormData = {
  otp: string;
};

export default function VerifyScreen() {
  const params = useLocalSearchParams<{ phone: string }>();
  const [loading, setLoading] = useState(false);
  const [initialRender, setInitialRender] = useState(true);
  const colorScheme = useColorScheme();
  const theme = useTheme();
  
  // Use theme-based colors
  const backgroundColor = colorScheme === 'dark' ? theme.colors.background : '#ffffff';
  const textColor = colorScheme === 'dark' ? theme.colors.text : '#000000';
  const inputBgColor = colorScheme === 'dark' ? theme.colors.card : '#F2F2F2';
  const placeholderColor = colorScheme === 'dark' ? theme.colors.border : '#A0A0A0';
  
  useEffect(() => {
    // Set initial render to false after a short delay to ensure screen loads properly
    const timer = setTimeout(() => {
      setInitialRender(false);
    }, 300);
    
    return () => {
      clearTimeout(timer);
    };
  }, []);
  
  const { control, handleSubmit, formState: { errors } } = useForm<FormData>({
    defaultValues: {
      otp: ''
    }
  });

  const onSubmit = async (data: FormData) => {
    try {
      setLoading(true);
      
      const { error, data: authData } = await supabase.auth.verifyOtp({
        phone: params.phone || '',
        token: data.otp,
        type: 'sms',
      });

      if (error) throw error;

      // Check if this is a new user (no profile yet)
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', authData.user?.id)
        .single();
      
      if (profileError) {
        // New user with no profile - go to profile setup
        console.log('No profile found, navigating to create profile page...');
        router.replace('/auth/create-profile');
      } else {
        // User has a profile (complete or incomplete) - go to main app
        console.log('Profile found, navigating to tabs...');
        router.replace('/(tabs)');
      }
      
    } catch (error: any) {
      console.error('Verification error:', error.message);
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  // Show loading indicator during initial render
  if (initialRender) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor }}>
        <ActivityIndicator size="large" color="#f97316" />
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor }}>
      <ThemedView style={[styles.container, { backgroundColor }]}>
        <View style={styles.content}>
          <ThemedText style={styles.title}>Verify Phone</ThemedText>
          <ThemedText style={styles.subtitle}>
            Enter the verification code sent to {params.phone}
          </ThemedText>

          <View style={styles.form}>
            <ThemedText style={styles.label}>Verification Code</ThemedText>
            <Controller
              control={control}
              rules={{
                required: 'Verification code is required',
                pattern: {
                  value: /^[0-9]{6}$/,
                  message: 'Please enter a valid 6-digit code'
                }
              }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={[styles.input, { 
                    backgroundColor: inputBgColor, 
                    color: textColor 
                  }]}
                  placeholder="Enter 6-digit code"
                  placeholderTextColor={placeholderColor}
                  keyboardType="number-pad"
                  onBlur={onBlur}
                  onChangeText={onChange}
                  value={value}
                  maxLength={6}
                  autoFocus
                />
              )}
              name="otp"
            />
            {errors.otp && (
              <ThemedText style={[styles.errorText, { color: 'red' }]}>{errors.otp.message}</ThemedText>
            )}

            <TouchableOpacity
              style={[styles.button, loading && styles.buttonDisabled, { backgroundColor: '#f97316' }]}
              onPress={handleSubmit(onSubmit)}
              disabled={loading}
            >
              <ThemedText style={[styles.buttonText, { color: 'white' }]}>
                {loading ? 'Verifying...' : 'Verify & Continue'}
              </ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.resendButton}
              onPress={() => {
                router.replace({
                  pathname: '/auth/phone'
                } as any);
              }}
            >
              <ThemedText style={[styles.resendText, { color: '#f97316' }]}>Go back to change number</ThemedText>
            </TouchableOpacity>
          </View>
        </View>
      </ThemedView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    maxWidth: 400,
    width: '100%',
    alignSelf: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 30,
    opacity: 0.7,
  },
  form: {
    width: '100%',
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
  },
  input: {
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    marginBottom: 20,
    letterSpacing: 8,
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#f97316',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 20,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  errorText: {
    marginTop: -15,
    marginBottom: 10,
  },
  resendButton: {
    marginTop: 20,
    alignItems: 'center',
  },
  resendText: {
    fontSize: 16,
  },
}); 