import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { supabase } from '@/lib/supabase';
import { Feather } from '@expo/vector-icons';
import { decode } from 'base64-arraybuffer';
import * as ImagePicker from 'expo-image-picker';
import { Stack, router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Image,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    View,
    useColorScheme,
} from 'react-native';

// Add a simple UUID generator function that doesn't rely on crypto.getRandomValues()
function generateUUID() {
  let dt = new Date().getTime();
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = (dt + Math.random() * 16) % 16 | 0;
    dt = Math.floor(dt / 16);
    return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
  });
}

export default function CreateSiteScreen() {
  const [siteName, setSiteName] = useState('');
  const [organizationName, setOrganizationName] = useState('');
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [loading, setLoading] = useState(false);
  const colorScheme = useColorScheme();
  
  const textInputBg = colorScheme === 'dark' ? '#262626' : '#f8fafc';
  const textInputColor = colorScheme === 'dark' ? '#fff' : '#000';
  const borderColor = colorScheme === 'dark' ? '#404040' : '#e2e8f0';
  
  // Handle image selection
  const selectImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: "images",
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
        base64: true,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        setImageUri(asset.uri);
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };
  
  // Upload image to Supabase Storage
  const uploadImage = async () => {
    if (!imageUri) return null;
    
    try {
      setUploading(true);
      console.log('Starting site image upload with URI:', imageUri);
      
      // Check if the image URI is valid
      let imageUriToUse = imageUri;
      if (!imageUriToUse.startsWith('file://') && !imageUriToUse.startsWith('content://')) {
        console.log('Adding file:// prefix to URI');
        imageUriToUse = `file://${imageUriToUse}`;
      }
      
      // Fetch the image
      console.log('Fetching image from URI');
      const response = await fetch(imageUriToUse);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
      }
      
      console.log('Converting response to blob');
      const blob = await response.blob();
      console.log('Blob size:', blob.size);
      
      const reader = new FileReader();
      return new Promise<string | null>((resolve, reject) => {
        reader.onload = async () => {
          try {
            const base64Data = reader.result?.toString().split(',')[1];
            if (!base64Data) {
              console.error('Failed to convert image to base64');
              resolve(null);
              return;
            }
            
            // Use the custom UUID generator instead of uuidv4()
            const fileName = `site-${generateUUID()}`;
            const filePath = `${fileName}.jpg`;
            
            // Upload to Supabase Storage
            console.log('Uploading to Supabase storage');
            const { error: uploadError } = await supabase.storage
              .from('sites')
              .upload(filePath, decode(base64Data), {
                contentType: 'image/jpeg',
              });
              
            if (uploadError) {
              console.error('Supabase storage error:', uploadError);
              resolve(null);
              return;
            }
            
            // Get public URL
            const { data } = supabase.storage
              .from('sites')
              .getPublicUrl(filePath);
            
            console.log('Site image uploaded successfully:', data.publicUrl);  
            resolve(data.publicUrl);
          } catch (error) {
            console.error('Error in reader.onload:', error);
            reject(error);
          }
        };
        
        reader.onerror = (event) => {
          console.error('FileReader error:', event);
          reject(new Error('FileReader error'));
        };
        
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      return null;
    } finally {
      setUploading(false);
    }
  };
  
  // Create site
  const createSite = async () => {
    // Validate form fields
    if (!siteName.trim()) {
      Alert.alert('Error', 'Please enter a site name');
      return;
    }
    
    if (!organizationName.trim()) {
      Alert.alert('Error', 'Please enter an organization name');
      return;
    }
    
    setLoading(true);
    console.log('Starting site creation process');
    
    try {
      // Get user data
      console.log('Getting user data');
      const { data: userData, error: userError } = await supabase.auth.getUser();
      
      if (userError || !userData.user) {
        console.error('User authentication error:', userError);
        throw new Error('User not authenticated');
      }
      
      // Get user profile
      console.log('Getting user profile');
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('user_id', userData.user.id)
        .single();
        
      if (profileError || !profileData) {
        console.error('Profile fetch error:', profileError);
        throw new Error('Profile not found');
      }
      
      // Upload image if selected
      let siteImageUrl = null;
      if (imageUri) {
        console.log('Uploading image');
        try {
          siteImageUrl = await uploadImage();
          console.log('Image uploaded, URL:', siteImageUrl);
        } catch (imageError) {
          console.error('Image upload error:', imageError);
          // Continue without image if upload fails
        }
      }
      
      // Create site record
      console.log('Creating site record');
      const { data: site, error: siteError } = await supabase
        .from('sites')
        .insert([
          {
            name: siteName.trim(),
            organization_name: organizationName.trim(),
            site_image_url: siteImageUrl,
            owner_id: profileData.id,
            status: 'active',
          },
        ])
        .select()
        .single();
        
      if (siteError) {
        console.error('Site creation error:', siteError);
        throw new Error(siteError.message);
      }
      
      console.log('Site created successfully:', site);
      
      // Add creator as Super Admin in site_members
      console.log('Adding creator as Super Admin');
      const { error: memberError } = await supabase
        .from('site_members')
        .insert([
          {
            site_id: site.id,
            user_id: userData.user.id,
            role: 'Super Admin',
            category: 'Site Owner',
          },
        ]);
        
      if (memberError) {
        console.error('Error adding site member:', memberError);
        // Don't throw error here, still consider site creation successful
      } else {
        console.log('Creator added as Super Admin successfully');
      }
      
      // Success message
      Alert.alert(
        'Success',
        'Site created successfully!',
        [
          {
            text: 'OK',
            onPress: () => {
              // Navigate back to sites screen
              console.log('Site creation complete, navigating back');
              router.replace("/(tabs)/sites");
            }
          }
        ]
      );
      
    } catch (error: any) {
      console.error('Error creating site:', error);
      Alert.alert('Error', error.message || 'Failed to create site. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      <ThemedView style={styles.container}>
        <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
        
        <Stack.Screen
          options={{
            title: 'Create Site',
            headerRight: () => (
              <TouchableOpacity
                onPress={createSite}
                disabled={loading}
                style={styles.saveButton}
              >
                {loading ? (
                  <ActivityIndicator size="small" color="#fff" />
                ) : (
                  <ThemedText style={styles.saveButtonText}>Create</ThemedText>
                )}
              </TouchableOpacity>
            ),
          }}
        />
        
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Site Name *</ThemedText>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: textInputBg,
                  color: textInputColor,
                  borderColor: borderColor,
                },
              ]}
              placeholder="Enter site name"
              placeholderTextColor={colorScheme === 'dark' ? '#a3a3a3' : '#94a3b8'}
              value={siteName}
              onChangeText={setSiteName}
              autoCapitalize="words"
            />
          </View>
          
          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Organization Name *</ThemedText>
            <TextInput
              style={[
                styles.input,
                {
                  backgroundColor: textInputBg,
                  color: textInputColor,
                  borderColor: borderColor,
                },
              ]}
              placeholder="Enter organization name"
              placeholderTextColor={colorScheme === 'dark' ? '#a3a3a3' : '#94a3b8'}
              value={organizationName}
              onChangeText={setOrganizationName}
              autoCapitalize="words"
            />
          </View>
          
          <View style={styles.formGroup}>
            <ThemedText style={styles.label}>Site Image (Optional)</ThemedText>
            
            <TouchableOpacity
              style={[
                styles.imageUploadContainer,
                { borderColor: borderColor }
              ]}
              onPress={selectImage}
              disabled={uploading}
            >
              {imageUri ? (
                <Image source={{ uri: imageUri }} style={styles.previewImage} />
              ) : (
                <View style={styles.uploadPlaceholder}>
                  <Feather
                    name="image"
                    size={40}
                    color={colorScheme === 'dark' ? '#a3a3a3' : '#94a3b8'}
                  />
                  <ThemedText style={styles.uploadText}>
                    Tap to select an image
                  </ThemedText>
                </View>
              )}
              
              {uploading && (
                <View style={styles.uploadingOverlay}>
                  <ActivityIndicator size="large" color="#f97316" />
                </View>
              )}
            </TouchableOpacity>
            
            {imageUri && (
              <TouchableOpacity
                style={styles.removeImageButton}
                onPress={() => setImageUri(null)}
              >
                <ThemedText style={styles.removeImageText}>
                  Remove Image
                </ThemedText>
              </TouchableOpacity>
            )}
          </View>
        </ScrollView>
      </ThemedView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
  },
  imageUploadContainer: {
    height: 200,
    borderWidth: 1,
    borderRadius: 8,
    borderStyle: 'dashed',
    overflow: 'hidden',
    position: 'relative',
  },
  uploadPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadText: {
    marginTop: 8,
    fontSize: 14,
    opacity: 0.7,
  },
  previewImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  uploadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeImageButton: {
    marginTop: 8,
    alignSelf: 'flex-end',
  },
  removeImageText: {
    fontSize: 14,
    color: '#ef4444',
  },
  saveButton: {
    backgroundColor: '#f97316',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 6,
  },
  saveButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
}); 