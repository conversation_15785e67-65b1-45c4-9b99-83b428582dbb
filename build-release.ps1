# Infratask Release Build Script
Write-Host "🚀 Infratask Release Build Script" -ForegroundColor Green

# Set paths
$keystorePath = "android\app\infratask-release-key.keystore"
$androidDir = "android"

# Check if keystore exists
if (Test-Path $keystorePath) {
    Write-Host "✅ Keystore already exists at: $keystorePath" -ForegroundColor Green
} else {
    Write-Host "🔑 Generating release keystore..." -ForegroundColor Yellow
    
    # Try different keytool locations
    $keytoolPaths = @(
        "keytool",
        "C:\Program Files\Android\Android Studio\jbr\bin\keytool.exe",
        "C:\Program Files\Java\jdk-17\bin\keytool.exe",
        "C:\Program Files\Java\jdk-11\bin\keytool.exe",
        "C:\Program Files (x86)\Java\jdk-17\bin\keytool.exe",
        "C:\Program Files (x86)\Java\jdk-11\bin\keytool.exe"
    )
    
    $keytoolCmd = $null
    foreach ($path in $keytoolPaths) {
        try {
            if ($path -eq "keytool") {
                & keytool -version 2>$null
                if ($LASTEXITCODE -eq 0) {
                    $keytoolCmd = "keytool"
                    break
                }
            } elseif (Test-Path $path) {
                $keytoolCmd = "`"$path`""
                break
            }
        } catch {
            # Continue to next path
        }
    }
    
    if ($keytoolCmd) {
        Write-Host "🔧 Found keytool: $keytoolCmd" -ForegroundColor Green
        
        $cmd = "$keytoolCmd -genkeypair -v -storetype PKCS12 -keystore `"$keystorePath`" -alias infratask-key-alias -keyalg RSA -keysize 2048 -validity 10000 -storepass `"Mansoor@24`" -keypass `"Mansoor@24`" -dname `"CN=Infratask, OU=Development, O=Infratask, L=City, ST=State, C=US`""
        
        Write-Host "Executing: $cmd" -ForegroundColor Cyan
        Invoke-Expression $cmd
        
        if (Test-Path $keystorePath) {
            Write-Host "✅ Keystore generated successfully!" -ForegroundColor Green
        } else {
            Write-Host "❌ Keystore generation failed" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "❌ Keytool not found!" -ForegroundColor Red
        Write-Host "Please install Java JDK or Android Studio" -ForegroundColor Yellow
        Write-Host "Manual instructions available in BUILD_INSTRUCTIONS.md" -ForegroundColor Yellow
        exit 1
    }
}

# Build options
Write-Host ""
Write-Host "📦 Build Options:" -ForegroundColor Cyan
Write-Host "1. EAS Build (Cloud) - Recommended"
Write-Host "2. Gradle Build (Local) - Requires Java setup"
Write-Host "3. Exit"

$choice = Read-Host "Choose an option (1-3)"

switch ($choice) {
    "1" {
        Write-Host "🌤️ Starting EAS Cloud Build..." -ForegroundColor Yellow
        npx eas build --platform android --profile production
    }
    "2" {
        Write-Host "🔨 Starting Gradle Build..." -ForegroundColor Yellow
        Set-Location $androidDir
        .\gradlew bundleRelease
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Build successful!" -ForegroundColor Green
            Write-Host "📍 AAB file location: app\build\outputs\bundle\release\app-release.aab" -ForegroundColor Green
        } else {
            Write-Host "❌ Build failed. Try EAS Build instead." -ForegroundColor Red
        }
        Set-Location ..
    }
    "3" {
        Write-Host "👋 Goodbye!" -ForegroundColor Green
        exit 0
    }
    default {
        Write-Host "❌ Invalid choice" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "✅ Script completed!" -ForegroundColor Green 