import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface PaymentUpdateRequest {
  razorpay_order_id: string;
  razorpay_payment_id?: string;
  razorpay_signature?: string;
  plan_id: string;
  payment_status: 'success' | 'cancelled' | 'failed';
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')!
    const token = authHeader.replace('Bearer ', '')

    // Get user from token
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser(token)
    
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse request body
    const body: PaymentUpdateRequest = await req.json()
    const { 
      razorpay_order_id, 
      razorpay_payment_id, 
      razorpay_signature, 
      plan_id, 
      payment_status 
    } = body

    console.log('🔄 Processing payment update:', {
      user_id: user.id,
      razorpay_order_id,
      payment_status,
      plan_id,
      has_payment_id: !!razorpay_payment_id,
      has_signature: !!razorpay_signature
    })

    // Update razorpay_orders table status
    const orderStatus = payment_status === 'success' ? 'paid' : 
                       payment_status === 'cancelled' ? 'cancelled' : 'failed'

    const { error: orderUpdateError } = await supabaseClient
      .from('razorpay_orders')
      .update({ 
        status: orderStatus,
        updated_at: new Date().toISOString()
      })
      .eq('order_id', razorpay_order_id)
      .eq('user_id', user.id)

    if (orderUpdateError) {
      console.error('Error updating razorpay_orders:', orderUpdateError)
    }

    // If payment is successful, update/create user_subscriptions
    if (payment_status === 'success' && razorpay_payment_id && razorpay_signature) {
      // Validate required fields
      if (!razorpay_order_id || !plan_id) {
        console.error('❌ Missing required fields:', { razorpay_order_id, plan_id })
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Missing required payment data',
            details: 'Order ID and Plan ID are required'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      const currentDate = new Date()
      const endDate = new Date(currentDate)
      endDate.setDate(endDate.getDate() + 30) // 30 days subscription

      const subscriptionData = {
        user_id: user.id,
        plan_id: plan_id,
        plan_name: plan_id === 'standard' ? 'Standard Plan' : 'Premium Plan',
        status: 'active',
        current_period_start: currentDate.toISOString(),
        current_period_end: endDate.toISOString(),
        order_id: razorpay_order_id,
        payment_id: razorpay_payment_id,
        razorpay_signature: razorpay_signature,
        additional_users: 0,
        auto_renew: true,
        updated_at: new Date().toISOString()
      }

      // Check if subscription already exists, then update or insert
      const { data: existingSubscription } = await supabaseClient
        .from('user_subscriptions')
        .select('id')
        .eq('user_id', user.id)
        .single()

      let subscriptionResult
      let subscriptionError

      if (existingSubscription) {
        // Update existing subscription
        const { data, error } = await supabaseClient
          .from('user_subscriptions')
          .update(subscriptionData)
          .eq('user_id', user.id)
          .select()
          .single()
        subscriptionResult = data
        subscriptionError = error
        console.log('📝 Updated existing subscription for user:', user.id)
      } else {
        // Insert new subscription
        const { data, error } = await supabaseClient
          .from('user_subscriptions')
          .insert([subscriptionData])
          .select()
          .single()
        subscriptionResult = data
        subscriptionError = error
        console.log('✨ Created new subscription for user:', user.id)
      }

      if (subscriptionError) {
        console.error('❌ Error updating user_subscriptions:', {
          error: subscriptionError,
          user_id: user.id,
          plan_id: plan_id,
          order_id: razorpay_order_id
        })
        return new Response(
          JSON.stringify({
            success: false,
            error: 'Failed to update subscription',
            details: subscriptionError.message,
            code: subscriptionError.code || 'UNKNOWN_ERROR'
          }),
          {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
      }

      console.log('✅ Subscription updated successfully:', {
        subscription_id: subscriptionResult.id,
        user_id: subscriptionResult.user_id,
        plan_id: subscriptionResult.plan_id,
        status: subscriptionResult.status,
        order_id: subscriptionResult.order_id
      })

      return new Response(
        JSON.stringify({
          success: true,
          message: 'Payment processed and subscription activated successfully',
          subscription: subscriptionResult
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    } else {
      // Payment cancelled or failed - only update order status
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: `Payment ${payment_status} - order status updated`,
          order_status: orderStatus
        }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

  } catch (error) {
    console.error('Error in update-subscription-payment function:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
