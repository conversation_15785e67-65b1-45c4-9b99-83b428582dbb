-- Create sub_contractor_attendance table
CREATE TABLE IF NOT EXISTS sub_contractor_attendance (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sub_contractor_id UUID NOT NULL REFERENCES sub_contractors(id) ON DELETE CASCADE,
    site_id UUID NOT NULL REFERENCES sites(id) ON DELETE CASCADE,
    attendance_date DATE NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('present', 'absent', 'half_day', 'overtime')),
    overtime_hours DECIMAL(4,2) DEFAULT NULL,
    marked_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one attendance record per sub-contractor per date
    UNIQUE(sub_contractor_id, attendance_date)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_sub_contractor_attendance_contractor_id ON sub_contractor_attendance(sub_contractor_id);
CREATE INDEX IF NOT EXISTS idx_sub_contractor_attendance_site_id ON sub_contractor_attendance(site_id);
CREATE INDEX IF NOT EXISTS idx_sub_contractor_attendance_date ON sub_contractor_attendance(attendance_date);
CREATE INDEX IF NOT EXISTS idx_sub_contractor_attendance_status ON sub_contractor_attendance(status);

-- Enable Row Level Security (RLS)
ALTER TABLE sub_contractor_attendance ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only access attendance for sites they have access to
CREATE POLICY "Users can view sub-contractor attendance for their sites" ON sub_contractor_attendance
    FOR SELECT USING (
        site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert sub-contractor attendance for their sites" ON sub_contractor_attendance
    FOR INSERT WITH CHECK (
        site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update sub-contractor attendance for their sites" ON sub_contractor_attendance
    FOR UPDATE USING (
        site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete sub-contractor attendance for their sites" ON sub_contractor_attendance
    FOR DELETE USING (
        site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    );

-- Create trigger for updated_at
CREATE TRIGGER update_sub_contractor_attendance_updated_at 
    BEFORE UPDATE ON sub_contractor_attendance 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create a view similar to attendance_with_labor for easier querying
CREATE OR REPLACE VIEW sub_contractor_attendance_with_details AS
SELECT 
    sca.*,
    sc.name as contractor_name,
    sc.category as contractor_category
FROM sub_contractor_attendance sca
JOIN sub_contractors sc ON sca.sub_contractor_id = sc.id; 