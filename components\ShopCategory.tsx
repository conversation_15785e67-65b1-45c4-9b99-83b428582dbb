import LocationSelector from '@/components/LocationSelector';
import { useAuth } from '@/context/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { supabase } from '@/lib/supabase';
import { MaterialIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Dimensions,
    Image,
    Pressable,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width } = Dimensions.get('window');

interface Category {
  id: number;
  name: string;
  description: string;
  icon: string;
  color: string;
  display_order: number;
  is_active: boolean;
  subcategories?: SubCategory[];
}

interface SubCategory {
  id: number;
  name: string;
  description: string;
  parent_category_id: number;
  icon: string;
  product_count?: number;
}

interface ShopCategoryProps {
  onCategorySelect?: (categoryId: number, categoryName: string) => void;
}

// Category image mapping for main categories and subcategories
const getCategoryImage = (categoryName: string, isMainCategory: boolean = false) => {
  // Main category images (you may want to create specific images for these)
  const mainCategoryImages: { [key: string]: any } = {
    'Civil & Interior': require('@/assets/category/Cement.png'), // Using cement as representative
    'Furniture & Architectural Hardware': require('@/assets/category/Kitchen_Systems_and_Accessories.png'),
    'Electrical': require('@/assets/category/Wires.png'),
    'Plumbing, Sanitary & Bath': require('@/assets/category/CPVC_Pipes_and_Fittings.png'),
  };

  // Subcategory images (existing images)
  const subcategoryImages: { [key: string]: any } = {
    'Cement': require('@/assets/category/Cement.png'),
    'Painting': require('@/assets/category/Painting.png'),
    'Waterproofing': require('@/assets/category/Waterproofing.png'),
    'Fevicol': require('@/assets/category/Fevicol.png'),
    'Plywood MDF & HDHMR': require('@/assets/category/Plywood_MDF_and_HDHMR.png'),
    'Kitchen Systems & Accessories': require('@/assets/category/Kitchen_Systems_and_Accessories.png'),
    'Overhead Tanks': require('@/assets/category/Overhead Tanks.png'),
    'Sanitary & Bath Fittings': require('@/assets/category/Sanitary_and_Bath_Fittings.png'),
    'Tiles': require('@/assets/category/Tiles.png'),
    'Conduits & GI Boxes': require('@/assets/category/Conduits_and_GI_Boxes.png'),
    'CPVC Pipes & Fittings': require('@/assets/category/CPVC_Pipes_and_Fittings.png'),
    'Door Locks & Hardware': require('@/assets/category/Door_Locks_and_Hardware.png'),
    'Hinges & Channels & handles': require('@/assets/category/Hinges_and_Channels_and_handles.png'),
    'Switches & Sockets': require('@/assets/category/Switches_and_Sockets.png'),
    'Wardrobe & Bed Fittings': require('@/assets/category/Wardrobe_and_Bed_Fittings.png'),
    'Wires': require('@/assets/category/Wires.png'),
    'Bricks': require('@/assets/category/Bricks.png'),
    'Steel': require('@/assets/category/Steel.png'),
    'Sand': require('@/assets/category/Sand.png'),
    'Tools': require('@/assets/category/Tools.png'),
  };

  if (isMainCategory) {
    return mainCategoryImages[categoryName] || require('@/assets/category/Tools.png');
  }

  return subcategoryImages[categoryName] || require('@/assets/category/Tools.png');
};



export default function ShopCategory({ onCategorySelect }: ShopCategoryProps) {
  const colorScheme = useColorScheme();
  const { user, profile } = useAuth();
  const insets = useSafeAreaInsets();
  const primaryColor = '#f97316';

  // Force light mode for consistency
  const backgroundColor = '#fff';
  const cardBackground = '#fff';
  const textColor = '#000';
  const secondaryTextColor = '#666';
  const borderColor = '#e5e5e5';

  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [currentLocation, setCurrentLocation] = useState<{
    address: string;
    coordinates?: { latitude: number; longitude: number };
    deliveryTime?: string;
  } | null>(null);

  // Load categories from Supabase
  const loadCategories = async () => {
    try {
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('shop_categories')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      if (categoriesError) {
        console.error('Error loading categories:', categoriesError);
        return;
      }

      // Load subcategories for each category
      const { data: subcategoriesData, error: subcategoriesError } = await supabase
        .from('shop_subcategories')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      if (subcategoriesError) {
        console.error('Error loading subcategories:', subcategoriesError);
      }

      // Group subcategories by parent category
      const subcategoriesMap = (subcategoriesData || []).reduce((acc, sub) => {
        if (!acc[sub.parent_category_id]) {
          acc[sub.parent_category_id] = [];
        }
        acc[sub.parent_category_id].push(sub);
        return acc;
      }, {} as { [key: number]: SubCategory[] });

      // Add subcategories to each category
      const categoriesWithSubs = categoriesData.map(category => ({
        ...category,
        subcategories: subcategoriesMap[category.id] || []
      }));

      setCategories(categoriesWithSubs);
    } catch (error) {
      console.error('Error loading categories:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCategories();
  }, []);

  // Filter categories based on search - include subcategories in search
  const filteredCategories = categories.filter(category => {
    const query = searchQuery.toLowerCase();

    // Check if main category matches
    const categoryMatches = category.name.toLowerCase().includes(query) ||
                           category.description.toLowerCase().includes(query);

    // Check if any subcategory matches
    const subcategoryMatches = category.subcategories?.some(subcategory =>
      subcategory.name.toLowerCase().includes(query) ||
      subcategory.description.toLowerCase().includes(query)
    );

    return categoryMatches || subcategoryMatches;
  }).map(category => {
    // If searching, filter subcategories to only show matching ones
    if (searchQuery.trim() === '') {
      return category;
    }

    const query = searchQuery.toLowerCase();
    const categoryMatches = category.name.toLowerCase().includes(query) ||
                           category.description.toLowerCase().includes(query);

    // If main category matches, show all subcategories
    if (categoryMatches) {
      return category;
    }

    // If only subcategories match, show only matching subcategories
    const filteredSubcategories = category.subcategories?.filter(subcategory =>
      subcategory.name.toLowerCase().includes(query) ||
      subcategory.description.toLowerCase().includes(query)
    );

    return {
      ...category,
      subcategories: filteredSubcategories || []
    };
  });

  const handleCategoryPress = (category: Category) => {
    if (selectedCategory?.id === category.id) {
      setSelectedCategory(null);
    } else {
      setSelectedCategory(category);
    }
  };

  const handleSubcategoryPress = (subcategory: SubCategory) => {
    if (onCategorySelect) {
      onCategorySelect(subcategory.parent_category_id, subcategory.name);
    }
    // Navigate to category details page
    router.push({
      pathname: '/(main)/category-details',
      params: {
        categoryId: subcategory.id.toString(),
        categoryName: subcategory.name
      }
    });
  };

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor, justifyContent: 'center', alignItems: 'center' }]}>
        <ActivityIndicator size="large" color={primaryColor} />
        <Text style={[styles.loadingText, { color: secondaryTextColor }]}>Loading categories...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor }]}>
      {/* Header with Location and Search */}
      <View style={[styles.headerContainer, { paddingTop: insets.top + 10 }]}>
        {/* Profile and Location Section */}
        <View style={styles.profileLocationContainer}>
          {/* Profile Image */}
          <Pressable
            style={styles.profileImageContainer}
            onPress={() => router.push('/(main)/(tabs)/profile')}
          >
            {profile?.profile_image_url ? (
              <Image
                source={{ uri: profile.profile_image_url }}
                style={styles.profileImage}
              />
            ) : (
              <View style={[styles.profileImagePlaceholder, { backgroundColor: primaryColor }]}>
                <Text style={styles.profileImageText}>
                  {profile?.full_name?.charAt(0) || user?.email?.charAt(0) || '?'}
                </Text>
              </View>
            )}
          </Pressable>

          {/* Location Selector */}
          <View style={styles.locationSelectorContainer}>
            <LocationSelector
              onLocationChange={(location) => {
                setCurrentLocation(location);
                console.log('Location changed:', location);
              }}
              initialLocation={currentLocation || undefined}
            />
          </View>
        </View>

        {/* Search Bar */}
        <View style={[styles.searchContainer, { backgroundColor: cardBackground, borderColor }]}>
          <MaterialIcons name="search" size={18} color={secondaryTextColor} />
          <TextInput
            style={[styles.searchInput, { color: textColor }]}
            placeholder="Search categories and subcategories..."
            placeholderTextColor={secondaryTextColor}
            value={searchQuery}
            onChangeText={setSearchQuery}
            autoCorrect={false}
            autoCapitalize="none"
            returnKeyType="search"
          />
          {searchQuery.length > 0 && (
            <Pressable onPress={() => setSearchQuery('')}>
              <MaterialIcons name="clear" size={18} color={secondaryTextColor} />
            </Pressable>
          )}
        </View>
      </View>

      {/* Categories Grid */}
      <ScrollView
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {filteredCategories.map((category) => (
          <View key={category.id} style={styles.categorySection}>
            {/* Main Category Header */}
            <View style={styles.categoryHeader}>
              <Text style={[styles.categoryTitle, { color: textColor }]}>
                {category.name}
              </Text>
            </View>

            {/* Subcategories Grid - 4 columns */}
            {category.subcategories && category.subcategories.length > 0 && (
              <View style={styles.subcategoriesGrid}>
                {category.subcategories.map((subcategory, index) => {
                  if (index % 4 === 0) {
                    const subcategoryGroup = category.subcategories!.slice(index, index + 4);
                    return (
                      <View key={`row-${index}`} style={styles.subcategoryRow}>
                        {subcategoryGroup.map((subcat) => (
                          <Pressable
                            key={subcat.id}
                            style={[styles.subcategoryCard, { backgroundColor: cardBackground }]}
                            onPress={() => handleSubcategoryPress(subcat)}
                          >
                            <View style={styles.subcategoryImageContainer}>
                              <Image
                                source={getCategoryImage(subcat.name, false)}
                                style={styles.subcategoryImage}
                                resizeMode="contain"
                              />
                            </View>
                            <Text style={[styles.subcategoryName, { color: textColor }]} numberOfLines={2}>
                              {subcat.name}
                            </Text>
                          </Pressable>
                        ))}
                        {/* Add empty placeholders for remaining slots */}
                        {Array.from({ length: 4 - subcategoryGroup.length }).map((_, emptyIndex) => (
                          <View key={`empty-${index}-${emptyIndex}`} style={styles.subcategoryCard} />
                        ))}
                      </View>
                    );
                  }
                  return null;
                })}
              </View>
            )}
          </View>
        ))}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    backgroundColor: '#fff',
  },
  profileLocationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  profileImageContainer: {
    marginRight: 12,
  },
  profileImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  profileImagePlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileImageText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  locationSelectorContainer: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    height: 40,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 14,
    fontWeight: '400',
    paddingVertical: 0,
    textAlignVertical: 'center',
    includeFontPadding: false,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  categorySection: {
    marginBottom: 24,
  },
  categoryHeader: {
    marginBottom: 12,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000',
  },
  subcategoriesGrid: {
    gap: 8,
  },
  subcategoryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
    gap: 6,
  },
  subcategoryCard: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 8,
    alignItems: 'center',
    minHeight: 100,
    justifyContent: 'center',
  },
  subcategoryImageContainer: {
    width: 50,
    height: 50,
    borderRadius: 6,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 6,
  },
  subcategoryImage: {
    width: 32,
    height: 32,
  },
  subcategoryName: {
    fontSize: 10,
    fontWeight: '500',
    textAlign: 'center',
    lineHeight: 12,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 12,
  },
});
