# Razorpay Live Keys Setup for Production

This document explains how live Razorpay keys are configured for production builds of the Infratask app.

## 🔑 Key Configuration

### Environment Variables (.env file)

The `.env` file now contains both test and live keys:

```bash
# Razorpay Configuration - Test Keys (Development)
EXPO_PUBLIC_RAZORPAY_KEY_ID=rzp_test_NQ26LCch0J1GHa
RAZORPAY_KEY_SECRET=gsedvop5Yvz7fKnilyqy8L37

# Razorpay Configuration - Live Keys (Production)
EXPO_PUBLIC_RAZORPAY_KEY_ID_LIVE=***********************
RAZORPAY_KEY_SECRET_LIVE=DgGurBMtCbIaIDRas7YxIYVg
```

### Supabase Edge Function Environment Variables

The following secrets have been added to Supabase:

- `RAZORPAY_KEY_ID_LIVE`: Live key ID for production payments
- `RAZORPAY_KEY_SECRET_LIVE`: Live key secret for production payments  
- `ENVIRONMENT`: Set to "production" to enable live keys

## 🏗️ Build Configuration

### EAS Build Profiles (eas.json)

The production build profile now sets the environment:

```json
{
  "build": {
    "production": {
      "autoIncrement": true,
      "android": {
        "buildType": "app-bundle"
      },
      "env": {
        "ENVIRONMENT": "production"
      }
    }
  }
}
```

## 🔄 Key Selection Logic

### Client-Side (React Native)

In `app/(main)/(tabs)/subscription.tsx`:

```typescript
// Check if we're in production environment (only when ENVIRONMENT is explicitly set to 'production')
// This ensures development builds use test keys even when __DEV__ is false
const isProduction = process.env.ENVIRONMENT === 'production';

const RAZORPAY_CONFIG = {
  key_id: isProduction
    ? process.env.EXPO_PUBLIC_RAZORPAY_KEY_ID_LIVE
    : process.env.EXPO_PUBLIC_RAZORPAY_KEY_ID,
  // ... other config
};
```

### Server-Side (Supabase Edge Function)

In `supabase/functions/create-razorpay-order/index.ts`:

```typescript
// Check if we should use live keys (production environment only)
// This ensures development builds use test keys consistently with client-side logic
const isProduction = Deno.env.get('ENVIRONMENT') === 'production'

const RAZORPAY_KEY_ID = isProduction
  ? (Deno.env.get('RAZORPAY_KEY_ID_LIVE') || '***********************')
  : (Deno.env.get('RAZORPAY_KEY_ID') || 'rzp_test_NQ26LCch0J1GHa')
```

## 🚀 How It Works

### Development Mode
- Uses test keys (`rzp_test_*`)
- `ENVIRONMENT` variable is not set to "production"
- Safe for testing without real payments
- Works in both Expo Go and development builds

### Production Mode
- Uses live keys (`rzp_live_*`)
- `ENVIRONMENT` variable explicitly set to "production"
- Real payments processed through Razorpay
- Only activated in production builds (Play Store/App Store)

## 🔧 Deployment Steps

### 1. Build Production APK
```bash
eas build --platform android --profile production
```

### 2. Environment Detection
The app automatically detects production environment and switches to live keys.

### 3. Edge Function
The Supabase edge function also automatically uses live keys in production.

## ⚠️ Security Notes

1. **Live keys are never exposed to client-side code in production**
2. **Key secrets are stored securely in Supabase environment variables**
3. **Test keys are used in development for safety**
4. **Production builds automatically use live keys**

## 🧪 Testing

### Development Testing
- Use test cards: `4111 1111 1111 1111`
- No real money transactions
- Safe for unlimited testing

### Production Testing
- Use real payment methods
- Real money transactions
- Test with small amounts first

## 📝 Verification

To verify which keys are being used, check the console logs:

```
Razorpay Environment: Production (Live Keys)
```

or

```
Razorpay Environment: Development (Test Keys)
```

## 🔄 Key Rotation

To update live keys in the future:

1. Update `.env` file with new keys
2. Update Supabase secrets:
   ```bash
   npx supabase secrets set RAZORPAY_KEY_ID_LIVE=new_live_key --project-ref vsnhscndlifvaptwdfsw
   npx supabase secrets set RAZORPAY_KEY_SECRET_LIVE=new_live_secret --project-ref vsnhscndlifvaptwdfsw
   ```
3. Redeploy edge functions:
   ```bash
   npx supabase functions deploy create-razorpay-order --project-ref vsnhscndlifvaptwdfsw
   ```

## ✅ Current Status

- ✅ Live keys added to `.env` file
- ✅ Live keys added to Supabase environment variables
- ✅ Client-side code updated to use appropriate keys
- ✅ Edge function updated to use appropriate keys
- ✅ Production build configuration updated
- ✅ Edge function redeployed with new logic

The app is now ready for production payments with live Razorpay keys!
