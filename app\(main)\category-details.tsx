import { useAuth } from '@/context/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { supabase } from '@/lib/supabase';
import { MaterialIcons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Dimensions,
    FlatList,
    Image,
    Pressable,
    ScrollView,
    StyleSheet,
    Text,
    View
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width } = Dimensions.get('window');

interface Product {
  id: number;
  categories: number;
  name: string;
  description: string;
  image_url: string;
  images?: string[];
  price: number;
  original_price: number;
  unit: string;
  brand: string;
  stock_quantity: number;
  is_active: boolean;
}

interface SubcategoryBanner {
  id: number;
  subcategory_id: number;
  banner_url: string;
  title: string;
  description: string;
  display_order: number;
  is_active: boolean;
}

interface CartItem {
  id: number;
  name: string;
  price: number;
  original_price?: number;
  quantity: number;
  unit: string;
  brand?: string;
}

export default function CategoryDetailsScreen() {
  const { user } = useAuth();
  const colorScheme = useColorScheme();
  const insets = useSafeAreaInsets();
  const params = useLocalSearchParams();
  
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [banners, setBanners] = useState<SubcategoryBanner[]>([]);
  const [brands, setBrands] = useState<string[]>([]);
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [addingToCart, setAddingToCart] = useState<number | null>(null);

  // Get category details from params
  const categoryId = params.categoryId as string;
  const categoryName = params.categoryName as string;

  // Theme colors - Force light mode for category details page
  const backgroundColor = '#ffffff';
  const textColor = '#000000';
  const secondaryTextColor = '#666666';
  const cardBackground = '#f8f9fa';
  const primaryColor = '#f97316';

  // Load products for this category
  const loadProducts = async () => {
    try {
      const { data, error } = await supabase
        .from('shop_products')
        .select('*')
        .eq('categories', parseInt(categoryId))
        .eq('is_active', true)
        .order('name');

      if (error) throw error;

      if (data) {
        const processedProducts = data.map(product => ({
          ...product,
          price: parseFloat(product.price) || 0,
          original_price: parseFloat(product.original_price) || 0
        }));
        setProducts(processedProducts);
        setFilteredProducts(processedProducts);
        
        // Extract unique brands
        const uniqueBrands = [...new Set(processedProducts.map(p => p.brand).filter(Boolean))];
        setBrands(uniqueBrands);
      }
    } catch (error) {
      console.error('Error loading products:', error);
    }
  };

  // Load category banners
  const loadBanners = async () => {
    try {
      const { data, error } = await supabase
        .from('shop_subcategory_banners')
        .select('*')
        .eq('subcategory_id', parseInt(categoryId))
        .eq('is_active', true)
        .order('display_order');

      if (error) throw error;
      if (data) setBanners(data);
    } catch (error) {
      console.error('Error loading banners:', error);
    }
  };

  // Filter products by brand
  const filterProducts = () => {
    let filtered = products;

    if (selectedBrand) {
      filtered = filtered.filter(product => product.brand === selectedBrand);
    }

    setFilteredProducts(filtered);
  };

  // Add to cart function
  const addToCart = async (product: Product) => {
    setAddingToCart(product.id);

    const existingItem = cart.find(item => item.id === product.id);
    const newQuantity = existingItem ? existingItem.quantity + 1 : 1;

    if (existingItem) {
      setCart(cart.map(item =>
        item.id === product.id
          ? { ...item, quantity: newQuantity }
          : item
      ));
    } else {
      const newCartItem: CartItem = {
        id: product.id,
        name: product.name,
        price: product.price,
        quantity: 1,
        unit: product.unit,
        brand: product.brand
      };
      setCart([...cart, newCartItem]);
    }

    // Save to Supabase
    await saveCartItem(product.id, newQuantity);

    // Reset the adding state after a short delay
    setTimeout(() => {
      setAddingToCart(null);
    }, 500);

    console.log(`Added ${product.name} to cart`);
  };

  // Update cart quantity function
  const updateCartQuantity = async (productId: number, quantity: number) => {
    if (quantity <= 0) {
      // Remove item from cart if quantity is 0 or less
      setCart(cart.filter(item => item.id !== productId));
      await removeCartItem(productId);
    } else {
      setCart(cart.map(item =>
        item.id === productId
          ? { ...item, quantity }
          : item
      ));
      await saveCartItem(productId, quantity);
    }
  };

  // Get current quantity of item in cart
  const getCartQuantity = (productId: number): number => {
    const cartItem = cart.find(item => item.id === productId);
    return cartItem ? cartItem.quantity : 0;
  };

  // Save cart item to Supabase
  const saveCartItem = async (productId: number, quantity: number) => {
    if (!user) return;

    try {
      // First, try to update existing item
      const { data: existingItem, error: selectError } = await supabase
        .from('shop_cart')
        .select('id')
        .eq('user_id', user.id)
        .eq('product_id', productId)
        .single();

      if (selectError && selectError.code !== 'PGRST116') {
        // PGRST116 is "not found" error, which is expected for new items
        console.error('Error checking existing cart item:', selectError);
        return;
      }

      if (existingItem) {
        // Update existing item
        const { error: updateError } = await supabase
          .from('shop_cart')
          .update({
            quantity: quantity,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', user.id)
          .eq('product_id', productId);

        if (updateError) {
          console.error('Error updating cart item:', updateError);
        }
      } else {
        // Insert new item
        const { error: insertError } = await supabase
          .from('shop_cart')
          .insert({
            user_id: user.id,
            product_id: productId,
            quantity: quantity,
            added_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (insertError) {
          console.error('Error inserting cart item:', insertError);
        }
      }
    } catch (error) {
      console.error('Error saving cart item:', error);
    }
  };

  // Remove cart item from Supabase
  const removeCartItem = async (productId: number) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('shop_cart')
        .delete()
        .eq('user_id', user.id)
        .eq('product_id', productId);

      if (error) {
        console.error('Error removing cart item:', error);
      }
    } catch (error) {
      console.error('Error removing cart item:', error);
    }
  };

  // Load cart from Supabase
  const loadCart = async () => {
    if (!user) return;

    try {
      // Get cart items from database
      const { data: cartData, error: cartError } = await supabase
        .from('shop_cart')
        .select('product_id, quantity')
        .eq('user_id', user.id);

      if (cartError) {
        console.log('Cart loading failed, using empty cart:', cartError);
        setCart([]);
        return;
      }

      if (cartData && cartData.length > 0) {
        // Get product details separately
        const productIds = cartData.map(item => item.product_id);
        const { data: productsData, error: productsError } = await supabase
          .from('shop_products')
          .select('id, name, price, original_price, unit, brand')
          .in('id', productIds);

        if (productsError) {
          console.log('Product loading failed, using empty cart:', productsError);
          setCart([]);
          return;
        }

        if (productsData) {
          const cartItems: CartItem[] = cartData.map(cartItem => {
            const product = productsData.find(p => p.id === cartItem.product_id);
            return {
              id: cartItem.product_id,
              name: product?.name || 'Unknown Product',
              price: parseFloat(product?.price) || 0,
              original_price: parseFloat(product?.original_price) || 0,
              quantity: cartItem.quantity,
              unit: product?.unit || 'piece',
              brand: product?.brand || ''
            };
          });
          setCart(cartItems);
        }
      } else {
        setCart([]);
      }
    } catch (error) {
      console.log('Cart loading failed completely, using empty cart:', error);
      setCart([]);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([loadProducts(), loadBanners()]);
      if (user) {
        await loadCart();
      }
      setLoading(false);
    };

    if (categoryId) {
      loadData();
    }
  }, [categoryId, user]);

  useEffect(() => {
    filterProducts();
  }, [selectedBrand, products]);

  // Navigate to product details
  const navigateToProductDetails = (product: Product) => {
    router.push({
      pathname: '/(main)/product-details',
      params: {
        productId: product.id.toString(),
        categoryId: categoryId,
        categoryName: categoryName
      }
    });
  };

  // Render product item
  const renderProductItem = ({ item }: { item: Product }) => (
    <Pressable
      style={[styles.productCard, { backgroundColor: cardBackground }]}
      onPress={() => navigateToProductDetails(item)}
    >
      <Image
        source={{ uri: item.image_url || 'https://via.placeholder.com/150' }}
        style={styles.productImage}
        resizeMode="cover"
      />
      <View style={styles.productInfo}>
        <Text style={[styles.productName, { color: textColor }]} numberOfLines={2}>
          {item.name}
        </Text>
        <Text style={[styles.productBrand, { color: secondaryTextColor }]}>
          {item.brand}
        </Text>
        <View style={styles.priceContainer}>
          <Text style={[styles.price, { color: primaryColor }]}>
            ₹{item.price.toFixed(2)}
          </Text>
          {item.original_price > item.price && (
            <Text style={[styles.originalPrice, { color: secondaryTextColor }]}>
              ₹{item.original_price.toFixed(2)}
            </Text>
          )}
        </View>
        <Text style={[styles.unit, { color: secondaryTextColor }]}>
          per {item.unit}
        </Text>
        {getCartQuantity(item.id) > 0 ? (
          // Quantity Counter
          <View style={[styles.quantityCounter, { borderColor: primaryColor }]}>
            <Pressable
              style={[styles.quantityButton, { backgroundColor: primaryColor }]}
              onPress={(e) => {
                e.stopPropagation();
                updateCartQuantity(item.id, getCartQuantity(item.id) - 1);
              }}
            >
              <Text style={styles.quantityButtonText}>-</Text>
            </Pressable>
            <Text style={[styles.quantityText, { color: textColor }]}>
              {getCartQuantity(item.id)}
            </Text>
            <Pressable
              style={[styles.quantityButton, { backgroundColor: primaryColor }]}
              onPress={(e) => {
                e.stopPropagation();
                updateCartQuantity(item.id, getCartQuantity(item.id) + 1);
              }}
            >
              <Text style={styles.quantityButtonText}>+</Text>
            </Pressable>
          </View>
        ) : (
          // Add to Cart Button
          <Pressable
            style={[
              styles.addButton,
              {
                backgroundColor: addingToCart === item.id ? '#d97706' : primaryColor,
                opacity: addingToCart === item.id ? 0.8 : 1
              }
            ]}
            onPress={(e) => {
              e.stopPropagation();
              addToCart(item);
            }}
            disabled={addingToCart === item.id}
          >
            <Text style={styles.addButtonText}>
              {addingToCart === item.id ? 'Adding...' : 'Add to Cart'}
            </Text>
          </Pressable>
        )}
      </View>
    </Pressable>
  );

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor, justifyContent: 'center', alignItems: 'center' }]}>
        <ActivityIndicator size="large" color={primaryColor} />
        <Text style={[styles.loadingText, { color: secondaryTextColor }]}>Loading products...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor }]}>
      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
        <Pressable style={styles.backButton} onPress={() => router.back()}>
          <MaterialIcons name="arrow-back" size={24} color={textColor} />
        </Pressable>
        <Text style={[styles.headerTitle, { color: textColor }]} numberOfLines={1}>
          {categoryName}
        </Text>
        <View style={styles.headerRight}>
          <Pressable
            style={styles.cartButton}
            onPress={() => {
              // Navigate back to shop with cart tab active
              router.push({
                pathname: '/(main)/shop',
                params: { activeTab: 'cart' }
              });
            }}
          >
            <MaterialIcons name="shopping-cart" size={24} color={textColor} />
            {cart.length > 0 && (
              <View style={[styles.cartBadge, { backgroundColor: primaryColor }]}>
                <Text style={styles.cartBadgeText}>
                  {cart.reduce((total, item) => total + item.quantity, 0)}
                </Text>
              </View>
            )}
          </Pressable>
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Category Banner */}
        {banners.length > 0 && (
          <View style={styles.bannerContainer}>
            <Image
              source={{ uri: banners[0].banner_url }}
              style={styles.categoryBanner}
              resizeMode="cover"
            />
            {banners[0].title && (
              <View style={styles.bannerOverlay}>
                <Text style={styles.bannerTitle}>{banners[0].title}</Text>
                {banners[0].description && (
                  <Text style={styles.bannerDescription}>{banners[0].description}</Text>
                )}
              </View>
            )}
          </View>
        )}



        {/* Brand Filter */}
        {brands.length > 0 && (
          <View style={styles.brandFilterContainer}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <Pressable
                style={[
                  styles.brandButton,
                  { backgroundColor: selectedBrand === '' ? primaryColor : cardBackground }
                ]}
                onPress={() => setSelectedBrand('')}
              >
                <Text style={[
                  styles.brandButtonText,
                  { color: selectedBrand === '' ? '#ffffff' : textColor }
                ]}>
                  All Brands
                </Text>
              </Pressable>
              {brands.map((brand) => (
                <Pressable
                  key={brand}
                  style={[
                    styles.brandButton,
                    { backgroundColor: selectedBrand === brand ? primaryColor : cardBackground }
                  ]}
                  onPress={() => setSelectedBrand(brand)}
                >
                  <Text style={[
                    styles.brandButtonText,
                    { color: selectedBrand === brand ? '#ffffff' : textColor }
                  ]}>
                    {brand}
                  </Text>
                </Pressable>
              ))}
            </ScrollView>
          </View>
        )}

        {/* Products Grid */}
        <View style={styles.productsContainer}>
          <FlatList
            data={filteredProducts}
            renderItem={renderProductItem}
            keyExtractor={(item) => item.id.toString()}
            numColumns={2}
            columnWrapperStyle={styles.productRow}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginHorizontal: 16,
  },
  headerRight: {
    width: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cartButton: {
    padding: 8,
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: 2,
    right: 2,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  cartBadgeText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  bannerContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  categoryBanner: {
    width: '100%',
    height: 200,
  },
  bannerOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 16,
  },
  bannerTitle: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  bannerDescription: {
    color: '#ffffff',
    fontSize: 14,
    opacity: 0.9,
  },

  brandFilterContainer: {
    marginBottom: 16,
    paddingLeft: 16,
  },
  brandButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  brandButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  productsContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  productRow: {
    justifyContent: 'space-between',
  },
  productCard: {
    width: (width - 48) / 2,
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  productImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    marginBottom: 8,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  productBrand: {
    fontSize: 12,
    marginBottom: 6,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  price: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
  },
  originalPrice: {
    fontSize: 12,
    textDecorationLine: 'line-through',
  },
  unit: {
    fontSize: 11,
    marginBottom: 8,
  },
  addButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
    alignItems: 'center',
    width: '100%',
    height: 36,
  },
  addButtonText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
  },
  quantityCounter: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 6,
    overflow: 'hidden',
    width: '100%',
    height: 36, // Same height as addButton
  },
  quantityButton: {
    paddingVertical: 8,
    paddingHorizontal: 8,
    alignItems: 'center',
    justifyContent: 'center',
    flex: 0.3, // Takes 30% of the width on each side
    height: '100%',
  },
  quantityButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  quantityText: {
    flex: 0.4, // Takes 40% of the width in the center
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
    paddingVertical: 8,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
});
