#!/usr/bin/env node

/**
 * <PERSON>ript to automatically bump version and submit to Google Play Store
 * Usage: node scripts/submit-android.js
 */

const { execSync } = require('child_process');
const { bumpAndroidVersionCode } = require('./bump-version');

async function submitToPlayStore() {
  console.log('🚀 Starting Android submission process...\n');
  
  try {
    // Step 1: Bump version code
    console.log('📋 Step 1: Bumping Android version code...');
    const bumpResult = bumpAndroidVersionCode();
    
    if (!bumpResult.success) {
      throw new Error(`Failed to bump version: ${bumpResult.error}`);
    }
    
    console.log('\n📋 Step 2: Building production app...');
    console.log('⏳ This may take several minutes...');
    
    // Step 2: Build the app
    try {
      execSync('eas build --platform android --profile production --non-interactive', {
        stdio: 'inherit',
        cwd: process.cwd()
      });
      console.log('✅ Build completed successfully!\n');
    } catch (buildError) {
      console.error('❌ Build failed:', buildError.message);
      throw buildError;
    }
    
    console.log('📋 Step 3: Submitting to Google Play Store...');
    
    // Step 3: Submit to Play Store
    try {
      execSync('eas submit --platform android --non-interactive', {
        stdio: 'inherit',
        cwd: process.cwd()
      });
      console.log('✅ Submission completed successfully!');
    } catch (submitError) {
      console.error('❌ Submission failed:', submitError.message);
      throw submitError;
    }
    
    console.log('\n🎉 Successfully submitted to Google Play Store!');
    console.log(`📱 Version: ${bumpResult.newVersionCode}`);
    console.log('📝 Check your Google Play Console for the submission status.');
    
  } catch (error) {
    console.error('\n❌ Submission process failed:', error.message);
    console.log('\n🔧 Troubleshooting tips:');
    console.log('1. Make sure you have EAS CLI installed: npm install -g @expo/eas-cli');
    console.log('2. Ensure you\'re logged in: eas login');
    console.log('3. Check your service account key file exists');
    console.log('4. Verify your eas.json configuration');
    process.exit(1);
  }
}

// If called directly, run the submission
if (require.main === module) {
  submitToPlayStore();
}

module.exports = { submitToPlayStore }; 