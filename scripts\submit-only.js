#!/usr/bin/env node

/**
 * Script to bump version and submit existing build to Google Play Store
 * Usage: node scripts/submit-only.js
 * 
 * This script assumes you already have a recent build available
 */

const { execSync } = require('child_process');
const { bumpAndroidVersionCode } = require('./bump-version');

async function submitOnly() {
  console.log('🚀 Starting Android submission (without rebuild)...\n');
  
  try {
    // Step 1: Bump version code
    console.log('📋 Step 1: Bumping Android version code...');
    const bumpResult = bumpAndroidVersionCode();
    
    if (!bumpResult.success) {
      throw new Error(`Failed to bump version: ${bumpResult.error}`);
    }
    
    console.log('\n📋 Step 2: Submitting to Google Play Store...');
    console.log('📝 Using the latest available build...');
    
    // Step 2: Submit to Play Store
    try {
      execSync('eas submit --platform android --non-interactive', {
        stdio: 'inherit',
        cwd: process.cwd()
      });
      console.log('✅ Submission completed successfully!');
    } catch (submitError) {
      console.error('❌ Submission failed:', submitError.message);
      console.log('\n💡 If no recent build is available, run: npm run submit-android');
      throw submitError;
    }
    
    console.log('\n🎉 Successfully submitted to Google Play Store!');
    console.log(`📱 Version Code: ${bumpResult.newVersionCode}`);
    console.log('📝 Check your Google Play Console for the submission status.');
    
  } catch (error) {
    console.error('\n❌ Submission process failed:', error.message);
    console.log('\n🔧 Troubleshooting tips:');
    console.log('1. Make sure you have a recent build: npm run build-android');
    console.log('2. Ensure you\'re logged in: eas login');
    console.log('3. Check your service account key file exists');
    console.log('4. For a complete build + submit process, use: npm run submit-android');
    process.exit(1);
  }
}

// If called directly, run the submission
if (require.main === module) {
  submitOnly();
}

module.exports = { submitOnly }; 