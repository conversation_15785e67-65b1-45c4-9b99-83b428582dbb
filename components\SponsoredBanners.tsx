import { supabase } from '@/lib/supabase';
import React, { useEffect, useRef, useState } from 'react';
import {
    ActivityIndicator,
    Dimensions,
    Image,
    Linking,
    Pressable,
    ScrollView,
    StyleSheet,
    Text,
    View
} from 'react-native';

const { width } = Dimensions.get('window');

interface SponsoredBanner {
  id: number;
  title: string;
  description: string;
  image_url: string;
  banner_url: string;
  display_order: number;
  is_active: boolean;
}

export default function SponsoredBanners() {
  const [banners, setBanners] = useState<SponsoredBanner[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const scrollViewRef = useRef<ScrollView>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const primaryColor = '#f97316';
  const secondaryTextColor = '#666';

  // Load sponsored banners
  const loadBanners = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('sponsored_banners')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      if (error) {
        console.error('Error loading sponsored banners:', error);
        return;
      }

      setBanners(data || []);
    } catch (error) {
      console.error('Error loading sponsored banners:', error);
    } finally {
      setLoading(false);
    }
  };

  // Auto-scroll functionality
  const startAutoScroll = () => {
    if (banners.length <= 1) return;

    intervalRef.current = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % banners.length;
        scrollViewRef.current?.scrollTo({
          x: nextIndex * (width - 42 + 16),
          animated: true,
        });
        return nextIndex;
      });
    }, 3000); // Change banner every 3 seconds
  };

  const stopAutoScroll = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  // Handle manual scroll
  const handleScroll = (event: any) => {
    const scrollPosition = event.nativeEvent.contentOffset.x;
    const index = Math.round(scrollPosition / (width - 42 + 8));
    setCurrentIndex(index);
  };

  // Handle banner press
  const handleBannerPress = async (banner: SponsoredBanner) => {
    if (banner.banner_url) {
      try {
        const supported = await Linking.canOpenURL(banner.banner_url);
        if (supported) {
          await Linking.openURL(banner.banner_url);
        }
      } catch (error) {
        console.error('Error opening banner URL:', error);
      }
    }
  };

  useEffect(() => {
    loadBanners();
  }, []);

  useEffect(() => {
    if (banners.length > 1) {
      startAutoScroll();
    }

    return () => {
      stopAutoScroll();
    };
  }, [banners]);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={primaryColor} />
        <Text style={[styles.loadingText, { color: secondaryTextColor }]}>
          Loading banners...
        </Text>
      </View>
    );
  }

  if (banners.length === 0) {
    return (
      <View style={styles.placeholderContainer}>
        <Text style={styles.placeholderText}>No sponsored banners available</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        ref={scrollViewRef}
        horizontal
        pagingEnabled={false}
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        onTouchStart={stopAutoScroll}
        onTouchEnd={startAutoScroll}
        style={styles.scrollView}
        snapToInterval={width - 42 + 8}
        snapToAlignment="start"
        decelerationRate="fast"
      >
        {banners.map((banner, index) => (
          <Pressable
            key={banner.id}
            style={[styles.bannerContainer, index < banners.length - 1 && styles.bannerMargin]}
            onPress={() => handleBannerPress(banner)}
            android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
          >
            <Image
              source={{ uri: banner.image_url }}
              style={styles.bannerImage}
              resizeMode="cover"
            />
            <View style={styles.gradientOverlay} />
            <View style={styles.contentContainer}>
              <View style={styles.textContainer}>
                <Text style={styles.bannerTitle} numberOfLines={1}>
                  {banner.title}
                </Text>
                <Text style={styles.bannerDescription} numberOfLines={1}>
                  {banner.description}
                </Text>
              </View>
            </View>
          </Pressable>
        ))}
      </ScrollView>

      {/* Indicators */}
      {banners.length > 1 && (
        <View style={styles.indicatorContainer}>
          {banners.map((_, index) => (
            <View
              key={index}
              style={[
                styles.indicator,
                {
                  backgroundColor: index === currentIndex ? primaryColor : '#ddd',
                  width: index === currentIndex ? 20 : 8,
                }
              ]}
            />
          ))}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  loadingContainer: {
    height: 150,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    marginBottom: 16,
    marginHorizontal: 16,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
  },
  placeholderContainer: {
    height: 150,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    marginBottom: 16,
    marginHorizontal: 16,
  },
  placeholderText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
  scrollView: {
  },
  bannerContainer: {
    width: width - 42,
    height: 150,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  bannerMargin: {
    marginRight: 16,
  },
  bannerImage: {
    width: '100%',
    height: '100%',
  },
  gradientOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  contentContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  textContainer: {
    flex: 1,
  },
  bannerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 2,
  },
  bannerDescription: {
    fontSize: 12,
    color: '#fff',
    opacity: 0.9,
  },
  indicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 12,
  },
  indicator: {
    height: 8,
    borderRadius: 4,
    marginHorizontal: 3,
  },
});
