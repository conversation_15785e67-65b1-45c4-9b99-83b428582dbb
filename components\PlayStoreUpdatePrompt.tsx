import React from 'react';
import {
  View,
  Text,
  Modal,
  Pressable,
  StyleSheet,
  Dimensions,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useColorScheme } from '@/hooks/useColorScheme';
import { UpdateInfo } from '@/lib/playStoreUpdates';

interface PlayStoreUpdatePromptProps {
  visible: boolean;
  updateInfo: UpdateInfo;
  onUpdate: () => void;
  onDismiss?: () => void;
}

const { width: screenWidth } = Dimensions.get('window');

export function PlayStoreUpdatePrompt({
  visible,
  updateInfo,
  onUpdate,
  onDismiss,
}: PlayStoreUpdatePromptProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const colors = {
    background: isDark ? '#1a1a1a' : '#ffffff',
    text: isDark ? '#ffffff' : '#000000',
    textSecondary: isDark ? '#a1a1a1' : '#6b7280',
    border: isDark ? '#333333' : '#e5e7eb',
    overlay: 'rgba(0, 0, 0, 0.5)',
    primary: '#f97316',
    success: '#10b981',
    warning: '#f59e0b',
  };

  const isImmediate = updateInfo.updateType === 'immediate' || updateInfo.mandatory;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      statusBarTranslucent
    >
      <View style={[styles.overlay, { backgroundColor: colors.overlay }]}>
        <View style={[styles.container, { backgroundColor: colors.background }]}>
          {/* Header */}
          <View style={styles.header}>
            <View style={[styles.iconContainer, { backgroundColor: isImmediate ? colors.warning + '20' : colors.primary + '20' }]}>
              <Ionicons
                name={isImmediate ? "warning" : "download"}
                size={32}
                color={isImmediate ? colors.warning : colors.primary}
              />
            </View>
            <Text style={[styles.title, { color: colors.text }]}>
              {isImmediate ? 'Update Required' : 'Update Available'}
            </Text>
            <Text style={[styles.version, { color: colors.textSecondary }]}>
              Version {updateInfo.version}
            </Text>
          </View>

          {/* Content */}
          <View style={styles.content}>
            {isImmediate ? (
              <Text style={[styles.description, { color: colors.textSecondary }]}>
                This update is required to continue using the app. Please update now to access all features.
              </Text>
            ) : (
              <Text style={[styles.description, { color: colors.textSecondary }]}>
                A new version is available with improvements and bug fixes. Update now for the best experience.
              </Text>
            )}

            {updateInfo.releaseNotes && (
              <View style={styles.releaseNotesContainer}>
                <Text style={[styles.releaseNotesTitle, { color: colors.text }]}>
                  What's New:
                </Text>
                <Text style={[styles.releaseNotes, { color: colors.textSecondary }]}>
                  {updateInfo.releaseNotes}
                </Text>
              </View>
            )}
          </View>

          {/* Actions */}
          <View style={styles.actions}>
            {!isImmediate && onDismiss && (
              <Pressable
                style={[styles.button, styles.secondaryButton, { borderColor: colors.border }]}
                onPress={onDismiss}
              >
                <Text style={[styles.buttonText, { color: colors.textSecondary }]}>
                  Later
                </Text>
              </Pressable>
            )}

            <Pressable
              style={[styles.button, styles.primaryButton]}
              onPress={onUpdate}
            >
              <LinearGradient
                colors={['#f97316', '#dc2626']}
                style={styles.gradientButton}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <Ionicons name="download" size={20} color="#ffffff" />
                <Text style={[styles.buttonText, { color: '#ffffff', marginLeft: 8 }]}>
                  {isImmediate ? 'Update Now' : 'Update'}
                </Text>
              </LinearGradient>
            </Pressable>
          </View>

          {/* Update Type Indicator */}
          <View style={styles.updateTypeContainer}>
            <View style={[
              styles.updateTypeBadge,
              { backgroundColor: isImmediate ? colors.warning + '20' : colors.success + '20' }
            ]}>
              <Text style={[
                styles.updateTypeText,
                { color: isImmediate ? colors.warning : colors.success }
              ]}>
                {isImmediate ? 'Immediate Update' : 'Flexible Update'}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  container: {
    width: Math.min(screenWidth - 40, 400),
    borderRadius: 20,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 4,
  },
  version: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  content: {
    marginBottom: 24,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 16,
  },
  releaseNotesContainer: {
    backgroundColor: 'rgba(249, 115, 22, 0.1)',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#f97316',
  },
  releaseNotesTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  releaseNotes: {
    fontSize: 14,
    lineHeight: 20,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
  },
  button: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  secondaryButton: {
    borderWidth: 1,
    paddingVertical: 16,
    paddingHorizontal: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  primaryButton: {
    flex: isImmediate ? 1 : 1.5,
  },
  gradientButton: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  updateTypeContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  updateTypeBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  updateTypeText: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
});

// Helper function to determine if immediate update is needed
const isImmediate = false; // This will be determined by the updateInfo prop
