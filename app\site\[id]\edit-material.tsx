import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { supabase } from '@/lib/supabase';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import * as ImagePicker from 'expo-image-picker';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Image,
  KeyboardAvoidingView,
  Modal,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  useColorScheme,
  View
} from 'react-native';

// Define UOM options
const UOM_OPTIONS = [
  'No.s',
  'Box',
  'Each',
  'Meter',
  'Feet',
  'Running Feet',
  'Square Meter',
  'Square Feet',
  'Tones',
  'Liter',
  'Cubic Meter',
  'Cubic Feet',
  'Kilogram',
  'Bags',
  'Other'
];

type Material = {
  id: string;
  name: string;
  specifications: string | null;
  stock_level: number;
  minimum_stock_level: number;
  unit_of_measure: string;
  price: number;
  category: string | null;
  received_date: string;
  created_at: string;
  updated_at: string;
  site_id: string;
  invoice_number: string | null;
  invoice_image_url: string | null;
  used_stock?: number;
};

export default function EditMaterialScreen() {
  const { id, materialId } = useLocalSearchParams();
  const colorScheme = useColorScheme();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showImageModal, setShowImageModal] = useState(false);
  
  // Form state
  const [materialName, setMaterialName] = useState('');
  const [specifications, setSpecifications] = useState('');
  const [stockLevel, setStockLevel] = useState('');
  const [minimumStock, setMinimumStock] = useState('');
  const [unitOfMeasure, setUnitOfMeasure] = useState('');
  const [price, setPrice] = useState('');
  const [category, setCategory] = useState('');
  const [receivedDate, setReceivedDate] = useState(new Date());
  const [invoiceNumber, setInvoiceNumber] = useState('');
  const [invoiceImage, setInvoiceImage] = useState<string | null>(null);
  const [existingInvoiceUrl, setExistingInvoiceUrl] = useState<string | null>(null);
  const [stockUpdate, setStockUpdate] = useState('0');
  const [previousUsed, setPreviousUsed] = useState('0');
  const [imageLoading, setImageLoading] = useState(false);
  const [imageError, setImageError] = useState(false);
  
  // Calculated values
  const totalStockLevel = parseFloat(stockLevel) || 0;
  const previouslyUsedStock = parseFloat(previousUsed) || 0;
  const newUsedStock = parseFloat(stockUpdate) || 0;
  const totalUsedStock = previouslyUsedStock + newUsedStock;
  const balanceStockLevel = totalStockLevel - totalUsedStock;
  
  // Load material data
  useEffect(() => {
    const loadMaterial = async () => {
      try {
        setLoading(true);
        
        // Check permissions first
        const { data: userData } = await supabase.auth.getUser();
        if (!userData.user) {
          router.replace('/');
          return;
        }
        
        const { data: memberData, error: memberError } = await supabase
          .from('site_members')
          .select('role')
          .eq('site_id', id)
          .eq('user_id', userData.user.id)
          .single();
        
        if (memberError || !memberData) {
          console.error('Error checking permissions:', memberError);
          Alert.alert('Access Denied', 'You do not have permission to access this site.');
          router.back();
          return;
        }
        
        if (memberData.role !== 'Admin' && memberData.role !== 'Super Admin') {
          Alert.alert('Permission Denied', 'You need Admin or Super Admin privileges to edit materials.');
          router.back();
          return;
        }
        
        // Load material data
        const { data: materialData, error: materialError } = await supabase
          .from('materials')
          .select('*')
          .eq('id', materialId)
          .eq('site_id', id)
          .single();
        
        if (materialError || !materialData) {
          console.error('Error loading material:', materialError);
          Alert.alert('Error', 'Failed to load material data.');
          router.back();
          return;
        }
        
        // Populate form fields
        setMaterialName(materialData.name);
        setSpecifications(materialData.specifications || '');
        setStockLevel(materialData.stock_level.toString());
        setMinimumStock(materialData.minimum_stock_level.toString());
        setUnitOfMeasure(materialData.unit_of_measure);
        setPrice(materialData.price.toString());
        setCategory(materialData.category || '');
        setReceivedDate(new Date(materialData.received_date));
        setInvoiceNumber(materialData.invoice_number || '');
        
        // Handle used_stock field, default to 0 if not present
        setPreviousUsed(materialData.used_stock?.toString() || '0');
        setStockUpdate('0'); // Always start with 0 for new updates
        
        // Store the image URL path for the useEffect to process
        if (materialData.invoice_image_url) {
          setExistingInvoiceUrl(materialData.invoice_image_url);
        }
      } catch (error) {
        console.error('Error in loadMaterial:', error);
        Alert.alert('Error', 'Something went wrong while loading material data.');
        router.back();
      } finally {
        setLoading(false);
      }
    };
    
    loadMaterial();
  }, [id, materialId]);
  
  // Effect to handle invoice image URL changes
  useEffect(() => {
    if (existingInvoiceUrl) {
      console.log('Processing existing invoice URL:', existingInvoiceUrl);
      try {
        // Reset error state when attempting to load a new image
        setImageError(false);
        
        // If the URL includes 'materials/' but doesn't have the full path, it needs to be fixed
        if (existingInvoiceUrl.includes('materials/') && !existingInvoiceUrl.startsWith('http')) {
          // Get full public URL from Supabase
          const { data } = supabase.storage
            .from('material-images')
            .getPublicUrl(existingInvoiceUrl);
          
          if (data?.publicUrl) {
            console.log('Setting public URL:', data.publicUrl);
            setInvoiceImage(data.publicUrl);
          } else {
            console.error('No public URL generated');
            setImageError(true);
          }
        } else if (existingInvoiceUrl.startsWith('http')) {
          // Already a full URL
          setInvoiceImage(existingInvoiceUrl);
        } else {
          // Handle other cases - might need fixing
          console.log('URL might need different handling:', existingInvoiceUrl);
          setImageError(true);
        }
      } catch (error) {
        console.error('Error generating public URL:', error);
        setImageError(true);
      }
    }
  }, [existingInvoiceUrl]);
  
  // Handle date change
  const onDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setReceivedDate(selectedDate);
    }
  };
  
  // Show date picker
  const showDatePickerModal = () => {
    setShowDatePicker(true);
  };
  
  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString();
  };

  // Validate image before selecting it
  const validateImage = (asset: any): boolean => {
    // Check if the image is too large (> 5MB)
    const MAX_SIZE = 5 * 1024 * 1024; // 5MB in bytes
    if (asset.fileSize && asset.fileSize > MAX_SIZE) {
      Alert.alert(
        'Image Too Large',
        'The selected image is too large (over 5MB). Please select a smaller image.',
        [{ text: 'OK' }]
      );
      return false;
    }
    
    return true;
  };

  // Handle invoice image selection
  const selectInvoiceImage = async () => {
    try {
      // Request permissions
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please allow access to your photo library to upload images.');
        return;
      }

      // Launch image picker with better options
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: "images",
        allowsEditing: true,
        quality: 0.7, // Reduced quality to prevent large files
        aspect: [4, 3],
        base64: false,
        exif: false
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        
        // Validate the image before setting it
        if (validateImage(asset)) {
          setInvoiceImage(asset.uri);
        }
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  // Handle camera capture
  const captureInvoiceImage = async () => {
    try {
      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please allow access to your camera to capture images.');
        return;
      }

      // Launch camera with better options
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: "images",
        allowsEditing: true,
        quality: 0.7,
        aspect: [4, 3],
        base64: false,
        exif: false
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        
        // Validate the image before setting it
        if (validateImage(asset)) {
          setInvoiceImage(asset.uri);
        }
      }
    } catch (error) {
      console.error('Error capturing image:', error);
      Alert.alert('Error', 'Failed to capture image. Please try again.');
    }
  };

  // Show image picker options with view option
  const showImagePickerOptions = () => {
    if (invoiceImage) {
      Alert.alert(
        'Invoice Image',
        'Choose an option',
        [
          { text: 'View Image', onPress: () => setShowImageModal(true) },
          { text: 'Change Image', onPress: showChangeImageOptions },
          { text: 'Cancel', style: 'cancel' }
        ]
      );
    } else {
      showChangeImageOptions();
    }
  };

  // Show change image options
  const showChangeImageOptions = () => {
    Alert.alert(
      'Upload Invoice Image',
      'Choose an option',
      [
        { text: 'Camera', onPress: captureInvoiceImage },
        { text: 'Gallery', onPress: selectInvoiceImage },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };
  
  // Image load handlers
  const handleImageLoadStart = () => {
    setImageLoading(true);
    setImageError(false);
  };

  const handleImageLoadSuccess = () => {
    setImageLoading(false);
    setImageError(false);
  };

  const handleImageLoadError = () => {
    console.error('Image failed to load:', invoiceImage);
    setImageLoading(false);
    setImageError(true);
  };
  
  // Handle form submission
  const handleSubmit = async () => {
    // Validate form
    if (!materialName.trim()) {
      Alert.alert('Error', 'Material name is required.');
      return;
    }
    
    if (!unitOfMeasure.trim()) {
      Alert.alert('Error', 'Unit of measure is required.');
      return;
    }
    
    // Convert numeric fields
    const totalStockNum = parseFloat(stockLevel) || 0;
    const minimumStockNum = parseFloat(minimumStock) || 0;
    const priceNum = parseFloat(price) || 0;
    const previouslyUsedStockNum = parseFloat(previousUsed) || 0;
    const newUsedStockNum = parseFloat(stockUpdate) || 0;
    const totalUsedStockNum = previouslyUsedStockNum + newUsedStockNum;
    
    if (totalStockNum < 0) {
      Alert.alert('Error', 'Total stock level cannot be negative.');
      return;
    }
    
    if (minimumStockNum < 0) {
      Alert.alert('Error', 'Minimum stock level cannot be negative.');
      return;
    }
    
    if (priceNum < 0) {
      Alert.alert('Error', 'Price cannot be negative.');
      return;
    }
    
    // Ensure used stock doesn't exceed total stock
    if (totalUsedStockNum > totalStockNum) {
      Alert.alert('Error', 'Used stock cannot exceed total stock level.');
      return;
    }
    
    setSaving(true);
    
    try {
      // Get current user
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) {
        Alert.alert('Error', 'User not authenticated.');
        setSaving(false);
        return;
      }
      
      // Format date for database
      const formattedDate = receivedDate.toISOString().split('T')[0];
      
      // Upload invoice image if a new one is provided
      let invoiceImageUrl = existingInvoiceUrl;
      if (invoiceImage && invoiceImage !== existingInvoiceUrl && !invoiceImage.startsWith('https://vsnhscndlifvaptwdfsw.supabase.co/storage/v1/object/public/material-images/')) {
        try {
          console.log('Starting image upload with URI:', invoiceImage);
          const fileExt = invoiceImage.split('.').pop()?.toLowerCase() || 'jpg';
          const fileName = `${userData.user.id}-${Date.now()}.${fileExt}`;
          const filePath = `materials/${fileName}`;
          
          // Check if the image URI is valid
          let imageUri = invoiceImage;
          if (!imageUri.startsWith('file://') && !imageUri.startsWith('content://')) {
            console.log('Adding file:// prefix to URI');
            imageUri = `file://${imageUri}`;
          }
          
          // Use ReactNativeBlobUtil or similar library for more reliable uploads
          // For this fix, we'll improve the existing fetch approach
          console.log('Fetching image from URI');
          const response = await fetch(imageUri);
          
          if (!response.ok) {
            throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
          }
          
          console.log('Converting response to blob');
          const blob = await response.blob();
          console.log('Blob size:', blob.size);
          
          // Simple upload to the material-images bucket
          console.log('Uploading to Supabase storage');
          const { data, error } = await supabase.storage
            .from('material-images')
            .upload(filePath, blob, {
              contentType: `image/${fileExt === 'jpg' ? 'jpeg' : fileExt}`,
              upsert: true // Overwrite if exists
            });
            
          if (error) {
            console.error('Supabase storage error:', error);
            // Continue without the image
            Alert.alert(
              'Image Upload Issue',
              'Could not upload the new image. Keeping the existing image.',
              [{ text: 'Continue' }]
            );
          } else {
            // Store just the path, not the full URL
            invoiceImageUrl = filePath;
            console.log('Upload successful, file path:', filePath);
          }
        } catch (error) {
          console.error('Error processing image:', error);
          Alert.alert(
            'Image Upload Issue',
            'Could not process the new image. Keeping the existing image.',
            [{ text: 'Continue' }]
          );
        }
      }
      
      // Update material - keep original stock level but update used stock
      const { error } = await supabase
        .from('materials')
        .update({
          name: materialName.trim(),
          specifications: specifications.trim() || null,
          stock_level: totalStockNum,
          used_stock: totalUsedStockNum,
          minimum_stock_level: minimumStockNum,
          unit_of_measure: unitOfMeasure.trim(),
          price: priceNum,
          category: category.trim() || null,
          received_date: formattedDate,
          updated_at: new Date().toISOString(),
          invoice_number: invoiceNumber.trim() || null,
          // Only store the storage path, not the full URL
          invoice_image_url: invoiceImageUrl?.includes('https://vsnhscndlifvaptwdfsw.supabase.co/storage/v1/object/public/material-images/') 
            ? invoiceImageUrl.replace('https://vsnhscndlifvaptwdfsw.supabase.co/storage/v1/object/public/material-images/', '')
            : invoiceImageUrl
        })
        .eq('id', materialId)
        .eq('site_id', id);
      
      if (error) {
        console.error('Error updating material:', error);
        Alert.alert('Error', 'Failed to update material.');
        setSaving(false);
        return;
      }
      
      Alert.alert(
        'Success',
        'Material updated successfully!',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    } catch (error) {
      console.error('Error in handleSubmit:', error);
      Alert.alert('Error', 'Something went wrong while updating the material.');
    } finally {
      setSaving(false);
    }
  };
  
  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#f97316" />
      </ThemedView>
    );
  }
  
  return (
    <ThemedView style={styles.container}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      
      <Stack.Screen
        options={{
          title: 'Edit Material',
          headerBackTitle: 'Cancel',
        }}
      />
      
      {/* Full Screen Image Modal */}
      <Modal
        animationType="fade"
        transparent={false}
        visible={showImageModal}
        onRequestClose={() => setShowImageModal(false)}
      >
        <View style={styles.modalContainer}>
          <TouchableOpacity 
            style={styles.closeButton}
            onPress={() => setShowImageModal(false)}
          >
            <MaterialIcons name="close" size={28} color="#fff" />
          </TouchableOpacity>
          
          <View style={styles.modalImageContainer}>
            {imageLoading && (
              <ActivityIndicator size="large" color="#f97316" style={styles.modalLoader} />
            )}
            {!imageError ? (
              <Image
                source={{ uri: invoiceImage || '' }}
                style={styles.modalImage}
                resizeMode="contain"
                onLoadStart={handleImageLoadStart}
                onLoad={handleImageLoadSuccess}
                onError={handleImageLoadError}
              />
            ) : (
              <View style={styles.modalErrorContainer}>
                <MaterialIcons name="broken-image" size={60} color="#ef4444" />
                <ThemedText style={styles.modalErrorText}>Failed to load image</ThemedText>
                <TouchableOpacity
                  style={styles.retryButton}
                  onPress={() => {
                    setImageError(false);
                    // Force reload
                    if (existingInvoiceUrl) {
                      const { data } = supabase.storage
                        .from('material-images')
                        .getPublicUrl(existingInvoiceUrl);
                      
                      if (data?.publicUrl) {
                        setInvoiceImage(data.publicUrl);
                      }
                    }
                  }}
                >
                  <ThemedText style={styles.retryButtonText}>Retry Loading</ThemedText>
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>
      </Modal>
      
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollViewContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.formSection}>
            <ThemedText style={styles.sectionTitle}>Material Information</ThemedText>
            
            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Name *</ThemedText>
              <TextInput
                style={styles.input}
                value={materialName}
                onChangeText={setMaterialName}
                placeholder="Material name"
                placeholderTextColor="#94a3b8"
              />
            </View>
            
            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Specifications</ThemedText>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={specifications}
                onChangeText={setSpecifications}
                placeholder="Size, description, or other details"
                placeholderTextColor="#94a3b8"
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>
            
            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Category</ThemedText>
              <TextInput
                style={styles.input}
                value={category}
                onChangeText={setCategory}
                placeholder="Category (optional)"
                placeholderTextColor="#94a3b8"
              />
            </View>
          </View>
          
          <View style={styles.formSection}>
            <ThemedText style={styles.sectionTitle}>Inventory Details</ThemedText>
            
            <View style={styles.rowContainer}>
              <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
                <ThemedText style={styles.inputLabel}>Total Stock Level *</ThemedText>
                <TextInput
                  style={styles.input}
                  value={stockLevel}
                  onChangeText={setStockLevel}
                  placeholder="0"
                  placeholderTextColor="#94a3b8"
                  keyboardType="numeric"
                />
              </View>
              
              <View style={[styles.inputContainer, { flex: 1, marginLeft: 8 }]}>
                <ThemedText style={styles.inputLabel}>Minimum Stock</ThemedText>
                <TextInput
                  style={styles.input}
                  value={minimumStock}
                  onChangeText={setMinimumStock}
                  placeholder="0"
                  placeholderTextColor="#94a3b8"
                  keyboardType="numeric"
                />
              </View>
            </View>
            
            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Unit of Measure *</ThemedText>
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={unitOfMeasure}
                  onValueChange={(itemValue: string) => setUnitOfMeasure(itemValue)}
                  style={styles.picker}
                >
                  {UOM_OPTIONS.map((option: string) => (
                    <Picker.Item key={option} label={option} value={option} />
                  ))}
                </Picker>
              </View>
            </View>
            
            <View style={styles.rowContainer}>
              <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
                <ThemedText style={styles.inputLabel}>Price (optional)</ThemedText>
                <View style={styles.priceInputContainer}>
                  <ThemedText style={styles.currencySymbol}>₹</ThemedText>
                  <TextInput
                    style={styles.priceInput}
                    value={price}
                    onChangeText={setPrice}
                    placeholder="0.00"
                    placeholderTextColor="#94a3b8"
                    keyboardType="numeric"
                  />
                </View>
              </View>
              
              <View style={[styles.inputContainer, { flex: 1, marginLeft: 8 }]}>
                <ThemedText style={styles.inputLabel}>Received Date</ThemedText>
                <TouchableOpacity 
                  style={styles.datePickerButton}
                  onPress={showDatePickerModal}
                >
                  <ThemedText style={styles.datePickerText}>
                    {formatDate(receivedDate)}
                  </ThemedText>
                  <MaterialIcons name="calendar-today" size={18} color="#64748b" />
                </TouchableOpacity>
                
                {showDatePicker && (
                  <DateTimePicker
                    value={receivedDate}
                    mode="date"
                    display="default"
                    onChange={onDateChange}
                  />
                )}
              </View>
            </View>

            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Invoice Number (optional)</ThemedText>
              <TextInput
                style={styles.input}
                value={invoiceNumber}
                onChangeText={setInvoiceNumber}
                placeholder="Enter invoice number"
                placeholderTextColor="#94a3b8"
              />
            </View>

            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Invoice or Received Material Image (optional)</ThemedText>
              <TouchableOpacity
                style={styles.imagePickerButton}
                onPress={showImagePickerOptions}
              >
                {invoiceImage ? (
                  <View style={styles.imagePreviewContainer}>
                    {imageLoading && (
                      <View style={styles.imageLoadingOverlay}>
                        <ActivityIndicator size="large" color="#f97316" />
                      </View>
                    )}
                    {!imageError ? (
                      <Image 
                        source={{ uri: invoiceImage }} 
                        style={styles.imagePreview}
                        onLoadStart={handleImageLoadStart}
                        onLoad={handleImageLoadSuccess}
                        onError={handleImageLoadError}
                      />
                    ) : (
                      <View style={styles.imageErrorOverlay}>
                        <MaterialIcons name="broken-image" size={40} color="#ef4444" />
                        <ThemedText style={styles.imageErrorText}>Failed to load image</ThemedText>
                      </View>
                    )}
                    <View style={styles.imageActionButtons}>
                      <TouchableOpacity
                        style={styles.viewImageButton}
                        onPress={() => setShowImageModal(true)}
                      >
                        <MaterialIcons name="visibility" size={18} color="#fff" />
                        <ThemedText style={styles.actionButtonText}>View</ThemedText>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={styles.changeImageButton}
                        onPress={showChangeImageOptions}
                      >
                        <MaterialIcons name="edit" size={18} color="#fff" />
                        <ThemedText style={styles.actionButtonText}>Change</ThemedText>
                      </TouchableOpacity>
                    </View>
                  </View>
                ) : (
                  <View style={styles.imagePickerContent}>
                    <MaterialIcons name="add-photo-alternate" size={24} color="#64748b" />
                    <ThemedText style={styles.imagePickerText}>Upload Invoice Image</ThemedText>
                  </View>
                )}
              </TouchableOpacity>
            </View>
            
            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Update Used Stock</ThemedText>
              <TextInput
                style={styles.input}
                value={stockUpdate}
                onChangeText={setStockUpdate}
                placeholder="0"
                placeholderTextColor="#94a3b8"
                keyboardType="numeric"
              />
            </View>
          </View>
          
          <View style={styles.formSection}>
            <ThemedText style={styles.sectionTitle}>Stock Summary</ThemedText>
            
            <View style={styles.stockSummaryContainer}>
              <View style={styles.stockSummaryRow}>
                <ThemedText style={styles.stockSummaryLabel}>Total Stock:</ThemedText>
                <ThemedText style={styles.stockSummaryValue}>{totalStockLevel.toFixed(2)} {unitOfMeasure}</ThemedText>
              </View>
              
              <View style={styles.stockSummaryRow}>
                <ThemedText style={styles.stockSummaryLabel}>Previously Used Stock:</ThemedText>
                <ThemedText style={styles.stockSummaryValue}>{previouslyUsedStock.toFixed(2)} {unitOfMeasure}</ThemedText>
              </View>
              
              <View style={styles.stockSummaryRow}>
                <ThemedText style={styles.stockSummaryLabel}>New Used Stock:</ThemedText>
                <ThemedText style={styles.stockSummaryValue}>{newUsedStock.toFixed(2)} {unitOfMeasure}</ThemedText>
              </View>
              
              <View style={styles.stockSummaryRow}>
                <ThemedText style={styles.stockSummaryLabel}>Total Used Stock:</ThemedText>
                <ThemedText style={styles.stockSummaryValue}>{totalUsedStock.toFixed(2)} {unitOfMeasure}</ThemedText>
              </View>
              
              <View style={[styles.stockSummaryRow, styles.balanceRow]}>
                <ThemedText style={styles.stockSummaryLabel}>Balance Stock:</ThemedText>
                <ThemedText style={[
                  styles.stockSummaryValue, 
                  styles.balanceValue,
                  balanceStockLevel < 0 && styles.negativeBalance
                ]}>
                  {balanceStockLevel.toFixed(2)} {unitOfMeasure}
                </ThemedText>
              </View>
            </View>
          </View>
          
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.submitButton, saving && styles.submitButtonDisabled]}
              onPress={handleSubmit}
              disabled={saving}
            >
              {saving ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <>
                  <MaterialIcons name="save" size={18} color="#fff" />
                  <ThemedText style={styles.submitButtonText}>Save Changes</ThemedText>
                </>
              )}
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => router.back()}
              disabled={saving}
            >
              <MaterialIcons name="cancel" size={18} color="#64748b" />
              <ThemedText style={styles.cancelButtonText}>Cancel</ThemedText>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: 16,
    paddingBottom: 32,
  },
  formSection: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#0f172a',
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 4,
    color: '#64748b',
  },
  input: {
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#0f172a',
  },
  textArea: {
    minHeight: 80,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    padding: 12,
  },
  datePickerText: {
    fontSize: 16,
    color: '#0f172a',
  },
  pickerContainer: {
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    overflow: 'hidden',
  },
  picker: {
    height: 50,
    width: '100%',
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    padding: 12,
  },
  currencySymbol: {
    fontSize: 16,
    color: '#64748b',
    marginRight: 6,
  },
  priceInput: {
    flex: 1,
    fontSize: 16,
    color: '#0f172a',
    padding: 0,
  },
  imagePickerButton: {
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    overflow: 'hidden',
    height: 120,
  },
  imagePickerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imagePickerText: {
    marginTop: 8,
    fontSize: 14,
    color: '#64748b',
  },
  imagePreviewContainer: {
    flex: 1,
    position: 'relative',
  },
  imagePreview: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  imageLoadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  imageErrorOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(244,63,94,0.2)',
  },
  imageErrorText: {
    color: '#fff',
    marginTop: 8,
    fontSize: 14,
    fontWeight: 'bold',
  },
  imageActionButtons: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    flexDirection: 'row',
  },
  viewImageButton: {
    backgroundColor: 'rgba(59,130,246,0.8)',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderTopLeftRadius: 8,
    marginRight: 1,
  },
  changeImageButton: {
    backgroundColor: 'rgba(0,0,0,0.6)',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  stockSummaryContainer: {
    backgroundColor: '#f8fafc',
    borderRadius: 8,
    padding: 16,
    marginTop: 8,
  },
  stockSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 6,
  },
  stockSummaryLabel: {
    fontSize: 16,
    color: '#475569',
  },
  stockSummaryValue: {
    fontSize: 16,
    color: '#0f172a',
    fontWeight: '500',
  },
  balanceRow: {
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
    marginTop: 6,
    paddingTop: 12,
  },
  balanceValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#10b981',
  },
  negativeBalance: {
    color: '#ef4444',
  },
  buttonContainer: {
    marginVertical: 16,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f97316',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  submitButtonDisabled: {
    backgroundColor: '#fdba74',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  cancelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    padding: 16,
  },
  cancelButtonText: {
    color: '#64748b',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'center',
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    right: 20,
    zIndex: 10,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 20,
    padding: 8,
  },
  modalImageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalImage: {
    width: '100%',
    height: '100%',
  },
  modalLoader: {
    position: 'absolute',
    zIndex: 5,
  },
  modalErrorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalErrorText: {
    fontSize: 18,
    color: '#fff',
    marginTop: 16,
  },
  retryButton: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
}); 