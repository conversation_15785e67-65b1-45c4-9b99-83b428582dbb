import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useReportAccess } from '@/hooks/useReportAccess';
import {
    type Attendance,
    type Material,
    type Task,
    type TaskSubcategory
} from '@/lib/export';
import { canManageReportTemplates } from '@/lib/permissions';
import { supabase } from '@/lib/supabase';
import ReportsSection from '@/screens/SiteDetailScreen';
import { Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, ScrollView, StyleSheet, useColorScheme, View } from 'react-native';
import { Button } from 'react-native-paper';

export default function SiteReportsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const colorScheme = useColorScheme();
  
  const [site, setSite] = useState<{ id: string; name: string } | null>(null);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [attendance, setAttendance] = useState<Attendance[]>([]);
  const [materials, setMaterials] = useState<Material[]>([]);
  const [subcategories, setSubcategories] = useState<Record<string, TaskSubcategory[]>>({});
  const [loading, setLoading] = useState(true);
  
  // Check report access using our custom hook
  const { isLoading, hasAccess, userRole } = useReportAccess(id);
  
  // Load site data
  useEffect(() => {
    async function loadSiteData() {
      try {
        const { data: siteData, error } = await supabase
          .from('sites')
          .select('id, name')
          .eq('id', id)
          .single();
          
        if (error) throw error;
        setSite(siteData);
        
        // Load tasks
        const { data: tasksData } = await supabase
          .from('tasks')
          .select('*')
          .eq('site_id', id);
        
        setTasks(tasksData || []);
        
        // Load attendance with labor information
        const { data: attendanceData } = await supabase
          .from('attendance')
          .select(`
            id,
            laborer_id,
            site_id,
            attendance_date,
            status,
            created_at,
            updated_at,
            laborers:laborer_id (
              id,
              full_name,
              daily_wage,
              phone_number,
              created_at,
              updated_at
            )
          `)
          .eq('site_id', id);
          
        setAttendance(attendanceData || []);
        
        // Load materials
        const { data: materialsData } = await supabase
          .from('materials')
          .select('*')
          .eq('site_id', id);
          
        setMaterials(materialsData || []);
        
        // Load task subcategories
        const { data: subcategoriesData } = await supabase
          .from('task_subcategories')
          .select('*')
          .eq('site_id', id);
          
        // Group subcategories by task_id
        const subcategoriesMap: Record<string, TaskSubcategory[]> = {};
        
        subcategoriesData?.forEach(subcat => {
          if (!subcategoriesMap[subcat.task_id]) {
            subcategoriesMap[subcat.task_id] = [];
          }
          subcategoriesMap[subcat.task_id].push(subcat);
        });
        
        setSubcategories(subcategoriesMap);
        setLoading(false);
      } catch (error) {
        console.error('Error loading site data:', error);
        setLoading(false);
      }
    }
    
    if (hasAccess) {
      loadSiteData();
    }
  }, [id, hasAccess]);
  
  // Show loading state while checking permissions or loading data
  if (isLoading || loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#f97316" />
        <ThemedText style={styles.loadingText}>Loading reports...</ThemedText>
      </ThemedView>
    );
  }
  
  // This screen should only be accessible to users with report access
  // The hook will redirect if no access, but we add an extra check
  if (!hasAccess) {
    return null;
  }
  
  return (
    <ThemedView style={styles.container}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      
      <Stack.Screen 
        options={{
          title: 'Site Reports',
          headerRight: () => (
            canManageReportTemplates(userRole) ? (
              <Button 
                mode="contained" 
                compact 
                onPress={() => {
                  // Navigate to template management (would be implemented in a real app)
                  alert('Template management coming soon');
                }}
                style={styles.headerButton}
              >
                Manage
              </Button>
            ) : null
          ),
        }} 
      />
      
      <ScrollView style={styles.container}>
        {site && (
          <View style={styles.content}>
            <ThemedText style={styles.siteName}>{site.name} Reports</ThemedText>
            
            {/* Only render if site and data are loaded */}
            {site && (
              <ReportsSection 
                site={site}
                tasks={tasks}
                attendance={attendance}
                materials={materials}
                subcategories={subcategories}
              />
            )}
          </View>
        )}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  siteName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  headerButton: {
    marginRight: 8,
  },
}); 