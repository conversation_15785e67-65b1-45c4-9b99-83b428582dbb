# Quick Setup Guide: In-App Update Feature

## What's Been Implemented

✅ **Complete in-app update system** that checks for new versions and prompts users to update from Google Play Store

## Files Created/Modified

### New Files:
- `lib/inAppUpdate.ts` - Core update management logic
- `components/UpdatePrompt.tsx` - UI component for update notifications
- `database/create_app_versions_table.sql` - Database schema for version tracking
- `scripts/manage-app-version.js` - CLI tool for managing app versions
- `InAppUpdateGuide.md` - Comprehensive documentation

### Modified Files:
- `app/(tabs)/index.tsx` - Dashboard now includes update checking
- `package.json` - Added `expo-application` dependency

## Next Steps

### 1. Setup Database (Required)

Run this SQL in your Supabase dashboard:

```sql
-- Run the contents of database/create_app_versions_table.sql
-- This creates the app_versions table with proper RLS policies
```

### 2. Configure Environment Variables

Set these in your deployment environment:

```bash
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 3. Test the Feature

**Option A: Add a test version (higher version code):**
```bash
node scripts/manage-app-version.js add \
  --version "999.0.0" \
  --versionCode 999 \
  --platform android \
  --mandatory false \
  --notes "Test update - please ignore"
```

**Option B: Lower your current app version in `app.json` temporarily**

### 4. Production Usage

When you release version 1.1.0:

```bash
# Add new version to trigger updates
node scripts/manage-app-version.js add \
  --version "1.1.0" \
  --versionCode 2 \
  --platform android \
  --mandatory false \
  --notes "• Fixed transaction report bugs\n• Improved site management\n• Enhanced performance"

# Verify it's active
node scripts/manage-app-version.js list --platform android
```

## How It Works

1. **Dashboard Load**: When users open the dashboard, app checks for updates (max once per 24 hours)
2. **Version Compare**: Compares current version code with latest in database
3. **Show Prompt**: If update available, shows beautiful update prompt at top of dashboard
4. **Play Store**: "Update Now" button takes users directly to Google Play Store
5. **Smart Throttling**: Won't spam users - respects 24-hour check interval

## Features

- ✅ **Optional updates** - users can dismiss with "Later"
- ✅ **Mandatory updates** - users must update to continue
- ✅ **Beautiful UI** - matches your app's design with dark/light mode
- ✅ **Release notes** - show users what's new
- ✅ **Play Store integration** - direct deep links
- ✅ **Smart throttling** - respects user experience

## Troubleshooting

**No update prompt showing?**
- Check console for errors
- Verify database table exists
- Ensure 24+ hours passed since last check
- Use `updateManager.forceCheckForUpdates()` in debug

**Update button not working?**
- Verify package name is `com.infratasks.app`
- Test on device with Play Store installed

---

🎉 **Your app now has professional in-app update functionality!** 