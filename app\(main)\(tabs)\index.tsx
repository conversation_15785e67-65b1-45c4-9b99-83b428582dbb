import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import TransactionReportDownload from '@/components/TransactionReportDownload';
import UpdatePrompt from '@/components/UpdatePrompt';
import { useAuth } from '@/context/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { AppVersion, updateManager } from '@/lib/inAppUpdate';
import { supabase } from '@/lib/supabase';

import { FontAwesome5, MaterialCommunityIcons, MaterialIcons, Octicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { addDays, differenceInDays, format, isAfter, isBefore } from 'date-fns';
import * as FileSystem from 'expo-file-system';
import { LinearGradient } from 'expo-linear-gradient';
import * as Linking from 'expo-linking';
import * as Print from 'expo-print';
import { router } from 'expo-router';
import * as Sharing from 'expo-sharing';
import { useEffect, useState } from 'react';
import { Alert, Modal, Platform, Pressable, RefreshControl, ScrollView, StyleSheet, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import * as XLSX from 'xlsx';

// Import TopTabHeader
const TopTabHeader = require('@/components/TopTabHeader').default;

// Type for tasks
type Task = {
  id: string;
  name: string;
  status: string;
  site_id: string;
  site_name: string;
  due_date: string;
};

// Material database type
interface MaterialData {
  id: string;
  name: string;
  stock_quantity: number;
  unit_of_measure: string;
}

// Type for recent transactions
type Transaction = {
  id: string;
  type: 'income' | 'expense';
  amount: number;
  description: string;
  transaction_date: string;
  paid_to_received_from?: string;
  isNew?: boolean; // Flag to indicate a newly added or updated transaction
};

// For real-time subscription tracking
type RealtimeSubscription = {
  unsubscribe: () => void;
};

export default function DashboardScreen() {
  const insets = useSafeAreaInsets();
  const colorScheme = useColorScheme();
  const primaryColor = '#f97316'; // Infratask primary color
  const [loading, setLoading] = useState(true);
  const { profile, checkIfProfileNeeded, user } = useAuth();
  const [isFirstLoad, setIsFirstLoad] = useState(true);
  // Store the real-time subscription reference
  const [transactionSubscription, setTransactionSubscription] = useState<RealtimeSubscription | null>(null);

  // Initialize analytics data with zeros
  const [analyticsData, setAnalyticsData] = useState({
    totalSites: 0,
    totalWorkers: 0,
    tasksInProgress: 0, // changed to count instead of percentage
  });

  // State for due tasks
  const [dueTasks, setDueTasks] = useState<Task[]>([]);

  // State for recent transactions
  const [recentTransactions, setRecentTransactions] = useState<Transaction[]>([]);
  
  // States for transaction report feature
  const [showFromDate, setShowFromDate] = useState(false);
  const [showToDate, setShowToDate] = useState(false);
  const [fromDate, setFromDate] = useState(new Date(new Date().setDate(new Date().getDate() - 30))); // Default: 30 days ago
  const [toDate, setToDate] = useState(new Date()); // Default: today
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);
  const [reportModalVisible, setReportModalVisible] = useState(false);

  // States for in-app update feature
  const [updateInfo, setUpdateInfo] = useState<AppVersion | null>(null);
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(false);

  // Check if user needs to complete their profile
  useEffect(() => {
    // Only run once on first load of dashboard
    if (user && isFirstLoad) {
      setIsFirstLoad(false);
      
      const checkProfile = async () => {
        const needsProfile = await checkIfProfileNeeded();
        
        if (needsProfile) {
          Alert.alert(
            "Complete Your Profile",
            "Please complete your profile information to get the most out of Infratask.",
            [
              {
                text: "Later",
                style: "cancel"
              },
              { 
                text: "Complete Now", 
                onPress: () => router.push('/auth/create-profile')
              }
            ]
          );
        }
      };
      
      checkProfile();
    }
  }, [user, isFirstLoad]);

  // Check for app updates on dashboard load
  useEffect(() => {
    if (user) {
      initializeAndCheckUpdates();
    }
  }, [user]);

  // Function to initialize and check for app updates
  const initializeAndCheckUpdates = async () => {
    try {
      // Import the comprehensive update manager
      const { comprehensiveUpdateManager } = await import('@/lib/comprehensiveUpdateManager');

      // Initialize the update manager
      const initialized = await comprehensiveUpdateManager.initialize();

      if (initialized) {
        // Check for updates (this will handle development mode automatically)
        await comprehensiveUpdateManager.checkAndPromptForUpdates();
      } else {
        console.log('Update manager not initialized, skipping update check');
      }
    } catch (error) {
      console.error('Error with comprehensive update manager:', error);

      // Only use fallback in production builds
      if (!__DEV__) {
        try {
          const updateAvailable = await updateManager.checkForUpdates();
          if (updateAvailable) {
            await updateManager.markUpdateAsPrompted(updateAvailable.versionCode);
            setUpdateInfo(updateAvailable);
            setShowUpdatePrompt(true);
          }
        } catch (fallbackError) {
          console.error('Error with fallback update check:', fallbackError);
        }
      }
    }
  };

  // Handle update button press
  const handleUpdatePress = () => {
    if (updateInfo) {
      // Open Play Store directly instead of showing another prompt
      const packageName = 'com.infratasks.app';
      const playStoreUrl = `market://details?id=${packageName}`;
      const fallbackUrl = `https://play.google.com/store/apps/details?id=${packageName}`;

      Linking.canOpenURL(playStoreUrl)
        .then((supported) => {
          if (supported) {
            Linking.openURL(playStoreUrl);
          } else {
            Linking.openURL(fallbackUrl);
          }
        })
        .catch(() => {
          Linking.openURL(fallbackUrl);
        });
      
      setShowUpdatePrompt(false);
    }
  };

  // Handle dismiss update prompt
  const handleDismissUpdate = async () => {
    if (updateInfo) {
      // Mark update as dismissed
      await updateManager.markUpdateAsDismissed(updateInfo.versionCode);
    }
    setShowUpdatePrompt(false);
  };

  // Function to format due date display
  const formatDueDate = (dueDate: string) => {
    const today = new Date();
    const due = new Date(dueDate);
    const diffDays = differenceInDays(due, today);
    
    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Tomorrow';
    } else if (diffDays > 1 && diffDays <= 7) {
      return `${diffDays} days`;
    } else {
      return format(due, 'MMM d');
    }
  };

  // Function to format transaction date
  const formatTransactionDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'yyyy-MM-dd');
    } catch (error) {
      return dateString;
    }
  };

  // Function to load the analytics data and tasks
  const loadDashboardData = async () => {
    setLoading(true);
    try {
      const { data: userData, error: userError } = await supabase.auth.getUser();
      
      if (userError || !userData.user) {
        console.error('Error getting user:', userError);
        setLoading(false);
        return;
      }
      
      // Get user profile ID
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('user_id', userData.user.id)
        .single();
      
      if (profileError || !profileData) {
        console.error('Error fetching profile:', profileError);
        setLoading(false);
        return;
      }
      
      // Count total sites
      // First, get site IDs where user is a member
      const { data: memberSites, error: memberError } = await supabase
        .from('site_members')
        .select('site_id')
        .eq('user_id', userData.user.id);
        
      if (memberError) {
        console.error('Error fetching site memberships:', memberError);
      }
      
      // Extract site IDs to an array
      const siteIds = memberSites ? memberSites.map(site => site.site_id) : [];
      
      // Get sites where user is either the owner or a member
      let query = supabase
        .from('sites')
        .select('id');
        
      if (siteIds.length > 0) {
        // User is a member of some sites
        query = query.or(`owner_id.eq.${profileData.id},id.in.(${siteIds.join(',')})`);
      } else {
        // User is only an owner
        query = query.eq('owner_id', profileData.id);
      }
      
      // Execute the query
      const { data: sitesData, error: sitesError } = await query;
      
      if (sitesError) {
        console.error('Error loading sites:', sitesError);
      }
      
      // Get all sites the user has access to
      const allSitesIds = sitesData ? sitesData.map(site => site.id) : [];
      
      // Count total workers (across all sites)
      let workersCount = 0;
      if (allSitesIds.length > 0) {
        // Count unique workers across all sites
        const { data: workersData, error: workersError } = await supabase
          .from('site_members')
          .select('user_id')
          .in('site_id', allSitesIds);
          
        if (workersError) {
          console.error('Error counting workers:', workersError);
        } else if (workersData) {
          // Create a Set to eliminate duplicates (workers who are members of multiple sites)
          const uniqueWorkers = new Set(workersData.map(worker => worker.user_id));
          workersCount = uniqueWorkers.size;
        }
      }
      
      // Count tasks in progress (across all sites)
      let tasksInProgressCount = 0;
      if (allSitesIds.length > 0) {
        const { data: tasksData, error: tasksError } = await supabase
          .from('tasks')
          .select('id')
          .in('site_id', allSitesIds)
          .eq('status', 'in progress');
          
        if (tasksError) {
          console.error('Error counting tasks in progress:', tasksError);
        } else if (tasksData) {
          tasksInProgressCount = tasksData.length;
        }
      }
      
      // Update analytics data with real counts
      setAnalyticsData({
        totalSites: sitesData?.length || 0,
        totalWorkers: workersCount,
        tasksInProgress: tasksInProgressCount,
      });
      
      // Fetch tasks due soon
      if (allSitesIds.length > 0) {
        // Get today's date and 7 days from now
        const today = new Date();
        const nextWeek = addDays(today, 7);
        
        // Format dates for Supabase query
        const todayStr = today.toISOString().split('T')[0];
        const nextWeekStr = nextWeek.toISOString().split('T')[0];
        
        // Fetch tasks with due dates up to today (due or overdue)
        const { data: upcomingTasks, error: tasksError } = await supabase
          .from('tasks')
          .select(`
            id, 
            name, 
            status,
            due_date,
            site_id,
            sites(name)
          `)
          .in('site_id', allSitesIds)
          .lte('due_date', todayStr) // Tasks that are due today or past due
          .order('due_date', { ascending: true })
          .limit(3);
          
        if (tasksError) {
          console.error('Error fetching upcoming tasks:', tasksError);
        } else if (upcomingTasks) {
          // Format tasks with site names
          const formattedTasks = upcomingTasks.map(task => ({
            id: task.id,
            name: task.name,
            status: task.status,
            site_id: task.site_id,
            site_name: task.sites ? (task.sites as any).name : 'Unknown Site',
            due_date: task.due_date
          }));
          
          setDueTasks(formattedTasks);
        }
      }
      
      // Fetch recent transactions
      await fetchRecentTransactions();
      
    } catch (error) {
      console.error('Unexpected error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Function to fetch recent transactions (data only, no subscription setup)
  const fetchRecentTransactionsOnly = async () => {
    try {
      const { data: userData, error: userError } = await supabase.auth.getUser();

      if (userError || !userData.user) {
        console.error('Error getting user:', userError);
        return;
      }

      const { data, error } = await supabase
        .from('transactions')
        .select('id, type, amount, description, transaction_date, paid_to_received_from')
        .eq('user_id', userData.user.id)
        .order('transaction_date', { ascending: false })
        .limit(3);

      if (error) {
        console.error('Error fetching recent transactions:', error);
        return;
      }

      if (data) {
        setRecentTransactions(data);
      }
    } catch (error) {
      console.error('Unexpected error fetching recent transactions:', error);
    }
  };

  // Function to fetch recent transactions and set up subscription
  const fetchRecentTransactions = async () => {
    // First fetch the data
    await fetchRecentTransactionsOnly();

    try {
      const { data: userData, error: userError } = await supabase.auth.getUser();

      if (userError || !userData.user) {
        console.error('Error getting user for subscription setup:', userError);
        return;
      }

      // Set up real-time subscription if not already set
      if (!transactionSubscription) {
        setupTransactionSubscription(userData.user.id);
      }
    } catch (error) {
      console.error('Unexpected error setting up subscription:', error);
    }
  };
  
  // Function to set up real-time subscription for transactions
  const setupTransactionSubscription = (userId: string) => {
    // Unsubscribe from any existing subscription first
    if (transactionSubscription) {
      transactionSubscription.unsubscribe();
      setTransactionSubscription(null);
    }

    // Create a unique channel name to prevent conflicts
    const channelName = `transactions-changes-${userId}-${Date.now()}`;

    // Subscribe to changes on the transactions table filtered by user_id
    const subscription = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*', // Listen for all events (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'transactions',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          console.log('Real-time transaction update:', payload);
          
          // Update transactions based on the event type
          if (payload.eventType === 'INSERT') {
            // For new transactions, fetch only if we have less than 3 transactions or the new one is more recent
            const newTransaction = payload.new as Transaction;
            newTransaction.isNew = true; // Mark as new for animation
            setRecentTransactions(current => {
              // If we have less than 3 transactions, add the new one
              if (current.length < 3) {
                return [...current, newTransaction].sort((a, b) => 
                  new Date(b.transaction_date).getTime() - new Date(a.transaction_date).getTime()
                ).slice(0, 3);
              }
              
              // Check if the new transaction is more recent than any existing ones
              const oldestTransaction = current.reduce((oldest, tx) => 
                new Date(tx.transaction_date) < new Date(oldest.transaction_date) ? tx : oldest, 
                current[0]
              );
              
              if (new Date(newTransaction.transaction_date) > new Date(oldestTransaction.transaction_date)) {
                // Replace the oldest with the new one and sort
                return [...current.filter(tx => tx.id !== oldestTransaction.id), newTransaction]
                  .sort((a, b) => new Date(b.transaction_date).getTime() - new Date(a.transaction_date).getTime())
                  .slice(0, 3);
              }
              
              return current;
            });
            
            // Remove the "new" flag after animation
            setTimeout(() => {
              setRecentTransactions(current => 
                current.map(tx => tx.id === newTransaction.id ? {...tx, isNew: false} : tx)
              );
            }, 2000);
          } else if (payload.eventType === 'UPDATE') {
            // For updated transactions, update the corresponding transaction in state
            const updatedTransaction = payload.new as Transaction;
            updatedTransaction.isNew = true; // Mark as updated for animation
            setRecentTransactions(current => 
              current.map(tx => tx.id === updatedTransaction.id ? updatedTransaction : tx)
            );
            
            // Remove the "new" flag after animation
            setTimeout(() => {
              setRecentTransactions(current => 
                current.map(tx => tx.id === updatedTransaction.id ? {...tx, isNew: false} : tx)
              );
            }, 2000);
          } else if (payload.eventType === 'DELETE') {
            // For deleted transactions, remove from state and fetch a new one if needed
            const deletedId = payload.old.id;
            setRecentTransactions(current => {
              const filtered = current.filter(tx => tx.id !== deletedId);
              // If we're now below 3 transactions, fetch more
              if (filtered.length < current.length && filtered.length < 3) {
                // Schedule a fetch to get the next transaction (without setting up subscription again)
                setTimeout(() => fetchRecentTransactionsOnly(), 100);
              }
              return filtered;
            });
          }
        }
      )
      .subscribe();

    // Store the actual subscription for cleanup
    setTransactionSubscription(subscription);
  };



  // Load data when the component mounts
  useEffect(() => {
    loadDashboardData();

    // Cleanup subscription when component unmounts
    return () => {
      if (transactionSubscription) {
        transactionSubscription.unsubscribe();
      }
    };
  }, [profile]);

  // Function to navigate to all tasks
  const goToTasks = () => {
    // Assuming there's a tasks tab or page
    router.push('/sites');
  };

  // Function to navigate to transactions page
  const goToTransactions = () => {
    router.push('/transactions');
  };

  // Function to fetch transactions within a date range
  const fetchTransactionsForReport = async (startDate: Date, endDate: Date) => {
    try {
      const { data: userData, error: userError } = await supabase.auth.getUser();
      
      if (userError || !userData.user) {
        console.error('Error getting user:', userError);
        return [];
      }
      
      // Format dates for SQL query (YYYY-MM-DD format)
      const formattedStartDate = format(startDate, 'yyyy-MM-dd');
      const formattedEndDate = format(endDate, 'yyyy-MM-dd');
      
      // Fetch transactions within the date range
      const { data, error } = await supabase
        .from('transactions')
        .select('id, type, amount, description, transaction_date, paid_to_received_from')
        .eq('user_id', userData.user.id)
        .gte('transaction_date', formattedStartDate)
        .lte('transaction_date', formattedEndDate)
        .order('transaction_date', { ascending: false });
        
      if (error) {
        console.error('Error fetching transactions for report:', error);
        return [];
      }
      
      return data || [];
    } catch (error) {
      console.error('Unexpected error fetching transactions for report:', error);
      return [];
    }
  };

  // Generate PDF report
  const generatePdfReport = async () => {
    setIsGeneratingReport(true);
    try {
      const transactions = await fetchTransactionsForReport(fromDate, toDate);
      
      if (transactions.length === 0) {
        alert('No transactions found in the selected date range.');
        setIsGeneratingReport(false);
        return;
      }
      
      // Calculate totals
      let totalIncome = 0;
      let totalExpense = 0;
      
      transactions.forEach(tx => {
        if (tx.type === 'income') {
          totalIncome += Number(tx.amount);
        } else {
          totalExpense += Number(tx.amount);
        }
      });
      
      const netAmount = totalIncome - totalExpense;
      
      // Create HTML content for PDF
      let transactionsHtml = '';
      
      transactions.forEach((tx, index) => {
        transactionsHtml += `
          <tr ${index % 2 === 0 ? 'style="background-color: #f9fafb;"' : ''}>
            <td>${format(new Date(tx.transaction_date), 'yyyy-MM-dd')}</td>
            <td>${tx.description}</td>
            <td>${tx.paid_to_received_from || '-'}</td>
            <td style="color: ${tx.type === 'income' ? '#10b981' : '#f43f5e'}">
              ${tx.type === 'income' ? '+' : '-'} ₹${tx.amount}
            </td>
          </tr>
        `;
      });
      
      const html = `
        <!DOCTYPE html>
        <html>
          <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
            <style>
              body { font-family: 'Helvetica', sans-serif; margin: 0; padding: 20px; }
              h1 { color: #f97316; font-size: 24px; text-align: center; margin-bottom: 5px; }
              .date-range { text-align: center; margin-bottom: 20px; color: #4b5563; font-size: 14px; }
              table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
              th { background-color: #f97316; color: white; text-align: left; padding: 10px; }
              td { padding: 10px; border-bottom: 1px solid #e5e7eb; }
              .summary { background-color: #f3f4f6; padding: 15px; border-radius: 5px; }
              .summary-title { font-weight: bold; margin-bottom: 10px; color: #1f2937; }
              .summary-item { display: flex; justify-content: space-between; margin-bottom: 5px; }
              .net-amount { font-weight: bold; margin-top: 10px; display: flex; justify-content: space-between; }
              .income { color: #10b981; }
              .expense { color: #f43f5e; }
            </style>
          </head>
          <body>
            <h1>Transaction Report</h1>
            <div class="date-range">
              ${format(fromDate, 'MMMM d, yyyy')} - ${format(toDate, 'MMMM d, yyyy')}
            </div>
            
            <table>
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Description</th>
                  <th>Paid To/Received From</th>
                  <th>Amount</th>
                </tr>
              </thead>
              <tbody>
                ${transactionsHtml}
              </tbody>
            </table>
            
            <div class="summary">
              <div class="summary-title">Summary</div>
              <div class="summary-item">
                <span>Total Income:</span>
                <span class="income">₹${totalIncome.toFixed(2)}</span>
              </div>
              <div class="summary-item">
                <span>Total Expense:</span>
                <span class="expense">₹${totalExpense.toFixed(2)}</span>
              </div>
              <div class="net-amount">
                <span>Net Amount:</span>
                <span style="color: ${netAmount >= 0 ? '#10b981' : '#f43f5e'}">
                  ₹${Math.abs(netAmount).toFixed(2)}
                </span>
              </div>
            </div>
          </body>
        </html>
      `;
      
      // Generate PDF
      const { uri } = await Print.printToFileAsync({ html });
      
      // Get the generated filename from the URI
      const filename = `Transaction_Report_${format(fromDate, 'yyyyMMdd')}_to_${format(toDate, 'yyyyMMdd')}.pdf`;
      
      // On iOS, we need to save the file to a different location
      if (Platform.OS === 'ios') {
        const destinationUri = FileSystem.documentDirectory + filename;
        await FileSystem.moveAsync({
          from: uri,
          to: destinationUri
        });
        await Sharing.shareAsync(destinationUri);
      } else {
        await Sharing.shareAsync(uri, { UTI: '.pdf', mimeType: 'application/pdf' });
      }
      
    } catch (error) {
      console.error('Error generating PDF report:', error);
      alert('Failed to generate PDF report. Please try again.');
    } finally {
      setIsGeneratingReport(false);
      setReportModalVisible(false);
    }
  };
  
  // Generate Excel report
  const generateExcelReport = async () => {
    setIsGeneratingReport(true);
    try {
      const transactions = await fetchTransactionsForReport(fromDate, toDate);
      
      if (transactions.length === 0) {
        alert('No transactions found in the selected date range.');
        setIsGeneratingReport(false);
        return;
      }
      
      // Format the data for Excel export
      const excelData = transactions.map(tx => ({
        Date: format(new Date(tx.transaction_date), 'yyyy-MM-dd'),
        Type: tx.type.charAt(0).toUpperCase() + tx.type.slice(1),
        Description: tx.description,
        'Paid To/Received From': tx.paid_to_received_from || '',
        Amount: Number(tx.amount),
      }));
      
      // Calculate totals
      const totalIncome = transactions
        .filter(tx => tx.type === 'income')
        .reduce((sum, tx) => sum + Number(tx.amount), 0);
        
      const totalExpense = transactions
        .filter(tx => tx.type === 'expense')
        .reduce((sum, tx) => sum + Number(tx.amount), 0);
        
      const netAmount = totalIncome - totalExpense;
      
      // Add summary at the end
      excelData.push(
        { 
          Date: 'Summary',
          Type: '',
          Description: '',
          'Paid To/Received From': '',
          Amount: 0
        }, // Empty row with required properties
        { 
          Date: 'Total Income:',
          Type: '',
          Description: '',
          'Paid To/Received From': '',
          Amount: totalIncome
        },
        { 
          Date: 'Total Expense:',
          Type: '',
          Description: '',
          'Paid To/Received From': '',
          Amount: totalExpense
        },
        { 
          Date: 'Net Amount:',
          Type: '',
          Description: '',
          'Paid To/Received From': '',
          Amount: netAmount
        }
      );
      
      // Create a worksheet
      const ws = XLSX.utils.json_to_sheet(excelData);
      
      // Set column widths
      const wscols = [
        { wch: 12 }, // Date
        { wch: 10 }, // Type
        { wch: 30 }, // Description
        { wch: 25 }, // Paid To/Received From
        { wch: 12 }, // Amount
      ];
      ws['!cols'] = wscols;
      
      // Create a workbook
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Transactions');
      
      // Generate excel file
      const wbout = XLSX.write(wb, { type: 'base64', bookType: 'xlsx' });
      
      // Get the storage directory
      const filename = `Transaction_Report_${format(fromDate, 'yyyyMMdd')}_to_${format(toDate, 'yyyyMMdd')}.xlsx`;
      const fileUri = FileSystem.documentDirectory + filename;
      
      // Write the file
      await FileSystem.writeAsStringAsync(fileUri, wbout, {
        encoding: FileSystem.EncodingType.Base64
      });
      
      // Share the file
      await Sharing.shareAsync(fileUri, { UTI: '.xlsx', mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      
    } catch (error) {
      console.error('Error generating Excel report:', error);
      alert('Failed to generate Excel report. Please try again.');
    } finally {
      setIsGeneratingReport(false);
      setReportModalVisible(false);
    }
  };
  
  // Handle date changes
  const onFromDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || fromDate;
    setShowFromDate(Platform.OS === 'ios');
    setFromDate(currentDate);
    
    // Ensure fromDate is not after toDate
    if (isAfter(currentDate, toDate)) {
      setToDate(currentDate);
    }
  };
  
  const onToDateChange = (event: any, selectedDate?: Date) => {
    const currentDate = selectedDate || toDate;
    setShowToDate(Platform.OS === 'ios');
    setToDate(currentDate);
    
    // Ensure toDate is not before fromDate
    if (isBefore(currentDate, fromDate)) {
      setFromDate(currentDate);
    }
  };

  return (
    <>
      <TopTabHeader />
      <ThemedView style={[styles.container, { paddingTop: 0 }]}>
        {/* Header */}
      <View style={styles.header}>
        <ThemedText style={styles.headerTitle}>Dashboard</ThemedText>
      </View>

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl 
            refreshing={loading} 
            onRefresh={loadDashboardData} 
            colors={[primaryColor]} 
          />
        }
      >
        {/* In-App Update Prompt */}
        {showUpdatePrompt && updateInfo && (
          <UpdatePrompt
            updateInfo={updateInfo}
            onUpdate={handleUpdatePress}
            onDismiss={!updateInfo.mandatory ? handleDismissUpdate : undefined}
            style={{ marginHorizontal: 0, marginTop: 0 }}
          />
        )}

        {/* Analytics Section */}
        <View style={styles.analyticsSection}>
          {/* Total Sites Card */}
          <ThemedView style={[styles.analyticsCard, { backgroundColor: colorScheme === 'dark' ? '#262626' : '#fff' }]}>
            <LinearGradient
              colors={[primaryColor, '#ea580c']}
              style={styles.iconContainer}
            >
              <FontAwesome5 name="building" size={18} color="#fff" />
            </LinearGradient>
            <View style={styles.cardContent}>
              <ThemedText style={styles.cardValue}>{analyticsData.totalSites}</ThemedText>
              <ThemedText style={styles.cardLabel}>Total Sites</ThemedText>
            </View>
          </ThemedView>

          {/* Total Workers Card */}
          <ThemedView style={[styles.analyticsCard, { backgroundColor: colorScheme === 'dark' ? '#262626' : '#fff' }]}>
            <LinearGradient
              colors={[primaryColor, '#ea580c']}
              style={styles.iconContainer}
            >
              <FontAwesome5 name="users" size={18} color="#fff" />
            </LinearGradient>
            <View style={styles.cardContent}>
              <ThemedText style={styles.cardValue}>{analyticsData.totalWorkers}</ThemedText>
              <ThemedText style={styles.cardLabel}>Total Workers</ThemedText>
            </View>
          </ThemedView>

          {/* Tasks in Progress Card */}
          <ThemedView style={[styles.analyticsCard, { backgroundColor: colorScheme === 'dark' ? '#262626' : '#fff' }]}>
            <LinearGradient
              colors={[primaryColor, '#ea580c']}
              style={styles.iconContainer}
            >
              <Octicons name="tasklist" size={18} color="#fff" />
            </LinearGradient>
            <View style={styles.cardContent}>
              <ThemedText style={styles.cardValue}>{analyticsData.tasksInProgress}</ThemedText>
              <ThemedText style={styles.cardLabel}>Tasks in Progress</ThemedText>
            </View>
          </ThemedView>
        </View>

        {/* Due Tasks Section */}
        <View style={styles.sectionHeaderRow}>
          <ThemedText style={styles.sectionTitle}>Due Tasks</ThemedText>
          <Pressable onPress={goToTasks}>
            <ThemedText style={[styles.viewAllButton, { color: primaryColor }]}>View All</ThemedText>
          </Pressable>
        </View>
        <ThemedView style={styles.sectionContainer}>
          {dueTasks.length > 0 ? (
            dueTasks.map(task => (
              <Pressable 
                key={task.id} 
                style={[
                  styles.taskCard,
                  { backgroundColor: colorScheme === 'dark' ? '#262626' : '#fff' }
                ]}
                onPress={() => {
                  router.push({
                    pathname: "/site/[id]/task-details",
                    params: { id: task.site_id, taskId: task.id }
                  });
                }}
              >
                <View style={styles.taskCardHeader}>
                  <View style={[styles.taskCategoryTag, { 
                    backgroundColor: 
                      task.status === 'completed' ? '#22c55e' : 
                      task.status === 'in progress' ? '#f97316' : '#ef4444'
                  }]}>
                    <ThemedText style={styles.taskCategoryText}>{task.status}</ThemedText>
                  </View>
                  <ThemedText style={[
                    styles.taskDueDate,
                    new Date(task.due_date) < new Date() ? { color: '#ef4444' } : {}
                  ]}>
                    {formatDueDate(task.due_date)}
                  </ThemedText>
                </View>
                <ThemedText style={styles.taskName}>{task.name}</ThemedText>
                <ThemedText style={styles.taskSite}>{task.site_name}</ThemedText>
              </Pressable>
            ))
          ) : (
            <ThemedView style={[
              styles.emptyStateContainer,
              { backgroundColor: colorScheme === 'dark' ? '#262626' : '#fff' }
            ]}>
              <ThemedText style={styles.emptyStateText}>No tasks due</ThemedText>
            </ThemedView>
          )}
        </ThemedView>

        {/* Recent Transactions Section */}
        <View style={styles.sectionHeaderRow}>
          <ThemedText style={styles.sectionTitle}>Recent Transactions</ThemedText>
          <Pressable onPress={goToTransactions}>
            <ThemedText style={[styles.viewAllButton, { color: primaryColor }]}>View All</ThemedText>
          </Pressable>
        </View>
        <ThemedView style={styles.sectionContainer}>
          {recentTransactions.length > 0 ? (
            recentTransactions.map(transaction => (
              <Pressable 
                key={transaction.id} 
                style={[
                  styles.transactionCard,
                  { backgroundColor: colorScheme === 'dark' ? '#262626' : '#fff' },
                  transaction.isNew && styles.highlightedTransactionCard
                ]}
                onPress={goToTransactions}
              >
                <View style={styles.transactionIconContainer}>
                  <View style={[
                    styles.transactionIconBg, 
                    { backgroundColor: transaction.type === 'income' ? '#ecfdf5' : '#fff1f2' }
                  ]}>
                    <MaterialCommunityIcons 
                      name={transaction.type === 'income' ? 'arrow-down-circle' : 'arrow-up-circle'} 
                      size={24} 
                      color={transaction.type === 'income' ? '#10b981' : '#f43f5e'} 
                    />
                  </View>
                </View>
                
                <View style={styles.transactionDetails}>
                  <ThemedText style={styles.transactionDescription}>{transaction.description}</ThemedText>
                  <ThemedText style={styles.transactionDate}>{formatTransactionDate(transaction.transaction_date)}</ThemedText>
                </View>
                
                <View style={styles.transactionAmount}>
                  <ThemedText style={[
                    styles.amountText, 
                    { color: transaction.type === 'income' ? '#10b981' : '#f43f5e' }
                  ]}>
                    {transaction.type === 'income' ? '+' : '-'} ₹{transaction.amount}
                  </ThemedText>
                </View>
              </Pressable>
            ))
          ) : (
            <ThemedView style={[
              styles.emptyStateContainer,
              { backgroundColor: colorScheme === 'dark' ? '#262626' : '#fff' }
            ]}>
              <ThemedText style={styles.emptyStateText}>No recent transactions</ThemedText>
            </ThemedView>
          )}
        </ThemedView>

        {/* Transaction Report Section */}
        <ThemedText style={styles.sectionTitle}>Transaction Report</ThemedText>
        <ThemedView 
          style={[
            styles.reportSection, 
            { backgroundColor: colorScheme === 'dark' ? '#262626' : '#fff' }
          ]}
        >
          <View style={styles.dateRangeInfo}>
            <View style={styles.dateInfo}>
              <ThemedText style={styles.dateLabel}>From</ThemedText>
              <Pressable 
                style={styles.datePickerButton} 
                onPress={() => setShowFromDate(true)}
              >
                <ThemedText style={styles.dateButtonText}>
                  {format(fromDate, 'MMM d, yyyy')}
                </ThemedText>
                <MaterialIcons name="date-range" size={18} color={primaryColor} />
              </Pressable>
            </View>
            <View style={styles.dateInfo}>
              <ThemedText style={styles.dateLabel}>To</ThemedText>
              <Pressable 
                style={styles.datePickerButton} 
                onPress={() => setShowToDate(true)}
              >
                <ThemedText style={styles.dateButtonText}>
                  {format(toDate, 'MMM d, yyyy')}
                </ThemedText>
                <MaterialIcons name="date-range" size={18} color={primaryColor} />
              </Pressable>
            </View>
          </View>
          
          <Pressable
            style={[
              styles.generateReportButton,
              { backgroundColor: primaryColor, marginTop: 16 }
            ]}
            onPress={() => setReportModalVisible(true)}
          >
            <FontAwesome5 name="file-download" size={18} color="#fff" style={styles.buttonIcon} />
            <ThemedText style={[styles.generateButtonText, { color: '#fff' }]}>Generate Report</ThemedText>
          </Pressable>
          
          {/* Date pickers */}
          {Platform.OS === 'android' && showFromDate && (
            <DateTimePicker
              testID="fromDatePicker"
              value={fromDate}
              mode="date"
              display="default"
              onChange={onFromDateChange}
            />
          )}
          
          {Platform.OS === 'android' && showToDate && (
            <DateTimePicker
              testID="toDatePicker"
              value={toDate}
              mode="date"
              display="default"
              onChange={onToDateChange}
            />
          )}
          
          {/* iOS date picker modal */}
          {Platform.OS === 'ios' && (
            <Modal
              animationType="slide"
              transparent={true}
              visible={showFromDate || showToDate}
              onRequestClose={() => {
                setShowFromDate(false);
                setShowToDate(false);
              }}
            >
              <View style={styles.modalOverlay}>
                <ThemedView style={[
                  styles.modalContent, 
                  { backgroundColor: colorScheme === 'dark' ? '#1c1c1e' : '#fff' }
                ]}>
                  <View style={styles.modalHeader}>
                    <ThemedText style={styles.modalTitle}>
                      {showFromDate ? 'Select Start Date' : 'Select End Date'}
                    </ThemedText>
                    <Pressable 
                      onPress={() => {
                        setShowFromDate(false);
                        setShowToDate(false);
                      }}
                      style={styles.closeButton}
                    >
                      <MaterialIcons 
                        name="close" 
                        size={24} 
                        color={colorScheme === 'dark' ? '#fff' : '#000'} 
                      />
                    </Pressable>
                  </View>
                  
                  <DateTimePicker
                    testID={showFromDate ? "fromDatePicker" : "toDatePicker"}
                    value={showFromDate ? fromDate : toDate}
                    mode="date"
                    display="spinner"
                    onChange={showFromDate ? onFromDateChange : onToDateChange}
                    style={{ height: 200 }}
                  />
                  
                  <Pressable
                    style={[
                      styles.generateReportButton,
                      { backgroundColor: primaryColor }
                    ]}
                    onPress={() => {
                      setShowFromDate(false);
                      setShowToDate(false);
                    }}
                  >
                    <ThemedText style={[styles.generateButtonText, { color: '#fff' }]}>
                      Confirm
                    </ThemedText>
                  </Pressable>
                </ThemedView>
              </View>
            </Modal>
          )}
        </ThemedView>

        {/* Transaction Report Download Section */}
        <TransactionReportDownload 
          visible={reportModalVisible}
          onClose={() => setReportModalVisible(false)}
          onPdfDownload={generatePdfReport}
          onExcelDownload={generateExcelReport}
          isGenerating={isGeneratingReport}
        />
      </ScrollView>
    </ThemedView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    paddingTop: 5,
  },

  scrollContent: {
    paddingBottom: 20,
  },
  analyticsSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  analyticsCard: {
    width: '31%',
    borderRadius: 16,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardContent: {
    flex: 1,
  },
  cardValue: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  cardLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  viewAllButton: {
    fontSize: 14,
    fontWeight: '500',
  },
  taskCard: {
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  taskCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  taskCategoryTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  taskCategoryText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  taskDueDate: {
    fontSize: 12,
    opacity: 0.8,
  },
  taskName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  taskSite: {
    fontSize: 12,
    opacity: 0.8,
  },
  emptyStateContainer: {
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  emptyStateText: {
    fontSize: 14,
    opacity: 0.7,
  },
  transactionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  transactionIconContainer: {
    marginRight: 12,
  },
  transactionIconBg: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  transactionDetails: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  transactionDate: {
    fontSize: 12,
    opacity: 0.7,
  },
  transactionAmount: {
    justifyContent: 'center',
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  highlightedTransactionCard: {
    backgroundColor: '#f8fafc', // Light background for light mode
    borderWidth: 1,
    borderColor: '#f97316', // Primary color border
    shadowColor: '#f97316',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  reportSection: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  dateRangeInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  dateInfo: {
    width: '48%',
  },
  dateLabel: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 8,
  },
  dateValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)'
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    paddingBottom: 40,
    elevation: 5,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 5,
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderRadius: 8,
    padding: 10,
    paddingHorizontal: 12,
    borderColor: '#d1d5db',
  },
  dateButtonText: {
    fontSize: 14,
  },
  exportButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    width: '48%',
  },
  exportButtonText: {
    color: '#ffffff',
    fontWeight: '500',
    fontSize: 14,
    marginLeft: 8,
  },
  generatingText: {
    textAlign: 'center',
    marginTop: 16,
    fontStyle: 'italic',
    opacity: 0.7,
  },
  generateReportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    width: '100%',
  },
  buttonIcon: {
    marginRight: 8,
  },
  generateButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
