import { mcp_supabase_execute_sql } from './mcp-supabase';
import { supabase } from './supabase';

// Interface for creating a Razorpay order
export interface CreateOrderRequest {
  amount: number; // Amount in paise (e.g., 50000 for ₹500)
  currency?: string; // Default: 'INR'
  receipt?: string; // Optional receipt ID
  partial_payment?: boolean; // Default: false
  notes?: Record<string, string>; // Additional metadata
  user_id?: string; // User ID for logging
  plan_id?: string; // Subscription plan ID
}

// Interface for Razorpay order response
export interface RazorpayOrder {
  id: string; // Razorpay order ID
  entity: string; // 'order'
  amount: number; // Amount in paise
  amount_paid: number; // Amount paid so far
  amount_due: number; // Amount remaining
  currency: string; // Currency code
  receipt: string; // Receipt ID
  offer_id: string | null; // Offer ID if any
  status: string; // Order status
  attempts: number; // Payment attempts
  notes: Record<string, string>; // Additional notes
  created_at: number; // Unix timestamp
}

// Response from the edge function
export interface CreateOrderResponse {
  success: boolean;
  order: RazorpayOrder;
  message: string;
}

// Error response
export interface OrderError {
  error: string;
  message: string;
}

/**
 * Creates a Razorpay order using the Supabase edge function
 * @param orderData - Order creation parameters
 * @returns Promise with order details or error
 */
export const createRazorpayOrder = async (
  orderData: CreateOrderRequest
): Promise<CreateOrderResponse> => {
  try {
    // Get current user session
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      throw new Error('User not authenticated');
    }

    // Add user_id to the order data if not provided
    const requestData = {
      ...orderData,
      user_id: orderData.user_id || session.user.id
    };

    // Call the Supabase edge function
    const { data, error } = await supabase.functions.invoke('create-razorpay-order', {
      body: requestData
    });

    if (error) {
      console.error('Edge function error:', error);
      throw new Error(error.message || 'Failed to create order');
    }

    if (!data.success) {
      throw new Error(data.message || 'Order creation failed');
    }

    return data as CreateOrderResponse;

  } catch (error) {
    console.error('Error creating Razorpay order:', error);
    throw error;
  }
};

/**
 * Helper function to create order for subscription plans
 * @param planId - Subscription plan ID ('standard' or 'premium')
 * @param userId - Optional user ID
 * @returns Promise with order details
 */
export const createSubscriptionOrder = async (
  planId: 'standard' | 'premium',
  userId?: string
): Promise<CreateOrderResponse> => {
  // Define plan amounts (in paise)
  const planAmounts = {
    standard: 24900, // ₹249
    premium: 99900   // ₹999
  };

  const planNames = {
    standard: 'Standard Plan',
    premium: 'Premium Plan'
  };

  const amount = planAmounts[planId];
  if (!amount) {
    throw new Error('Invalid plan ID');
  }

  return createRazorpayOrder({
    amount,
    receipt: `${planId}_${Date.now()}`,
    notes: {
      plan_id: planId,
      plan_name: planNames[planId],
      subscription_type: 'monthly'
    },
    user_id: userId,
    plan_id: planId
  });
};

/**
 * Helper function to create order for custom amounts
 * @param amount - Amount in rupees (will be converted to paise)
 * @param description - Description for the payment
 * @param userId - Optional user ID
 * @returns Promise with order details
 */
export const createCustomOrder = async (
  amount: number,
  description: string,
  userId?: string
): Promise<CreateOrderResponse> => {
  // Convert rupees to paise
  const amountInPaise = Math.round(amount * 100);

  return createRazorpayOrder({
    amount: amountInPaise,
    receipt: `custom_${Date.now()}`,
    notes: {
      description,
      amount_rupees: amount.toString(),
      payment_type: 'custom'
    },
    user_id: userId
  });
};

// Interface for payment update request
export interface PaymentUpdateRequest {
  razorpay_order_id: string;
  razorpay_payment_id?: string;
  razorpay_signature?: string;
  plan_id: string;
  payment_status: 'success' | 'cancelled' | 'failed';
}

// Interface for payment update response
export interface PaymentUpdateResponse {
  success: boolean;
  message: string;
  subscription?: any;
  order_status?: string;
}

/**
 * Updates subscription and payment status using edge function
 * @param paymentData - Payment update data
 * @returns Promise with update result
 */
export const updateSubscriptionPayment = async (
  paymentData: PaymentUpdateRequest
): Promise<PaymentUpdateResponse> => {
  try {
    // Get current user session
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      throw new Error('User not authenticated');
    }

    // Call the Supabase edge function
    const { data, error } = await supabase.functions.invoke('update-subscription-payment', {
      body: paymentData
    });

    if (error) {
      console.error('Edge function error details:', {
        error,
        paymentData,
        errorMessage: error.message,
        errorCode: error.code
      });

      // Create a more descriptive error
      const errorMessage = error.message || 'Failed to update payment status';
      const enhancedError = new Error(`Edge Function Error: ${errorMessage}`);
      (enhancedError as any).originalError = error;
      (enhancedError as any).paymentData = paymentData;
      throw enhancedError;
    }

    // Validate response data
    if (!data) {
      throw new Error('Edge function returned empty response');
    }

    return data as PaymentUpdateResponse;
  } catch (error) {
    console.error('Error updating subscription payment:', error);
    throw error;
  }
};

/**
 * Fetch order details from local database
 * @param orderId - Razorpay order ID
 * @returns Promise with order details from database
 */
export const getOrderFromDatabase = async (orderId: string) => {
  try {
    const { data, error } = await supabase
      .from('razorpay_orders')
      .select('*')
      .eq('order_id', orderId)
      .single();

    if (error) {
      console.error('Error fetching order from database:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  }
};

/**
 * Update order status in database
 * @param orderId - Razorpay order ID
 * @param status - New status
 * @returns Promise with update result
 */
export const updateOrderStatus = async (orderId: string, status: string) => {
  try {
    const { data, error } = await supabase
      .from('razorpay_orders')
      .update({ status })
      .eq('order_id', orderId)
      .select()
      .single();

    if (error) {
      console.error('Error updating order status:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Database update error:', error);
    throw error;
  }
};

/**
 * Interface for subscription creation/update data
 */
export interface SubscriptionData {
  user_id: string;
  plan_id: string;
  status: 'active' | 'cancelled' | 'expired' | 'trial';
  payment_id: string;
  order_id: string;
  razorpay_signature: string;
  current_period_start?: string;
  current_period_end?: string;
  additional_users?: number;
}

/**
 * Creates or updates user subscription using MCP tool after successful payment
 * @param subscriptionData - Subscription data including payment details
 * @returns Promise with subscription creation/update result
 */
export const createOrUpdateSubscriptionMCP = async (
  subscriptionData: SubscriptionData
): Promise<any> => {
  try {
    // Get Supabase project ID from environment or config
    const project_id = process.env.EXPO_PUBLIC_SUPABASE_PROJECT_ID || 'your-project-id';

    // Prepare the SQL query for upsert operation
    const query = `
      INSERT INTO user_subscriptions (
        user_id,
        plan_id,
        status,
        payment_id,
        order_id,
        razorpay_signature,
        current_period_start,
        current_period_end,
        additional_users,
        auto_renew,
        created_at,
        updated_at
      ) VALUES (
        '${subscriptionData.user_id}',
        '${subscriptionData.plan_id}',
        '${subscriptionData.status}',
        '${subscriptionData.payment_id}',
        '${subscriptionData.order_id}',
        '${subscriptionData.razorpay_signature}',
        '${subscriptionData.current_period_start || new Date().toISOString()}',
        '${subscriptionData.current_period_end || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()}',
        ${subscriptionData.additional_users || 0},
        true,
        NOW(),
        NOW()
      )
      ON CONFLICT (user_id)
      DO UPDATE SET
        plan_id = EXCLUDED.plan_id,
        status = EXCLUDED.status,
        payment_id = EXCLUDED.payment_id,
        order_id = EXCLUDED.order_id,
        razorpay_signature = EXCLUDED.razorpay_signature,
        current_period_start = EXCLUDED.current_period_start,
        current_period_end = EXCLUDED.current_period_end,
        additional_users = EXCLUDED.additional_users,
        auto_renew = EXCLUDED.auto_renew,
        updated_at = NOW()
      RETURNING *;
    `;

    // Execute the SQL query using MCP tool
    const result = await mcp_supabase_execute_sql({
      project_id,
      query
    });

    if (result.error) {
      console.error('MCP SQL execution error:', result.error);
      throw new Error(result.error.message || 'Failed to create/update subscription');
    }

    console.log('Subscription created/updated successfully via MCP:', result);
    return result;

  } catch (error) {
    console.error('Error creating/updating subscription via MCP:', error);
    throw error;
  }
};

// Interface for shop order creation
export interface ShopOrderRequest {
  cart_items: Array<{
    product_id: number;
    quantity: number;
    unit_price: number;
    total_price: number;
  }>;
  total_amount: number;
  delivery_address: string;
  delivery_time?: string;
  notes?: Record<string, string>;
}

// Response from shop order creation
export interface CreateShopOrderResponse {
  success: boolean;
  order: RazorpayOrder;
  shop_order: any;
  message: string;
}

/**
 * Creates a shop order with cart items and delivery details
 * @param orderData - Shop order creation parameters
 * @returns Promise with order details or error
 */
export const createShopOrder = async (
  orderData: ShopOrderRequest
): Promise<CreateShopOrderResponse> => {
  try {
    console.log('Creating shop order with data:', orderData);

    // Get current user session
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      throw new Error('User not authenticated');
    }

    console.log('User authenticated, calling edge function...');

    // Call the Supabase edge function for shop orders
    const { data, error } = await supabase.functions.invoke('create-shop-order', {
      body: orderData
    });

    console.log('Edge function response:', { data, error });

    if (error) {
      console.error('Shop order creation error:', error);
      throw new Error(error.message || 'Failed to create shop order');
    }

    if (!data || !data.success) {
      console.error('Shop order creation failed:', data);
      throw new Error(data?.message || 'Shop order creation failed');
    }

    console.log('Shop order created successfully:', data.order.id);
    return data;

  } catch (error) {
    console.error('Error in createShopOrder:', error);
    throw error;
  }
};

// Interface for shop order payment update
export interface ShopPaymentUpdateRequest {
  razorpay_order_id: string;
  razorpay_payment_id?: string;
  razorpay_signature?: string;
  payment_status: 'success' | 'cancelled' | 'failed';
  order_id: number; // Shop order ID from database
}

/**
 * Updates shop order payment status after payment completion using edge function
 * @param paymentData - Payment update data for shop order
 * @returns Promise with update result
 */
export const updateShopOrderPayment = async (
  paymentData: ShopPaymentUpdateRequest
): Promise<any> => {
  try {
    // Get current user session
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      throw new Error('User not authenticated');
    }

    // Call the Supabase edge function for shop order payment update
    const { data, error } = await supabase.functions.invoke('update-shop-order-payment', {
      body: paymentData
    });

    if (error) {
      console.error('Shop order payment update error:', error);
      throw new Error(error.message || 'Failed to update shop order payment');
    }

    if (!data || !data.success) {
      throw new Error(data?.message || 'Shop order payment update failed');
    }

    console.log('Shop order payment updated successfully');
    return data;

  } catch (error) {
    console.error('Error in updateShopOrderPayment:', error);
    throw error;
  }
};
