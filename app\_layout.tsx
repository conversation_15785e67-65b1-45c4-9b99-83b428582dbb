import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { SplashScreen, Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import { ActivityIndicator, View, useColorScheme as useSystemColorScheme } from 'react-native';
import 'react-native-reanimated';
import { AuthProvider, useAuth } from '../context/AuthContext';

import { useColorScheme } from '@/hooks/useColorScheme';

// Prevent splash screen from auto-hiding
SplashScreen.preventAutoHideAsync();

function RootLayoutNav() {
  const { session, profile, loading } = useAuth();
  const colorScheme = useColorScheme();
  const systemColorScheme = useSystemColorScheme();

  // Determine the appropriate background color based on the color scheme
  const backgroundColor = systemColorScheme === 'dark' ? DarkTheme.colors.background : DefaultTheme.colors.background;
  const theme = colorScheme === 'dark' ? DarkTheme : DefaultTheme;

  useEffect(() => {
    // Hide splash screen once auth state is determined
    if (!loading) {
      SplashScreen.hideAsync();
    }
  }, [loading]);

  console.log('RootLayoutNav: Auth state', { 
    hasSession: !!session, 
    hasProfile: !!profile, 
    loading, 
    profileComplete: profile?.full_name ? true : false 
  });

  // Keep the loading screen visible until everything is ready
  if (loading) {
    return (
      <ThemeProvider value={theme}>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor }}>
          <ActivityIndicator size="large" color="#f97316" />
        </View>
      </ThemeProvider>
    );
  }

  // If no session, redirect to auth starting with phone screen
  if (!session) {
    console.log('RootLayoutNav: No session, showing auth flow');
    return (
      <ThemeProvider value={theme}>
        <View style={{ flex: 1, backgroundColor }}>
          <Stack screenOptions={{ 
            headerShown: false,
            contentStyle: { backgroundColor }
          }}>
            <Stack.Screen name="auth/phone" options={{ animation: 'none' }} />
            <Stack.Screen name="auth/verify" options={{ animation: 'slide_from_right' }} />
            <Stack.Screen name="auth/create-profile" options={{ animation: 'slide_from_right' }} />
          </Stack>
          <StatusBar style={systemColorScheme === 'dark' ? 'light' : 'dark'} />
        </View>
      </ThemeProvider>
    );
  }

  // User has session but no profile - redirect to create profile screen
  if (!profile) {
    console.log('RootLayoutNav: Has session but no profile, showing profile creation');
    return (
      <ThemeProvider value={theme}>
        <View style={{ flex: 1, backgroundColor }}>
          <Stack screenOptions={{ 
            headerShown: false,
            contentStyle: { backgroundColor }
          }}>
            <Stack.Screen name="auth/create-profile" options={{ animation: 'none' }} />
          </Stack>
          <StatusBar style={systemColorScheme === 'dark' ? 'light' : 'dark'} />
        </View>
      </ThemeProvider>
    );
  }
  
  // User has a session and profile - show main app
  console.log('RootLayoutNav: User authenticated with profile, showing main app');
  return (
    <ThemeProvider value={theme}>
      <View style={{ flex: 1, backgroundColor }}>
        <Stack screenOptions={{ contentStyle: { backgroundColor } }}>
          <Stack.Screen name="(main)" options={{ headerShown: false }} />
          <Stack.Screen name="+not-found" />
          <Stack.Screen name="auth/create-profile" options={{ headerShown: false }} />
          <Stack.Screen name="site/[id]" options={{ headerTitle: 'Site Details' }} />
          <Stack.Screen name="site/[id]/tasks" options={{ headerTitle: 'Tasks' }} />
          <Stack.Screen name="site/[id]/create-task" options={{ headerTitle: 'Create Task' }} />
          <Stack.Screen name="site/[id]/edit-task" options={{ headerTitle: 'Edit Task' }} />
          <Stack.Screen name="site/[id]/task-details" options={{ headerTitle: 'Task Details' }} />
          <Stack.Screen name="site/[id]/materials" options={{ headerTitle: 'Materials' }} />
          <Stack.Screen name="site/[id]/add-material" options={{ headerTitle: 'Add Material' }} />
          <Stack.Screen name="site/[id]/material-details" options={{ headerTitle: 'Material Details' }} />
          <Stack.Screen name="site/[id]/edit-material" options={{ headerTitle: 'Edit Material' }} />
          <Stack.Screen name="site/[id]/members" options={{ headerTitle: 'Team Members' }} />
          <Stack.Screen name="site/[id]/add-member" options={{ headerTitle: 'Add Member' }} />
          <Stack.Screen name="site/[id]/attendance" options={{ headerTitle: 'Attendance' }} />
          <Stack.Screen name="site/[id]/reports" options={{ headerTitle: 'Reports' }} />
        </Stack>
        <StatusBar style={systemColorScheme === 'dark' ? 'light' : 'dark'} />
      </View>
    </ThemeProvider>
  );
}

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });
  const systemColorScheme = useSystemColorScheme();
  const backgroundColor = systemColorScheme === 'dark' ? DarkTheme.colors.background : DefaultTheme.colors.background;

  if (!loaded) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor }}>
        <ActivityIndicator size="large" color="#f97316" />
      </View>
    );
  }

  return (
    <AuthProvider>
      <RootLayoutNav />
    </AuthProvider>
  );
}
