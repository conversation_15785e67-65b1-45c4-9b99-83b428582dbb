{"expo": {"name": "Infratask", "slug": "Infratask", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "infratask", "userInterfaceStyle": "automatic", "newArchEnabled": false, "ios": {"supportsTablet": true, "bundleIdentifier": "com.infratasks.app", "version": "1"}, "android": {"icon": "./assets/images/icon.png", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.infratasks.app", "versionCode": 17, "permissions": ["INTERNET", "ACCESS_NETWORK_STATE", "READ_PHONE_STATE", "ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-updates", {"username": "alrabb666"}], ["expo-build-properties", {"android": {"enableProguardInReleaseBuilds": false, "enableShrinkResourcesInReleaseBuilds": false, "usesCleartextTraffic": true, "minSdkVersion": 24, "compileSdkVersion": 35, "targetSdkVersion": 35}, "ios": {"deploymentTarget": "15.1"}}], "expo-web-browser"], "experiments": {"typedRoutes": true}, "updates": {"enabled": true, "checkAutomatically": "ON_LOAD", "fallbackToCacheTimeout": 0}, "extra": {"router": {}, "eas": {"projectId": "93d91aee-fa0a-43c5-8c73-f186099f165d"}}, "owner": "alrabb666"}}