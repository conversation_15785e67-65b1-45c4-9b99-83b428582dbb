import { useColorScheme } from '@/hooks/useColorScheme';
import { FontAwesome5, MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { Pressable, StyleSheet, Text, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface ShopBottomTabsProps {
  activeTab: 'shop' | 'category' | 'cart';
  onTabChange: (tab: 'shop' | 'category' | 'cart') => void;
  cartItemCount?: number;
}

export default function ShopBottomTabs({ activeTab, onTabChange, cartItemCount = 0 }: ShopBottomTabsProps) {
  const colorScheme = useColorScheme();
  const insets = useSafeAreaInsets();
  const primaryColor = '#f97316';

  // Force light mode for shop bottom tabs
  const backgroundColor = '#fff';
  const textColor = '#000';
  const inactiveColor = '#999';
  const borderColor = '#e5e5e5';

  return (
    <View style={[styles.container, { backgroundColor, borderTopColor: borderColor, paddingBottom: 20 + insets.bottom }]}>
      {/* Shop Tab */}
      <Pressable
        style={[styles.tab, activeTab === 'shop' && styles.activeTab]}
        onPress={() => onTabChange('shop')}
      >
        <View style={styles.tabContent}>
          <MaterialIcons
            name="store"
            size={24}
            color={activeTab === 'shop' ? primaryColor : inactiveColor}
          />
          <Text style={[
            styles.tabText,
            { color: activeTab === 'shop' ? primaryColor : inactiveColor }
          ]}>
            Shop
          </Text>
        </View>
      </Pressable>

      {/* Category Tab */}
      <Pressable
        style={[styles.tab, activeTab === 'category' && styles.activeTab]}
        onPress={() => onTabChange('category')}
      >
        <View style={styles.tabContent}>
          <MaterialIcons
            name="category"
            size={24}
            color={activeTab === 'category' ? primaryColor : inactiveColor}
          />
          <Text style={[
            styles.tabText,
            { color: activeTab === 'category' ? primaryColor : inactiveColor }
          ]}>
            Category
          </Text>
        </View>
      </Pressable>

      {/* Cart Tab */}
      <Pressable
        style={[styles.tab, activeTab === 'cart' && styles.activeTab]}
        onPress={() => onTabChange('cart')}
      >
        <View style={styles.tabContent}>
          <View style={styles.cartIconContainer}>
            <FontAwesome5
              name="shopping-cart"
              size={22}
              color={activeTab === 'cart' ? primaryColor : inactiveColor}
            />
            {cartItemCount > 0 && (
              <View style={[styles.badge, { backgroundColor: primaryColor }]}>
                <Text style={styles.badgeText}>
                  {cartItemCount > 99 ? '99+' : cartItemCount}
                </Text>
              </View>
            )}
          </View>
          <Text style={[
            styles.tabText,
            { color: activeTab === 'cart' ? primaryColor : inactiveColor }
          ]}>
            Cart
          </Text>
        </View>
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    borderTopWidth: 1,
    paddingTop: 8,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    borderRadius: 12,
  },
  activeTab: {
    backgroundColor: 'rgba(249, 115, 22, 0.1)',
  },
  tabContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  tabText: {
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
  },
  cartIconContainer: {
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    top: -8,
    right: -8,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
});
