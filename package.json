{"name": "infratask", "main": "./index.js", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "bump-version": "node ./scripts/bump-version.js", "submit-android": "node ./scripts/submit-android.js", "submit-only": "node ./scripts/submit-only.js", "build-android": "eas build --platform android --profile production", "build-dev": "node scripts/build-dev.js", "fix-razorpay": "node scripts/fix-razorpay.js"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-picker/picker": "2.11.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@supabase/storage-js": "^2.7.3", "@supabase/supabase-js": "^2.50.0", "@types/crypto-js": "^4.2.2", "base64-arraybuffer": "^1.0.2", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "expo": "53.0.17", "expo-application": "~6.1.5", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.8", "expo-constants": "~17.1.5", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.4", "expo-file-system": "^18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-location": "^18.1.6", "expo-print": "~14.1.4", "expo-router": "~5.1.3", "expo-sharing": "^13.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-updates": "^0.28.17", "expo-web-browser": "~14.2.0", "isomorphic-ws": "^5.0.0", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.56.2", "react-native": "0.79.5", "react-native-blob-util": "^0.21.2", "react-native-calendars": "^1.1312.0", "react-native-collapsible-tab-view": "^8.0.1", "react-native-crypto-js": "^1.0.0", "react-native-dropdown-picker": "^5.4.6", "react-native-gesture-handler": "~2.24.0", "react-native-html-to-pdf": "^0.12.0", "react-native-paper": "^5.14.0", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.20.0", "react-native-websocket": "^1.0.2", "react-native-webview": "13.13.5", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/react-native-razorpay": "^2.2.6", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "overrides": {"react": "19.0.0", "react-dom": "19.0.0"}, "private": true}