-- Migration: Create function to automatically create standard subscription for added users
-- This function will be called when a premium user adds a new user to their subscription

-- Create function to create standard subscription for added users
CREATE OR REPLACE FUNCTION create_standard_subscription_for_user(
  p_added_user_id UUID,
  p_premium_user_id UUID,
  p_subscription_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_premium_subscription user_subscriptions%ROWTYPE;
  v_existing_subscription user_subscriptions%ROWTYPE;
  v_standard_plan subscription_plans%ROWTYPE;
  v_new_subscription user_subscriptions%ROWTYPE;
  v_result JSON;
BEGIN
  -- Log the function call
  RAISE LOG 'Creating standard subscription for user: % by premium user: %', p_added_user_id, p_premium_user_id;

  -- Verify that the premium user has an active premium subscription
  SELECT * INTO v_premium_subscription
  FROM user_subscriptions
  WHERE user_id = p_premium_user_id
    AND id = p_subscription_id
    AND plan_id = 'premium'
    AND status = 'active';

  IF NOT FOUND THEN
    RAISE LOG 'Premium subscription not found or not active for user: %', p_premium_user_id;
    RETURN json_build_object(
      'success', false,
      'error', 'Premium subscription not found or not active'
    );
  END IF;

  -- Check if the added user already has an active subscription
  SELECT * INTO v_existing_subscription
  FROM user_subscriptions
  WHERE user_id = p_added_user_id
    AND status = 'active';

  IF FOUND THEN
    RAISE LOG 'User already has an active subscription: %', p_added_user_id;
    RETURN json_build_object(
      'success', true,
      'message', 'User already has an active subscription',
      'existing_subscription', row_to_json(v_existing_subscription)
    );
  END IF;

  -- Get the standard plan details
  SELECT * INTO v_standard_plan
  FROM subscription_plans
  WHERE name = 'standard'
    AND is_active = true;

  IF NOT FOUND THEN
    RAISE LOG 'Standard plan not found';
    RETURN json_build_object(
      'success', false,
      'error', 'Standard plan not found'
    );
  END IF;

  -- Create the standard subscription with dates matching the premium subscription
  INSERT INTO user_subscriptions (
    user_id,
    plan_id,
    status,
    current_period_start,
    current_period_end,
    additional_users,
    auto_renew,
    created_at,
    updated_at
  ) VALUES (
    p_added_user_id,
    'standard',
    'active',
    v_premium_subscription.current_period_start,
    v_premium_subscription.current_period_end,
    0,
    false, -- Standard plans created this way don't auto-renew
    NOW(),
    NOW()
  )
  RETURNING * INTO v_new_subscription;

  RAISE LOG 'Standard subscription created successfully for user: %', p_added_user_id;

  -- Return success response
  RETURN json_build_object(
    'success', true,
    'message', 'Standard subscription created successfully for added user',
    'subscription', row_to_json(v_new_subscription)
  );

EXCEPTION
  WHEN OTHERS THEN
    RAISE LOG 'Error creating standard subscription: %', SQLERRM;
    RETURN json_build_object(
      'success', false,
      'error', 'Failed to create standard subscription',
      'details', SQLERRM
    );
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION create_standard_subscription_for_user(UUID, UUID, UUID) TO authenticated;

-- Create a trigger function to automatically call the subscription creation function
-- when a user is added to a premium subscription
CREATE OR REPLACE FUNCTION trigger_create_standard_subscription()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_premium_subscription user_subscriptions%ROWTYPE;
  v_result JSON;
BEGIN
  -- Only proceed if this is a new active subscription user
  IF NEW.status = 'active' AND (OLD IS NULL OR OLD.status != 'active') THEN
    
    -- Check if the subscription belongs to a premium user
    SELECT * INTO v_premium_subscription
    FROM user_subscriptions
    WHERE id = NEW.subscription_id
      AND plan_id = 'premium'
      AND status = 'active';

    IF FOUND THEN
      RAISE LOG 'Premium subscription detected, creating standard subscription for added user: %', NEW.user_id;
      
      -- Call the function to create standard subscription
      SELECT create_standard_subscription_for_user(
        NEW.user_id,
        v_premium_subscription.user_id,
        NEW.subscription_id
      ) INTO v_result;
      
      RAISE LOG 'Standard subscription creation result: %', v_result;
    END IF;
  END IF;

  RETURN NEW;
END;
$$;

-- Create the trigger on subscription_users table
DROP TRIGGER IF EXISTS trigger_create_standard_subscription_on_user_add ON subscription_users;
CREATE TRIGGER trigger_create_standard_subscription_on_user_add
  AFTER INSERT OR UPDATE ON subscription_users
  FOR EACH ROW
  EXECUTE FUNCTION trigger_create_standard_subscription();

-- Add comment for documentation
COMMENT ON FUNCTION create_standard_subscription_for_user(UUID, UUID, UUID) IS 
'Creates a standard subscription for a user added by a premium user. The subscription period matches the premium user''s subscription dates.';

COMMENT ON FUNCTION trigger_create_standard_subscription() IS 
'Trigger function that automatically creates standard subscriptions when users are added to premium subscriptions.';

COMMENT ON TRIGGER trigger_create_standard_subscription_on_user_add ON subscription_users IS 
'Automatically creates standard subscriptions for users added to premium subscriptions.';
