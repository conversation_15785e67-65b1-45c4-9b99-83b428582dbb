# In-App Updates Implementation Guide

## Overview

This implementation provides comprehensive in-app update functionality for the Infratask Android app, supporting both **native Android Play Core updates** and **Expo OTA updates**.

## Features

### ✅ Native Android Play Core Updates
- **Immediate Updates**: Force users to update before continuing
- **Flexible Updates**: Allow users to continue using the app while downloading
- **Update Priority**: Automatic determination based on staleness and priority
- **Progress Tracking**: Real-time download progress monitoring
- **Background Downloads**: Updates download in the background

### ✅ Expo OTA Updates
- **Over-the-Air Updates**: Instant updates without Play Store
- **Automatic Checking**: Updates checked on app launch
- **Fallback Support**: Seamless fallback between update methods

### ✅ Comprehensive Management
- **Dual Update System**: Combines both native and Expo updates
- **Smart Prioritization**: Native updates take priority when available
- **Automatic Detection**: Determines best update method automatically
- **Event Handling**: Complete event system for update lifecycle

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    App Launch                               │
└─────────────────┬───────────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────────┐
│         Comprehensive Update Manager                       │
│  ┌─────────────────────┐  ┌─────────────────────────────┐   │
│  │   Native Updates    │  │     Expo Updates           │   │
│  │   (Play Core)       │  │     (OTA)                  │   │
│  └─────────────────────┘  └─────────────────────────────┘   │
└─────────────────┬───────────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────────┐
│              Update UI Components                          │
│  ┌─────────────────────┐  ┌─────────────────────────────┐   │
│  │  Update Prompts     │  │   Progress Indicators      │   │
│  │  (Immediate/Flex)   │  │   (Download/Install)       │   │
│  └─────────────────────┘  └─────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## Implementation Files

### Core Libraries
- `lib/comprehensiveUpdateManager.ts` - Main update orchestrator
- `lib/playStoreUpdates.ts` - Custom Play Store update logic
- `lib/nativeInAppUpdate.ts` - Native module interface
- `hooks/usePlayStoreUpdates.ts` - React hooks for updates

### UI Components
- `components/PlayStoreUpdatePrompt.tsx` - Modern update dialog
- `components/UpdateManagerDemo.tsx` - Testing component

### Native Android Code
- `android/app/src/main/java/com/infratasks/app/InAppUpdateModule.kt`
- `android/app/src/main/java/com/infratasks/app/InAppUpdatePackage.kt`

### Configuration
- `app.json` - Expo updates configuration
- `android/app/build.gradle` - Play Core dependencies

## Usage

### Basic Integration

```typescript
import { comprehensiveUpdateManager } from '@/lib/comprehensiveUpdateManager';

// Initialize on app launch
useEffect(() => {
  const initUpdates = async () => {
    await comprehensiveUpdateManager.initialize();
    await comprehensiveUpdateManager.checkAndPromptForUpdates();
  };
  initUpdates();
}, []);
```

### Manual Update Check

```typescript
const checkForUpdates = async () => {
  const updateInfo = await comprehensiveUpdateManager.checkForUpdates(true);
  if (updateInfo) {
    await comprehensiveUpdateManager.startUpdateFlow(updateInfo);
  }
};
```

### Using React Hooks

```typescript
import { usePlayStoreUpdates } from '@/hooks/usePlayStoreUpdates';

function MyComponent() {
  const { updateAvailable, updateInfo, checkForUpdates, startUpdateFlow } = usePlayStoreUpdates();
  
  return (
    <View>
      {updateAvailable && (
        <Button onPress={() => startUpdateFlow()} title="Update Available" />
      )}
    </View>
  );
}
```

## Configuration Options

### Update Manager Configuration

```typescript
comprehensiveUpdateManager.configure({
  enableNativeUpdates: true,        // Enable Play Core updates
  enableCustomUpdates: true,        // Enable custom update logic
  prioritizeNativeUpdates: true,    // Prefer native over custom
  autoCheckInterval: 24 * 60 * 60 * 1000, // Check every 24 hours
  staleDaysForImmediate: 7,         // Force immediate after 7 days
  highPriorityThreshold: 4,         // Priority 4+ = immediate
});
```

### Update Types

#### Immediate Updates
- **When**: Critical updates, security fixes, high priority
- **Behavior**: Blocks app usage until updated
- **Triggers**: 
  - Update priority ≥ 4
  - App stale for ≥ 7 days
  - Mandatory flag set

#### Flexible Updates
- **When**: Feature updates, bug fixes, improvements
- **Behavior**: Downloads in background, prompts to restart
- **Triggers**: 
  - Normal priority updates
  - Non-mandatory updates
  - User preference

## Testing

### Prerequisites
1. App installed from Google Play Store (not sideloaded)
2. Higher version uploaded to Play Console
3. Same package ID and signing certificate
4. Testing account with access

### Testing Steps

1. **Build and Upload**:
   ```bash
   # Increment version in app.json
   eas build --platform android --profile production
   # Upload to Google Play Console (Internal Testing)
   ```

2. **Test Native Updates**:
   - Install from Play Store
   - Ensure device version < console version
   - Launch app to trigger update check

3. **Test Expo Updates**:
   ```bash
   eas update --branch production
   ```

4. **Debug with Demo Component**:
   ```typescript
   import { UpdateManagerDemo } from '@/components/UpdateManagerDemo';
   // Add to any screen for testing
   ```

## Deployment

### Production Checklist

- [ ] Version code incremented in `app.json`
- [ ] Release notes prepared
- [ ] Update priority set (1-5 scale)
- [ ] Testing completed on Internal Testing track
- [ ] Rollout percentage configured
- [ ] Monitoring dashboard ready

### Release Process

1. **Prepare Release**:
   ```bash
   node scripts/bump-version.js
   eas build --platform android --profile production
   ```

2. **Upload to Play Console**:
   - Upload AAB to Production track
   - Set rollout percentage (start with 5-10%)
   - Configure update priority if needed

3. **Monitor Rollout**:
   - Watch crash reports
   - Monitor update adoption rates
   - Increase rollout percentage gradually

## Troubleshooting

### Common Issues

#### "Update not available"
- Check version codes (device < console)
- Verify package ID matches
- Ensure app installed from Play Store
- Check signing certificate

#### "Native module not found"
- Rebuild app: `eas build --platform android`
- Check MainApplication.kt includes InAppUpdatePackage
- Verify Play Core dependencies in build.gradle

#### "Update check fails"
- Check network connectivity
- Verify Google Play Services
- Check app permissions
- Review error logs

### Debug Commands

```bash
# Check current configuration
node scripts/setup-in-app-updates.js

# Test update flow
adb logcat | grep -i "update\|play\|core"

# Check app version
adb shell dumpsys package com.infratasks.app | grep version
```

## Best Practices

### Update Strategy
1. **Gradual Rollout**: Start with 5-10% of users
2. **Monitor Metrics**: Watch adoption and crash rates
3. **Staged Updates**: Use Internal Testing → Production
4. **Emergency Updates**: Use immediate updates sparingly

### User Experience
1. **Clear Messaging**: Explain update benefits
2. **Flexible Timing**: Allow users to choose when to update
3. **Progress Feedback**: Show download/install progress
4. **Minimal Disruption**: Use flexible updates when possible

### Technical
1. **Version Management**: Consistent version incrementing
2. **Error Handling**: Graceful fallbacks for update failures
3. **Testing**: Comprehensive testing on multiple devices
4. **Monitoring**: Track update success/failure rates

## Monitoring and Analytics

### Key Metrics
- Update adoption rate
- Update completion time
- Update failure rate
- User abandonment during updates

### Implementation
```typescript
// Track update events
comprehensiveUpdateManager.addEventListener('onUpdateDownloaded', () => {
  // Analytics: Update downloaded
});

comprehensiveUpdateManager.addEventListener('onUpdateFailed', (error) => {
  // Analytics: Update failed
  // Error reporting
});
```

## Support

For issues or questions:
1. Check this documentation
2. Review Android Play Core documentation
3. Check Expo Updates documentation
4. Test with UpdateManagerDemo component
5. Review logs and error messages

---

**Last Updated**: January 2025  
**Version**: 1.0.0  
**Compatibility**: Android API 24+, Expo SDK 53+
