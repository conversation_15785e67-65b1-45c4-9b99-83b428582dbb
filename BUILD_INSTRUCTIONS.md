# Build Instructions for Infratask App with New Keystore

## Step 1: Find and Use Java/Keytool

### Option A: Use Android Studio's Java
If you have Android Studio installed, find keytool at:
```
C:\Program Files\Android\Android Studio\jbr\bin\keytool.exe
```

### Option B: Use System Java
If you have Java installed, try:
```
where java
```
Then use the keytool from the same directory.

## Step 2: Generate Keystore Manually

Open Command Prompt (not PowerShell) and run:

```cmd
cd "C:\Users\<USER>\Downloads\Constructions management app\Infratask\android\app"

"C:\Program Files\Android\Android Studio\jbr\bin\keytool.exe" -genkeypair -v -storetype PKCS12 -keystore infratask-release-key.keystore -alias infratask-key-alias -keyalg RSA -keysize 2048 -validity 10000
```

When prompted, enter:
- Keystore password: `Mansoor@24`
- Key password: `Mansoor@24` (or press Enter to use same as keystore)
- First name and last name: `Infratask`
- Organizational unit: `Development`
- Organization: `Infratask`
- City: `Your City`
- State: `Your State`
- Country code: `US` (or your country code)

## Step 3: Verify Keystore Creation

Check if the file was created:
```cmd
dir infratask-release-key.keystore
```

## Step 4: Build Release AAB

### Option A: Using Expo CLI (Recommended)
```cmd
cd "C:\Users\<USER>\Downloads\Constructions management app\Infratask"
npx expo run:android --variant release
```

### Option B: Using Gradle (if Java issues are resolved)
```cmd
cd "C:\Users\<USER>\Downloads\Constructions management app\Infratask\android"
.\gradlew bundleRelease
```

### Option C: Using EAS Build (Cloud Build)
```cmd
cd "C:\Users\<USER>\Downloads\Constructions management app\Infratask"
npx eas build --platform android --profile production
```

## Step 5: Locate Your AAB File

After successful build, your AAB file will be at:
```
android\app\build\outputs\bundle\release\app-release.aab
```

## Troubleshooting

### If Java/Gradle issues persist:
1. Check your Java version: `java -version`
2. Make sure you're using Java 17 or compatible
3. Use EAS Build (cloud build) as alternative

### If keytool not found:
1. Install Java JDK from Oracle or OpenJDK
2. Or use Android Studio's bundled Java

## Verification

To verify your keystore SHA1 fingerprint:
```cmd
"C:\Program Files\Android\Android Studio\jbr\bin\keytool.exe" -list -v -keystore infratask-release-key.keystore -alias infratask-key-alias
```

The SHA1 fingerprint should be different from the debug keystore and match what Google Play expects.

## Security Reminder

- ✅ Your gradle.properties is configured correctly
- ✅ Your build.gradle is configured correctly
- ⚠️ Keep your keystore file safe and backed up
- ⚠️ Never share your keystore passwords 