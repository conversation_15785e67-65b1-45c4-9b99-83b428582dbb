import React from 'react';
import { Alert, StyleSheet, View } from 'react-native';
import { Button, Card, Text } from 'react-native-paper';
import ExportButtons from '../components/ExportButtons';
import { useAuth } from '../context/AuthContext';
import {
    exportAttendanceAsExcel,
    exportAttendanceAsPDF,
    exportMaterialsAsExcel,
    exportMaterialsAsPDF,
    exportTaskReportAsExcel,
    exportTaskReportAsPDF,
    type Attendance,
    type Material,
    type Task,
    type TaskSubcategory
} from '../lib/export';
import { canExportReports, canManageReportTemplates, canViewReports } from '../lib/permissions';

type Site = {
  id: string;
  name: string;
};

type ReportsSectionProps = {
  site: Site;
  tasks: Task[];
  attendance: Attendance[];
  materials: Material[];
  subcategories: Record<string, TaskSubcategory[]>;
};

const ReportsSection: React.FC<ReportsSectionProps> = ({ site, tasks, attendance, materials, subcategories }) => {
  const { profile } = useAuth();
  const userRole = profile?.role as 'Super Admin' | 'Admin' | 'Member' | null;
  
  // Don't render anything for Members (no access)
  if (!canViewReports(userRole)) {
    return null;
  }

  const handleTaskExport = async (type: 'pdf' | 'excel') => {
    if (!canExportReports(userRole)) {
      Alert.alert('Permission Denied', 'You do not have permission to export reports.');
      return;
    }
    
    try {
      if (type === 'pdf') {
        await exportTaskReportAsPDF(tasks, subcategories, site.name);
      } else {
        const task = tasks[0];
        if (task) {
          await exportTaskReportAsExcel(task, subcategories[task.id] || [], site.name, 'System');
        }
      }
    } catch (error) {
      console.error('Error exporting tasks:', error);
      Alert.alert('Export Error', 'An error occurred while exporting the tasks report.');
    }
  };

  const handleAttendanceExport = async (type: 'pdf' | 'excel') => {
    if (!canExportReports(userRole)) {
      Alert.alert('Permission Denied', 'You do not have permission to export reports.');
      return;
    }
    
    try {
      if (type === 'pdf') {
        await exportAttendanceAsPDF(attendance, site.name);
      } else {
        await exportAttendanceAsExcel(attendance, site.name);
      }
    } catch (error) {
      console.error('Error exporting attendance:', error);
      Alert.alert('Export Error', 'An error occurred while exporting the attendance report.');
    }
  };

  const handleMaterialsExport = async (type: 'pdf' | 'excel') => {
    if (!canExportReports(userRole)) {
      Alert.alert('Permission Denied', 'You do not have permission to export reports.');
      return;
    }
    
    try {
      if (type === 'pdf') {
        await exportMaterialsAsPDF(materials, site.name);
      } else {
        await exportMaterialsAsExcel(materials, site.name);
      }
    } catch (error) {
      console.error('Error exporting materials:', error);
      Alert.alert('Export Error', 'An error occurred while exporting the materials report.');
    }
  };

  // Only Super Admin can manage report templates
  const handleManageTemplates = () => {
    if (!canManageReportTemplates(userRole)) {
      Alert.alert('Permission Denied', 'Only Super Admins can manage report templates.');
      return;
    }
    
    // Navigation to template management would go here
    Alert.alert('Template Management', 'Template management functionality coming soon.');
  };

  return (
    <Card style={styles.card}>
      <Card.Title 
        title="Reports" 
        right={props => 
          canManageReportTemplates(userRole) ? (
            <Button 
              mode="contained" 
              compact 
              onPress={handleManageTemplates} 
              style={styles.manageButton}
            >
              Manage Templates
            </Button>
          ) : null
        } 
      />
      <Card.Content>
        <View style={styles.reportSection}>
          <Text variant="titleMedium">Tasks Report</Text>
          <ExportButtons 
            onPdfExport={() => handleTaskExport('pdf')}
            onExcelExport={() => handleTaskExport('excel')}
          />
        </View>

        <View style={styles.reportSection}>
          <Text variant="titleMedium">Attendance Report</Text>
          <ExportButtons 
            onPdfExport={() => handleAttendanceExport('pdf')}
            onExcelExport={() => handleAttendanceExport('excel')}
          />
        </View>

        <View style={styles.reportSection}>
          <Text variant="titleMedium">Materials Report</Text>
          <ExportButtons 
            onPdfExport={() => handleMaterialsExport('pdf')}
            onExcelExport={() => handleMaterialsExport('excel')}
          />
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    margin: 16,
  },
  reportSection: {
    marginVertical: 12,
    gap: 8,
  },
  manageButton: {
    marginRight: 16,
  },
});

export default ReportsSection; 