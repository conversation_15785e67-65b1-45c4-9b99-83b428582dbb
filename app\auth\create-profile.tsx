import { useTheme } from '@react-navigation/native';
import * as FileSystem from 'expo-file-system';
import { Image } from 'expo-image';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { ActivityIndicator, Alert, Platform, ScrollView, StyleSheet, TextInput, TouchableOpacity, View, useColorScheme } from 'react-native';
import { ThemedText } from '../../components/ThemedText';
import { ThemedView } from '../../components/ThemedView';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../lib/supabase';

type FormData = {
  fullName: string;
  email: string;
  city: string;
  role: string;
};

// Define roles for the dropdown
const ROLES = [
  'Supervisor',
  'Site Engineer',
  'Manager',
  'Accounts',
  'Admin',
  'Skilled-worker',
  'Labourer',
  'Site owner',
  'Others'
];

export default function CreateProfileScreen() {
  const { user, refreshProfile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [initialRender, setInitialRender] = useState(true);
  const [imageUri, setImageUri] = useState<string | null>(null);
  const [imageBase64, setImageBase64] = useState<string | null>(null); // Store base64 as backup
  const [uploadingImage, setUploadingImage] = useState(false);
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [showRoles, setShowRoles] = useState(false);
  const colorScheme = useColorScheme();
  const theme = useTheme();
  
  // Use theme-based colors
  const backgroundColor = colorScheme === 'dark' ? theme.colors.background : '#ffffff';
  const textColor = colorScheme === 'dark' ? theme.colors.text : '#000000';
  const inputBgColor = colorScheme === 'dark' ? theme.colors.card : '#F2F2F2';
  const placeholderColor = colorScheme === 'dark' ? theme.colors.border : '#A0A0A0';
  const cardColor = colorScheme === 'dark' ? theme.colors.card : '#ffffff';
  const borderColor = colorScheme === 'dark' ? theme.colors.border : '#EEEEEE';
  
  const { control, handleSubmit, formState: { errors }, setValue } = useForm<FormData>({
    defaultValues: {
      fullName: '',
      email: '',
      city: '',
      role: ''
    }
  });

  // Set initial render to false after a short delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setInitialRender(false);
    }, 300);
    
    return () => {
      clearTimeout(timer);
    };
  }, []);

  // Check authentication status
  useEffect(() => {
    // First rely on the context user as the primary source of truth
    if (user) {
      console.log('Create Profile: Using authenticated user from context', { userId: user.id });
      
      // Pre-fill any existing profile data if available
      const fetchProfileData = async () => {
        try {
          const { data: profile, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('user_id', user.id)
            .single();
            
          if (!error && profile) {
            console.log('Found existing profile data, pre-filling form');
            setValue('fullName', profile.full_name || '');
            setValue('email', profile.email || '');
            setValue('city', profile.city || '');
            setValue('role', profile.role || '');
            setSelectedRole(profile.role || null);
            
            if (profile.profile_image_url) {
              setImageUri(profile.profile_image_url);
            }
          }
        } catch (err) {
          console.error('Error fetching profile data:', err);
        }
      };
      
      fetchProfileData();
      return;
    }
    
    // Fallback to getUser if context user is not available
    const checkAuth = async () => {
      const { data } = await supabase.auth.getUser();
      console.log('Create Profile: Auth check', { hasUser: !!data.user });
      
      if (!data.user) {
        console.log('Create Profile: No user, redirecting to login');
        router.replace('/auth/phone');
        return;
      }

      // Pre-fill any existing profile data if available
      try {
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('user_id', data.user.id)
          .single();
          
        if (!error && profile) {
          console.log('Found existing profile data, pre-filling form');
          setValue('fullName', profile.full_name || '');
          setValue('email', profile.email || '');
          setValue('city', profile.city || '');
          setValue('role', profile.role || '');
          setSelectedRole(profile.role || null);
          
          if (profile.profile_image_url) {
            setImageUri(profile.profile_image_url);
          }
        }
      } catch (err) {
        console.error('Error fetching profile data:', err);
      }
    };
    
    checkAuth();
  }, [user]);

  const pickImage = async () => {
    try {
      // Request permissions first
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (!permissionResult.granted) {
        Alert.alert('Permission Required', 'You need to grant permission to access your photos');
        return;
      }
      
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: "images",
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.5,
        base64: true, // Request base64 data to have a fallback
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const selectedAsset = result.assets[0];
        console.log('Image selected:', selectedAsset.uri);
        
        // Store the uri and base64 data for upload
        setImageUri(selectedAsset.uri);
        
        // Store base64 data if available for fallback
        if (selectedAsset.base64) {
          console.log('Base64 data available for fallback');
          setImageBase64(selectedAsset.base64);
        } else {
          // If base64 is not available, try to read the file directly
          try {
            if (selectedAsset.uri.startsWith('file://') || selectedAsset.uri.startsWith('/')) {
              const base64 = await FileSystem.readAsStringAsync(selectedAsset.uri, {
                encoding: FileSystem.EncodingType.Base64,
              });
              setImageBase64(base64);
              console.log('Generated base64 from file');
            }
          } catch (error) {
            console.error('Error reading file as base64:', error);
          }
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };

  const uploadProfileImage = async (userId: string): Promise<string | null> => {
    if (!imageUri && !imageBase64) return null;
    
    try {
      setUploadingImage(true);
      console.log('Starting image upload process...');
      
      // Set file info - using a simpler path structure with just the filename
      const fileExt = 'jpeg';
      const fileName = `${userId}-${Date.now()}.${fileExt}`;
      
      // Use the confirmed bucket name directly
      const bucketName = 'user-profiles';
      console.log(`Using bucket: ${bucketName} for upload`);
      
      let uploadedUrl = null;
      
      // Try using base64 data first
      if (imageBase64) {
        try {
          console.log('Using direct base64 upload method...');
          
          // Create the Uint8Array from base64
          const decodedData = decode(imageBase64);
          console.log(`Decoded data length: ${decodedData.length} bytes`);
          
          // Upload to Supabase
          const { data: uploadData, error: uploadError } = await supabase.storage
            .from(bucketName)
            .upload(fileName, decodedData, {
              contentType: 'image/jpeg',
              upsert: true
            });
            
          if (uploadError) {
            console.error('Upload error:', uploadError);
            console.log('Continuing to try file URI method');
          } else {
            console.log('Image uploaded successfully with base64:', uploadData);
            
            // Get public URL
            const { data } = supabase.storage
              .from(bucketName)
              .getPublicUrl(fileName);
              
            console.log('Image public URL:', data.publicUrl);
            uploadedUrl = data.publicUrl;
          }
        } catch (error) {
          console.error('Error uploading with base64:', error);
        }
      }
      
      // If base64 upload failed and we have a URI, try with file URI
      if (!uploadedUrl && imageUri) {
        try {
          console.log('Falling back to file URI method...');
          
          // For native platforms
          if (Platform.OS !== 'web' && (imageUri.startsWith('file://') || imageUri.startsWith('/'))) {
            try {
              const base64 = await FileSystem.readAsStringAsync(imageUri, {
                encoding: FileSystem.EncodingType.Base64,
              });
              
              if (base64) {
                console.log('Successfully read file as base64, attempting upload...');
                
                // Create the Uint8Array from base64
                const decodedData = decode(base64);
                console.log(`Decoded data length: ${decodedData.length} bytes`);
                
                // Upload to Supabase
                const { data: uploadData, error: uploadError } = await supabase.storage
                  .from(bucketName)
                  .upload(fileName, decodedData, {
                    contentType: 'image/jpeg',
                    upsert: true
                  });
                  
                if (uploadError) {
                  console.error('Upload error:', uploadError);
                } else {
                  console.log('Image uploaded successfully from file:', uploadData);
                  
                  // Get public URL
                  const { data } = supabase.storage
                    .from(bucketName)
                    .getPublicUrl(fileName);
                    
                  console.log('Image public URL:', data.publicUrl);
                  uploadedUrl = data.publicUrl;
                }
              }
            } catch (error) {
              console.error('Error reading or uploading file:', error);
            }
          }
        } catch (error) {
          console.error('Error with file URI method:', error);
        }
      }
      
      if (!uploadedUrl) {
        console.warn('Could not upload image. Proceeding without profile image.');
      }
      
      return uploadedUrl;
      
    } catch (error) {
      console.error('Error uploading image:', error);
      Alert.alert('Error', 'Failed to upload profile image. You can update it later.');
      return null;
    } finally {
      setUploadingImage(false);
    }
  };
  
  // Helper function to decode Base64 to Uint8Array for Supabase
  const decode = (base64: string): Uint8Array => {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
  };

  const onSubmit = async (data: FormData) => {
    try {
      setLoading(true);
      
      // Use user from auth context if available
      let userId;
      let phoneNumber = null;
      if (user) {
        userId = user.id;
        // Get the phone number from the authenticated user's data
        phoneNumber = user.phone;
        console.log('Using user from auth context', { userId, phoneNumber });
      } else {
        // Fallback to getUser if context user is not available
        const { data: userData, error } = await supabase.auth.getUser();
        
        if (error) {
          console.error('Error fetching user:', error);
          Alert.alert('Authentication Error', 'Please sign in again');
          router.replace('/auth/phone');
          return;
        }
        
        if (!userData.user) {
          console.error('User not authenticated');
          Alert.alert('Authentication Error', 'Please sign in to continue');
          router.replace('/auth/phone');
          return;
        }
        
        userId = userData.user.id;
        phoneNumber = userData.user.phone;
        console.log('Using user from getUser', { userId, phoneNumber });
      }
      
      // Upload profile image if selected
      let profileImageUrl = null;
      if (imageUri || imageBase64) {
        console.log('Starting profile image upload...');
        profileImageUrl = await uploadProfileImage(userId);
        if (!profileImageUrl) {
          console.warn('Image upload failed, continuing with profile creation without image');
        } else {
          console.log('Profile image uploaded successfully');
        }
      } else {
        console.log('No image selected, skipping image upload');
      }
      
      // Check if profile exists first
      const { data: existingProfile, error: fetchError } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .single();
        
      let updateError;
      
      if (fetchError || !existingProfile) {
        // Create new profile
        console.log('Creating new profile');
        const { error } = await supabase
          .from('profiles')
          .insert({
            user_id: userId,
            full_name: data.fullName,
            email: data.email,
            city: data.city,
            role: data.role,
            phone_number: phoneNumber, // Save the phone number in the profile
            profile_image_url: profileImageUrl,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
        updateError = error;
      } else {
        // Update existing profile
        console.log('Updating existing profile');
        const { error } = await supabase
          .from('profiles')
          .update({
            full_name: data.fullName,
            email: data.email,
            city: data.city,
            role: data.role,
            phone_number: phoneNumber, // Update the phone number in the profile
            profile_image_url: profileImageUrl || existingProfile.profile_image_url, // Keep existing image if upload failed
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId);
        updateError = error;
      }

      if (updateError) throw updateError;
      
      // Refresh profile in auth context to reflect changes
      await refreshProfile();
      
      // Navigate to main app
      console.log('Profile created, navigating to main app...');
      router.replace('/(tabs)');
      
    } catch (error: any) {
      console.error('Profile creation error:', error.message);
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  const toggleRolesDropdown = () => {
    setShowRoles(!showRoles);
  };

  const selectRole = (role: string) => {
    setSelectedRole(role);
    setValue('role', role);
    setShowRoles(false);
  };

  // Show loading indicator during initial render
  if (initialRender) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor }}>
        <ActivityIndicator size="large" color="#f97316" />
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor }}>
      <ThemedView style={[styles.container, { backgroundColor }]}>
        <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
          <View style={styles.content}>
            <ThemedText style={styles.title}>Create Profile</ThemedText>
            <ThemedText style={styles.subtitle}>
              Complete your profile details to continue
            </ThemedText>

            {/* Profile Image Section */}
            <View style={styles.profileImageSection}>
              <TouchableOpacity style={[styles.profileImageContainer, { borderColor: borderColor }]} onPress={pickImage}>
                {imageUri ? (
                  <Image
                    source={{ uri: imageUri }}
                    style={styles.profileImage}
                    contentFit="cover"
                    transition={200}
                  />
                ) : (
                  <View style={[styles.profileImagePlaceholder, { backgroundColor: inputBgColor }]}>
                    <ThemedText style={styles.profileImagePlaceholderText}>Add Photo</ThemedText>
                  </View>
                )}
              </TouchableOpacity>
              <TouchableOpacity onPress={pickImage}>
                <ThemedText style={[styles.changePhotoText, { color: '#f97316' }]}>
                  {imageUri ? 'Change Photo' : 'Select Photo'}
                </ThemedText>
              </TouchableOpacity>
            </View>

            <View style={styles.form}>
              {/* Full Name Field */}
              <ThemedText style={styles.label}>Full Name</ThemedText>
              <Controller
                control={control}
                rules={{
                  required: 'Full name is required',
                }}
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    style={[styles.input, { backgroundColor: inputBgColor, color: textColor }]}
                    placeholder="Enter your full name"
                    placeholderTextColor={placeholderColor}
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value}
                  />
                )}
                name="fullName"
              />
              {errors.fullName && (
                <ThemedText style={[styles.errorText, { color: 'red' }]}>{errors.fullName.message}</ThemedText>
              )}

              {/* Email Field */}
              <ThemedText style={styles.label}>Email (Optional)</ThemedText>
              <Controller
                control={control}
                rules={{
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Invalid email address',
                  },
                }}
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    style={[styles.input, { backgroundColor: inputBgColor, color: textColor }]}
                    placeholder="Enter your email"
                    placeholderTextColor={placeholderColor}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value}
                  />
                )}
                name="email"
              />
              {errors.email && (
                <ThemedText style={[styles.errorText, { color: 'red' }]}>{errors.email.message}</ThemedText>
              )}

              {/* City Field */}
              <ThemedText style={styles.label}>City (Optional)</ThemedText>
              <Controller
                control={control}
                render={({ field: { onChange, onBlur, value } }) => (
                  <TextInput
                    style={[styles.input, { backgroundColor: inputBgColor, color: textColor }]}
                    placeholder="Enter your city"
                    placeholderTextColor={placeholderColor}
                    onBlur={onBlur}
                    onChangeText={onChange}
                    value={value}
                  />
                )}
                name="city"
              />

              {/* Role Dropdown */}
              <ThemedText style={styles.label}>Your Role</ThemedText>
              <TouchableOpacity 
                style={[
                  styles.roleSelector, 
                  { backgroundColor: inputBgColor }
                ]} 
                onPress={toggleRolesDropdown}
              >
                <ThemedText style={{ color: selectedRole ? textColor : placeholderColor }}>
                  {selectedRole || 'Select your role'}
                </ThemedText>
              </TouchableOpacity>

              {showRoles && (
                <View style={[styles.rolesDropdown, { backgroundColor: cardColor, borderColor: borderColor }]}>
                  <ScrollView nestedScrollEnabled style={styles.rolesScrollView}>
                    {ROLES.map((role) => (
                      <TouchableOpacity
                        key={role}
                        style={[
                          styles.roleItem,
                          selectedRole === role && { backgroundColor: 'rgba(249, 115, 22, 0.1)' }
                        ]}
                        onPress={() => selectRole(role)}
                      >
                        <ThemedText style={[
                          styles.roleItemText,
                          selectedRole === role && { color: '#f97316', fontWeight: 'bold' }
                        ]}>
                          {role}
                        </ThemedText>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>
              )}

              <TouchableOpacity
                style={[
                  styles.button, 
                  (loading || uploadingImage) && styles.buttonDisabled,
                  { backgroundColor: '#f97316' }
                ]}
                onPress={handleSubmit(onSubmit)}
                disabled={loading || uploadingImage}
              >
                <ThemedText style={[styles.buttonText, { color: 'white' }]}>
                  {(loading || uploadingImage) ? 'Saving...' : 'Save Profile'}
                </ThemedText>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </ThemedView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flexGrow: 1,
    padding: 20,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 20,
  },
  content: {
    flex: 1,
    maxWidth: 400,
    width: '100%',
    alignSelf: 'center',
    paddingVertical: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 30,
    opacity: 0.7,
  },
  profileImageSection: {
    alignSelf: 'center',
    marginBottom: 30,
  },
  profileImageContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 2,
    borderColor: '#E5E5E5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  profileImagePlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#F2F2F2',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderStyle: 'dashed',
  },
  profileImagePlaceholderText: {
    fontSize: 14,
    color: '#666',
  },
  changePhotoText: {
    fontSize: 14,
    color: '#f97316',
    marginTop: 10,
  },
  form: {
    width: '100%',
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#F2F2F2',
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    marginBottom: 20,
    color: '#333',
  },
  roleSelector: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 15,
    marginBottom: 5,
    borderWidth: 1,
    borderColor: '#E5E5E5',
  },
  rolesDropdown: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    marginBottom: 20,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
    }),
  },
  rolesScrollView: {
    maxHeight: 200,
  },
  roleItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  roleItemText: {
    fontSize: 16,
    color: '#333333',
  },
  button: {
    backgroundColor: '#f97316',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    marginTop: 20,
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  errorText: {
    color: 'red',
    marginTop: -15,
    marginBottom: 10,
  },
}); 