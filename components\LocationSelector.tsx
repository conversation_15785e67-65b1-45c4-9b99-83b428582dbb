import { useColorScheme } from '@/hooks/useColorScheme';
import { MaterialIcons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Modal,
    Pressable,
    StyleSheet,
    Text,
    TextInput,
    View
} from 'react-native';

interface LocationData {
  address: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  deliveryTime?: string;
  type?: 'home' | 'work' | 'other';
  label?: string;
}

interface LocationSelectorProps {
  onLocationChange?: (location: LocationData) => void;
  initialLocation?: LocationData;
}

export default function LocationSelector({ onLocationChange, initialLocation }: LocationSelectorProps) {
  const colorScheme = useColorScheme();
  const [currentLocation, setCurrentLocation] = useState<LocationData>(
    initialLocation || { address: 'Detecting location...', deliveryTime: '2 - 4 hours' }
  );
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [manualAddress, setManualAddress] = useState('');
  const [isDetectingLocation, setIsDetectingLocation] = useState(false);
  const [locationPermission, setLocationPermission] = useState<Location.PermissionStatus | null>(null);

  // Mock saved addresses (in real app, this would come from storage/API)
  const [savedAddresses] = useState<LocationData[]>([
    {
      address: 'Home - 123 Main Street, Delhi',
      type: 'home',
      label: 'Home',
      deliveryTime: '2 - 4 hours',
      coordinates: { latitude: 28.6139, longitude: 77.2090 }
    },
    {
      address: 'Office - Tech Park, Bangalore',
      type: 'work',
      label: 'Work',
      deliveryTime: '3 - 5 hours',
      coordinates: { latitude: 12.9716, longitude: 77.5946 }
    }
  ]);

  // Colors based on Zepto/Blinkit design
  const primaryColor = '#f97316';
  const backgroundColor = '#fff';
  const textColor = '#000';
  const secondaryTextColor = '#666';
  const borderColor = '#e5e5e5';

  // Request location permission and get current location
  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      setLocationPermission(status);
      
      if (status === 'granted') {
        await getCurrentLocation();
      } else {
        setCurrentLocation({
          address: 'Location access denied',
          deliveryTime: '2 - 4 hours'
        });
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
      setCurrentLocation({
        address: 'Unable to detect location',
        deliveryTime: '2 - 4 hours'
      });
    }
  };

  // Get current location
  const getCurrentLocation = async () => {
    try {
      setIsDetectingLocation(true);
      
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      const reverseGeocode = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });

      if (reverseGeocode.length > 0) {
        const address = reverseGeocode[0];
        const formattedAddress = `${address.name || ''} ${address.street || ''}, ${address.city || ''}, ${address.region || ''}`.trim();
        
        const locationData: LocationData = {
          address: formattedAddress || 'Current Location',
          coordinates: {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          },
          deliveryTime: calculateDeliveryTime(location.coords.latitude, location.coords.longitude)
        };

        setCurrentLocation(locationData);
        onLocationChange?.(locationData);
      }
    } catch (error) {
      console.error('Error getting current location:', error);
      setCurrentLocation({
        address: 'Unable to detect location',
        deliveryTime: '2 - 4 hours'
      });
    } finally {
      setIsDetectingLocation(false);
    }
  };

  // Calculate delivery time based on location (enhanced mock implementation)
  const calculateDeliveryTime = (latitude: number, longitude: number): string => {
    // Mock delivery time calculation based on distance from service centers
    // In a real app, this would call a delivery estimation API

    // Mock service center coordinates (you can add more)
    const serviceCenters = [
      { lat: 28.6139, lng: 77.2090, name: 'Delhi Center' }, // Delhi
      { lat: 19.0760, lng: 72.8777, name: 'Mumbai Center' }, // Mumbai
      { lat: 12.9716, lng: 77.5946, name: 'Bangalore Center' }, // Bangalore
    ];

    // Calculate distance to nearest service center
    let minDistance = Infinity;
    serviceCenters.forEach(center => {
      const distance = Math.sqrt(
        Math.pow(latitude - center.lat, 2) + Math.pow(longitude - center.lng, 2)
      );
      minDistance = Math.min(minDistance, distance);
    });

    // Convert distance to delivery time (mock calculation)
    // Return delivery time in hours for construction materials
    if (minDistance < 0.1) {
      return '2 - 3 hours'; // Very close
    } else if (minDistance < 0.5) {
      return '2 - 4 hours'; // Nearby
    } else if (minDistance < 1.0) {
      return '3 - 5 hours'; // Moderate distance
    } else {
      return '4 - 6 hours'; // Far
    }
  };

  // Get delivery time estimate for manual address
  const getManualAddressDeliveryTime = (address: string): string => {
    // Mock delivery time based on address keywords
    const lowerAddress = address.toLowerCase();

    if (lowerAddress.includes('delhi') || lowerAddress.includes('mumbai') || lowerAddress.includes('bangalore')) {
      return '2 - 4 hours';
    } else if (lowerAddress.includes('city') || lowerAddress.includes('center') || lowerAddress.includes('main')) {
      return '3 - 5 hours';
    } else {
      return '4 - 6 hours';
    }
  };

  // Handle manual address entry
  const handleManualAddress = () => {
    if (manualAddress.trim()) {
      const locationData: LocationData = {
        address: manualAddress.trim(),
        deliveryTime: getManualAddressDeliveryTime(manualAddress.trim())
      };

      setCurrentLocation(locationData);
      onLocationChange?.(locationData);
      setShowLocationModal(false);
      setManualAddress('');
    } else {
      Alert.alert('Error', 'Please enter a valid address');
    }
  };

  // Auto-detect location on component mount
  useEffect(() => {
    if (!initialLocation) {
      requestLocationPermission();
    }
  }, []);

  return (
    <>
      {/* Location Header */}
      <Pressable 
        style={[styles.locationContainer, { backgroundColor, borderColor }]}
        onPress={() => setShowLocationModal(true)}
      >
        <View style={styles.locationContent}>
          <View style={styles.locationInfo}>
            <View style={styles.deliveryTimeContainer}>
              <MaterialIcons name="flash-on" size={16} color={primaryColor} />
              <Text style={[styles.deliveryTime, { color: primaryColor }]}>
                Delivery in {currentLocation.deliveryTime}
              </Text>
            </View>
            
            <View style={styles.addressContainer}>
              <MaterialIcons
                name={currentLocation.type === 'home' ? 'home' : currentLocation.type === 'work' ? 'work' : 'location-on'}
                size={16}
                color={secondaryTextColor}
              />
              <View style={styles.addressTextContainer}>
                {currentLocation.label && (
                  <Text style={[styles.addressLabel, { color: textColor }]}>
                    {currentLocation.label}
                  </Text>
                )}
                <Text style={[styles.addressText, { color: currentLocation.label ? secondaryTextColor : textColor }]} numberOfLines={1}>
                  {isDetectingLocation ? 'Detecting location...' : currentLocation.address}
                </Text>
              </View>
              {isDetectingLocation && (
                <ActivityIndicator size="small" color={primaryColor} style={styles.loadingIndicator} />
              )}
            </View>
          </View>
          
          <MaterialIcons name="keyboard-arrow-down" size={20} color={secondaryTextColor} />
        </View>
      </Pressable>

      {/* Location Selection Modal */}
      <Modal
        visible={showLocationModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowLocationModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: textColor }]}>Select Delivery Location</Text>
              <Pressable onPress={() => setShowLocationModal(false)}>
                <MaterialIcons name="close" size={24} color={secondaryTextColor} />
              </Pressable>
            </View>

            {/* Current Location Option */}
            <Pressable
              style={[styles.locationOption, { borderColor }]}
              onPress={() => {
                getCurrentLocation();
                setShowLocationModal(false);
              }}
            >
              <MaterialIcons name="my-location" size={20} color={primaryColor} />
              <Text style={[styles.optionText, { color: textColor }]}>Use current location</Text>
            </Pressable>

            {/* Saved Addresses */}
            {savedAddresses.length > 0 && (
              <>
                <Text style={[styles.sectionTitle, { color: textColor }]}>Saved Addresses</Text>
                {savedAddresses.map((address, index) => (
                  <Pressable
                    key={index}
                    style={[styles.savedAddressOption, { borderColor }]}
                    onPress={() => {
                      setCurrentLocation(address);
                      onLocationChange?.(address);
                      setShowLocationModal(false);
                    }}
                  >
                    <View style={styles.savedAddressContent}>
                      <View style={styles.savedAddressHeader}>
                        <MaterialIcons
                          name={address.type === 'home' ? 'home' : address.type === 'work' ? 'work' : 'location-on'}
                          size={18}
                          color={secondaryTextColor}
                        />
                        <Text style={[styles.savedAddressLabel, { color: textColor }]}>{address.label}</Text>
                        <Text style={[styles.savedAddressTime, { color: primaryColor }]}>{address.deliveryTime}</Text>
                      </View>
                      <Text style={[styles.savedAddressText, { color: secondaryTextColor }]} numberOfLines={1}>
                        {address.address}
                      </Text>
                    </View>
                  </Pressable>
                ))}
              </>
            )}

            {/* Manual Address Entry */}
            <View style={[styles.manualAddressContainer, { borderColor }]}>
              <MaterialIcons name="search" size={20} color={secondaryTextColor} />
              <TextInput
                style={[styles.addressInput, { color: textColor }]}
                placeholder="Enter complete address"
                placeholderTextColor={secondaryTextColor}
                value={manualAddress}
                onChangeText={setManualAddress}
                multiline
              />
            </View>

            <Pressable 
              style={[styles.confirmButton, { backgroundColor: primaryColor }]}
              onPress={handleManualAddress}
            >
              <Text style={styles.confirmButtonText}>Confirm Location</Text>
            </Pressable>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  locationContainer: {
    padding: 14,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 3,
  },
  locationContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  locationInfo: {
    flex: 1,
  },
  deliveryTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  deliveryTime: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  addressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addressTextContainer: {
    flex: 1,
    marginLeft: 6,
  },
  addressLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 1,
  },
  addressText: {
    fontSize: 13,
  },
  loadingIndicator: {
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  locationOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 16,
  },
  optionText: {
    fontSize: 16,
    marginLeft: 12,
  },
  manualAddressContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 20,
  },
  addressInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    minHeight: 40,
    textAlignVertical: 'top',
  },
  confirmButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 8,
    marginBottom: 12,
  },
  savedAddressOption: {
    padding: 12,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 8,
  },
  savedAddressContent: {
    flex: 1,
  },
  savedAddressHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  savedAddressLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  savedAddressTime: {
    fontSize: 12,
    fontWeight: '500',
  },
  savedAddressText: {
    fontSize: 13,
    marginLeft: 26,
  },
});
