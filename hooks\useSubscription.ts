import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/lib/supabase';
import { useEffect, useState } from 'react';

export interface UserSubscription {
  id: string;
  status: 'trial' | 'active' | 'cancelled' | 'expired';
  plan_id: string;
  plan_name: string;
  current_period_start: string;
  current_period_end: string;
  trial_end?: string;
  additional_users: number;
  auto_renew: boolean;
  payment_id?: string;
  order_id?: string;
  razorpay_signature?: string;
}

export interface TeamMember {
  id: string;
  user_id: string;
  email: string;
  full_name: string;
  role: 'owner' | 'admin' | 'member';
  status: 'active' | 'invited' | 'removed';
  joined_at?: string;
  invited_at?: string;
}

export interface SubscriptionPlan {
  name: string;
  price: number;
  maxUsers: number | null;
  additionalUserPrice: number;
}

export const planDetails: Record<string, SubscriptionPlan> = {
  'standard': { name: 'Standard', price: 249, maxUsers: 1, additionalUserPrice: 249 },
  'premium': { name: 'Premium', price: 999, maxUsers: 5, additionalUserPrice: 249 }
};

export interface UserLimits {
  includedUsers: number;
  currentActiveUsers: number;
  totalUsers: number;
  additionalUsers: number;
  additionalCost: number;
  canAddMore: boolean;
  remainingIncludedSlots: number;
}

export const useSubscription = () => {
  const { user } = useAuth();
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSubscription = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      
      // Fetch subscription with all fields including signature
      const { data: subData, error: subError } = await supabase
        .from('user_subscriptions')
        .select(`
          id,
          status,
          plan_id,
          plan_name,
          current_period_start,
          current_period_end,
          trial_end,
          additional_users,
          auto_renew,
          payment_id,
          order_id,
          razorpay_signature,
          created_at,
          updated_at
        `)
        .eq('user_id', user.id)
        .single();

      if (subError && subError.code !== 'PGRST116') {
        throw new Error('Failed to fetch subscription');
      }
      
      if (subData) {
        setSubscription(subData);
        
        // Fetch team members
        const { data: membersData, error: membersError } = await supabase
          .from('subscription_users')
          .select(`
            id,
            user_id,
            role,
            status,
            joined_at,
            invited_at
          `)
          .eq('subscription_id', subData.id);

        if (membersError) {
          console.error('Error fetching team members:', membersError);
          // Don't throw error for team members, continue with empty array
          setTeamMembers([]);
        } else if (membersData && membersData.length > 0) {
          // Get user IDs from members data
          const userIds = membersData.map(member => member.user_id);
          
          // Fetch profiles separately to avoid foreign key relationship issues
          const { data: profilesData } = await supabase
            .from('profiles')
            .select('user_id, email, full_name')
            .in('user_id', userIds);

          const formattedMembers = membersData.map((member: any) => {
            const profile = profilesData?.find(p => p.user_id === member.user_id);
            return {
              id: member.id,
              user_id: member.user_id,
              email: profile?.email || '',
              full_name: profile?.full_name || '',
              role: member.role,
              status: member.status,
              joined_at: member.joined_at,
              invited_at: member.invited_at
            };
          });
          setTeamMembers(formattedMembers);
        } else {
          setTeamMembers([]);
        }
      }
    } catch (err) {
      console.error('Error fetching subscription:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const updateSubscription = async (updates: Partial<UserSubscription>) => {
    if (!subscription) return false;
    
    try {
      const { error } = await supabase
        .from('user_subscriptions')
        .update(updates)
        .eq('id', subscription.id);

      if (error) {
        throw new Error('Failed to update subscription');
      }

      setSubscription({ ...subscription, ...updates });
      return true;
    } catch (err) {
      console.error('Error updating subscription:', err);
      setError(err instanceof Error ? err.message : 'Failed to update subscription');
      return false;
    }
  };

  const getActiveMemberCount = () => {
    return teamMembers.filter(m => m.status === 'active').length;
  };

  const getPlanDetails = () => {
    if (!subscription) return null;
    return planDetails[subscription.plan_id];
  };

  const getUserLimits = (): UserLimits | null => {
    if (!subscription) return null;

    const plan = getPlanDetails();
    if (!plan) return null;

    const currentActiveUsers = getActiveMemberCount();
    const totalUsers = currentActiveUsers + 1; // +1 for owner
    const includedUsers = plan.maxUsers || 5; // Default to 5 for premium
    const additionalUsers = Math.max(0, totalUsers - includedUsers);
    const additionalCost = additionalUsers * plan.additionalUserPrice;
    const remainingIncludedSlots = Math.max(0, includedUsers - totalUsers);

    return {
      includedUsers,
      currentActiveUsers,
      totalUsers,
      additionalUsers,
      additionalCost,
      canAddMore: subscription.plan_id === 'premium' || totalUsers < includedUsers,
      remainingIncludedSlots
    };
  };

  const canAddUser = (): boolean => {
    const limits = getUserLimits();
    return limits ? limits.canAddMore : false;
  };

  const requiresPaymentForNewUser = (): boolean => {
    const limits = getUserLimits();
    return limits ? limits.remainingIncludedSlots === 0 : false;
  };

  const getAdditionalUserCost = (usersToAdd: number = 1): number => {
    const limits = getUserLimits();
    if (!limits) return 0;

    const newTotalUsers = limits.totalUsers + usersToAdd;
    const newAdditionalUsers = Math.max(0, newTotalUsers - limits.includedUsers);
    const currentAdditionalUsers = limits.additionalUsers;
    const additionalUsersToPayFor = newAdditionalUsers - currentAdditionalUsers;

    return Math.max(0, additionalUsersToPayFor) * (getPlanDetails()?.additionalUserPrice || 249);
  };

  const isTrialActive = () => {
    return subscription?.status === 'trial';
  };

  const hasValidPayment = () => {
    return !!(subscription?.payment_id && subscription?.razorpay_signature);
  };

  const getPaymentInfo = () => {
    if (!subscription) return null;
    return {
      payment_id: subscription.payment_id,
      order_id: subscription.order_id,
      signature: subscription.razorpay_signature,
      verified: hasValidPayment()
    };
  };

  useEffect(() => {
    fetchSubscription();
  }, [user]);

  return {
    subscription,
    teamMembers,
    loading,
    error,
    fetchSubscription,
    updateSubscription,
    getActiveMemberCount,
    getPlanDetails,
    getUserLimits,
    canAddUser,
    requiresPaymentForNewUser,
    getAdditionalUserCost,
    isTrialActive,
    hasValidPayment,
    getPaymentInfo,
    planDetails
  };
};