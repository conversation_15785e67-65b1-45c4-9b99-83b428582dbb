import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Application from 'expo-application';
import Constants from 'expo-constants';
import { Alert, Linking, Platform } from 'react-native';

// Safely import expo-updates with fallback for development builds
let Updates: any = null;
try {
  Updates = require('expo-updates');
} catch (error) {
  console.log('expo-updates not available (development build)');
}

export interface UpdateInfo {
  version: string;
  versionCode: number;
  mandatory: boolean;
  releaseNotes?: string;
  downloadUrl?: string;
  updateType: 'immediate' | 'flexible';
}

export interface PlayStoreUpdateResult {
  updateAvailable: boolean;
  updateInfo?: UpdateInfo;
  immediateUpdateAllowed: boolean;
  flexibleUpdateAllowed: boolean;
}

class PlayStoreUpdateManager {
  private static instance: PlayStoreUpdateManager;
  private readonly STORAGE_KEY = 'play_store_update_info';
  private readonly CHECK_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours
  private readonly PACKAGE_NAME = 'com.infratasks.app';
  
  private constructor() {}

  static getInstance(): PlayStoreUpdateManager {
    if (!PlayStoreUpdateManager.instance) {
      PlayStoreUpdateManager.instance = new PlayStoreUpdateManager();
    }
    return PlayStoreUpdateManager.instance;
  }

  /**
   * Get current app version information
   */
  private getCurrentVersion() {
    return {
      version: Application.nativeApplicationVersion || '1.0.0',
      versionCode: Application.nativeBuildVersion ? parseInt(Application.nativeBuildVersion) : 1,
    };
  }

  /**
   * Check if we should check for updates (rate limiting)
   */
  private async shouldCheckForUpdates(): Promise<boolean> {
    try {
      const lastCheck = await AsyncStorage.getItem(`${this.STORAGE_KEY}_last_check`);
      if (!lastCheck) return true;
      
      const timeSinceLastCheck = Date.now() - parseInt(lastCheck);
      return timeSinceLastCheck > this.CHECK_INTERVAL;
    } catch {
      return true;
    }
  }

  /**
   * Save last check timestamp
   */
  private async saveLastCheckTime(): Promise<void> {
    try {
      await AsyncStorage.setItem(`${this.STORAGE_KEY}_last_check`, Date.now().toString());
    } catch (error) {
      console.error('Error saving last check time:', error);
    }
  }

  /**
   * Check Google Play Store for app updates using web scraping approach
   */
  private async checkPlayStoreVersion(): Promise<UpdateInfo | null> {
    try {
      // For production apps, you would typically use Google Play Developer API
      // For now, we'll simulate the check with a mock response
      const currentVersion = this.getCurrentVersion();
      
      // Mock Play Store response - replace with actual API call
      const mockPlayStoreResponse = {
        version: '1.0.1',
        versionCode: 3,
        mandatory: false,
        releaseNotes: 'Bug fixes and performance improvements',
        updateType: 'flexible' as const
      };

      // Check if update is available
      if (mockPlayStoreResponse.versionCode > currentVersion.versionCode) {
        return mockPlayStoreResponse;
      }

      return null;
    } catch (error) {
      console.error('Error checking Play Store version:', error);
      return null;
    }
  }

  /**
   * Check for Expo OTA updates
   */
  private async checkExpoUpdates(): Promise<boolean> {
    try {
      // Check if we're in development build or expo-updates is not available
      if (!Updates || __DEV__ || !Constants.executionEnvironment || Constants.executionEnvironment === 'storeClient') {
        console.log('Expo Updates not available in development build');
        return false;
      }

      if (!Updates.isEnabled) {
        console.log('Expo Updates is not enabled');
        return false;
      }

      const update = await Updates.checkForUpdateAsync();
      return update.isAvailable;
    } catch (error) {
      console.error('Error checking for Expo updates:', error);
      return false;
    }
  }

  /**
   * Download and apply Expo OTA update
   */
  private async downloadExpoUpdate(): Promise<boolean> {
    try {
      if (!Updates || __DEV__ || !Updates.isEnabled) return false;

      const update = await Updates.fetchUpdateAsync();
      if (update.isNew) {
        await Updates.reloadAsync();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error downloading Expo update:', error);
      return false;
    }
  }

  /**
   * Open Play Store for app update
   */
  private async openPlayStore(): Promise<void> {
    const playStoreUrl = `market://details?id=${this.PACKAGE_NAME}`;
    const fallbackUrl = `https://play.google.com/store/apps/details?id=${this.PACKAGE_NAME}`;

    try {
      const canOpen = await Linking.canOpenURL(playStoreUrl);
      if (canOpen) {
        await Linking.openURL(playStoreUrl);
      } else {
        await Linking.openURL(fallbackUrl);
      }
    } catch (error) {
      console.error('Error opening Play Store:', error);
      await Linking.openURL(fallbackUrl);
    }
  }

  /**
   * Show immediate update dialog
   */
  private showImmediateUpdateDialog(updateInfo: UpdateInfo): void {
    Alert.alert(
      'App Update Required',
      `A new version (${updateInfo.version}) is available. This update is required to continue using the app.\n\n${updateInfo.releaseNotes || ''}`,
      [
        {
          text: 'Update Now',
          onPress: () => this.openPlayStore(),
          style: 'default'
        }
      ],
      { cancelable: false }
    );
  }

  /**
   * Show flexible update dialog
   */
  private showFlexibleUpdateDialog(updateInfo: UpdateInfo): void {
    Alert.alert(
      'App Update Available',
      `A new version (${updateInfo.version}) is available with improvements and bug fixes.\n\n${updateInfo.releaseNotes || ''}`,
      [
        {
          text: 'Later',
          style: 'cancel',
          onPress: () => this.markUpdateDismissed(updateInfo.versionCode)
        },
        {
          text: 'Update',
          onPress: () => this.openPlayStore(),
          style: 'default'
        }
      ]
    );
  }

  /**
   * Mark update as dismissed
   */
  private async markUpdateDismissed(versionCode: number): Promise<void> {
    try {
      await AsyncStorage.setItem(`${this.STORAGE_KEY}_dismissed_${versionCode}`, 'true');
    } catch (error) {
      console.error('Error marking update as dismissed:', error);
    }
  }

  /**
   * Check if update was dismissed
   */
  private async isUpdateDismissed(versionCode: number): Promise<boolean> {
    try {
      const dismissed = await AsyncStorage.getItem(`${this.STORAGE_KEY}_dismissed_${versionCode}`);
      return dismissed === 'true';
    } catch {
      return false;
    }
  }

  /**
   * Main method to check for updates
   */
  async checkForUpdates(forceCheck: boolean = false): Promise<PlayStoreUpdateResult> {
    // Only check on Android
    if (Platform.OS !== 'android') {
      return {
        updateAvailable: false,
        immediateUpdateAllowed: false,
        flexibleUpdateAllowed: false
      };
    }

    // Rate limiting check
    if (!forceCheck && !(await this.shouldCheckForUpdates())) {
      return {
        updateAvailable: false,
        immediateUpdateAllowed: false,
        flexibleUpdateAllowed: false
      };
    }

    try {
      // Save check timestamp
      await this.saveLastCheckTime();

      // First check for Expo OTA updates
      const expoUpdateAvailable = await this.checkExpoUpdates();
      if (expoUpdateAvailable) {
        // Handle Expo update silently in background
        this.downloadExpoUpdate();
      }

      // Then check for Play Store updates
      const playStoreUpdate = await this.checkPlayStoreVersion();
      
      if (!playStoreUpdate) {
        return {
          updateAvailable: false,
          immediateUpdateAllowed: false,
          flexibleUpdateAllowed: false
        };
      }

      // Check if update was dismissed (only for non-mandatory updates)
      if (!playStoreUpdate.mandatory && await this.isUpdateDismissed(playStoreUpdate.versionCode)) {
        return {
          updateAvailable: false,
          immediateUpdateAllowed: false,
          flexibleUpdateAllowed: false
        };
      }

      return {
        updateAvailable: true,
        updateInfo: playStoreUpdate,
        immediateUpdateAllowed: playStoreUpdate.mandatory,
        flexibleUpdateAllowed: !playStoreUpdate.mandatory
      };

    } catch (error) {
      console.error('Error checking for updates:', error);
      return {
        updateAvailable: false,
        immediateUpdateAllowed: false,
        flexibleUpdateAllowed: false
      };
    }
  }

  /**
   * Start update flow
   */
  async startUpdateFlow(updateType: 'immediate' | 'flexible' = 'flexible'): Promise<void> {
    const result = await this.checkForUpdates(true);
    
    if (!result.updateAvailable || !result.updateInfo) {
      return;
    }

    if (updateType === 'immediate' || result.updateInfo.mandatory) {
      this.showImmediateUpdateDialog(result.updateInfo);
    } else {
      this.showFlexibleUpdateDialog(result.updateInfo);
    }
  }

  /**
   * Check and prompt for updates automatically
   */
  async checkAndPromptForUpdates(): Promise<void> {
    const result = await this.checkForUpdates();
    
    if (result.updateAvailable && result.updateInfo) {
      if (result.immediateUpdateAllowed) {
        this.showImmediateUpdateDialog(result.updateInfo);
      } else if (result.flexibleUpdateAllowed) {
        this.showFlexibleUpdateDialog(result.updateInfo);
      }
    }
  }
}

// Export singleton instance
export const playStoreUpdateManager = PlayStoreUpdateManager.getInstance();
