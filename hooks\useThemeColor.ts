/**
 * Learn more about light and dark modes:
 * https://docs.expo.dev/guides/color-schemes/
 */

import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

// Fallback colors in case the theme system fails
const fallbackColors = {
  text: '#000000',
  background: '#ffffff',
  tint: '#f97316',
  tabIconDefault: '#cccccc',
  tabIconSelected: '#f97316',
  // Add any other colors that might be in the Colors type
  icon: '#000000',
  primary: '#f97316'
};

export function useThemeColor(
  props: { light?: string; dark?: string },
  colorName: keyof typeof Colors.light & keyof typeof Colors.dark
) {
  const theme = useColorScheme() ?? 'light';
  const colorFromProps = props[theme];

  try {
    if (colorFromProps) {
      return colorFromProps;
    } else if (Colors && Colors[theme] && Colors[theme][colorName]) {
      return Colors[theme][colorName];
    }
    // If we reach here, there was an error getting the color from the theme
    return fallbackColors[colorName] || '#000000'; // Ultimate fallback is black
  } catch (error) {
    // Return fallback color on any error
    return fallbackColors[colorName] || '#000000'; // Ultimate fallback is black
  }
}
