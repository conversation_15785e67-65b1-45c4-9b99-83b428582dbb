-- Migration: Add site_id column to transactions table
-- This fixes the error: column transactions.site_id does not exist
-- Date: 2024-12-20

-- Add site_id column to transactions table
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS site_id UUID;

-- Add foreign key constraint to reference sites table
DO $$
BEGIN
    -- Add foreign key constraint
    ALTER TABLE transactions ADD CONSTRAINT fk_transactions_site_id 
        FOREIGN KEY (site_id) REFERENCES sites(id) ON DELETE CASCADE;
EXCEPTION
    WHEN duplicate_object THEN
        -- Constraint already exists, do nothing
        NULL;
END $$;

-- Create index for better performance on site_id queries
CREATE INDEX IF NOT EXISTS idx_transactions_site_id ON transactions(site_id);

-- Create index for better performance on compound queries (site_id + user_id)
CREATE INDEX IF NOT EXISTS idx_transactions_site_user ON transactions(site_id, user_id);

-- Add comment to document the column purpose
COMMENT ON COLUMN transactions.site_id IS 'Optional reference to site - NULL for global/personal transactions';

-- Grant necessary permissions (if needed)
-- Note: Adjust these based on your RLS policies and user roles
-- GRA<PERSON> SELECT, INSERT, UPDATE ON transactions TO authenticated; 