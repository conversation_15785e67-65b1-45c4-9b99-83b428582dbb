-- Create app_versions table for in-app update functionality
CREATE TABLE IF NOT EXISTS app_versions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    version VARCHAR(20) NOT NULL,
    version_code INTEGER NOT NULL,
    platform VARCHAR(10) NOT NULL CHECK (platform IN ('android', 'ios')),
    mandatory BOOLEAN DEFAULT FALSE,
    release_notes TEXT,
    download_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create unique index to prevent duplicate version codes for the same platform
CREATE UNIQUE INDEX IF NOT EXISTS idx_app_versions_platform_version_code 
ON app_versions(platform, version_code);

-- Create index for active versions
CREATE INDEX IF NOT EXISTS idx_app_versions_active 
ON app_versions(platform, is_active, version_code DESC);

-- Insert initial version (current version)
INSERT INTO app_versions (version, version_code, platform, mandatory, release_notes, is_active)
VALUES 
  ('1.0.0', 1, 'android', false, 'Initial release of Infratask - Construction Management App', true);

-- Add RLS policies for app_versions table
ALTER TABLE app_versions ENABLE ROW LEVEL SECURITY;

-- Policy to allow everyone to read active app versions
CREATE POLICY "Allow read access to active app versions" ON app_versions
    FOR SELECT USING (is_active = true);

-- Policy to allow authenticated users to read all versions (for admin purposes)
CREATE POLICY "Allow authenticated users to read all app versions" ON app_versions
    FOR SELECT USING (auth.role() = 'authenticated');

-- Policy to allow only service role to insert/update app versions (for admin/deployment scripts)
CREATE POLICY "Allow service role to manage app versions" ON app_versions
    FOR ALL USING (auth.role() = 'service_role'); 