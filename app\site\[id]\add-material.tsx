import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { supabase } from '@/lib/supabase';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  useColorScheme,
  View
} from 'react-native';

// Define UOM options
const UOM_OPTIONS = [
  'No.s',
  'Box',
  'Each',
  'Meter',
  'Feet',
  'Running Feet',
  'Square Meter',
  'Square Feet',
  'Tones',
  'Liter',
  'Cubic Meter',
  'Cubic Feet',
  'Kilogram',
  'Bags',
  'Other'
];

// Helper function to get image data
const getImageData = async (uri: string): Promise<{ base64: string, fileType: string }> => {
  try {
    // Get file info
    const fileInfo = await FileSystem.getInfoAsync(uri);
    if (!fileInfo.exists) {
      throw new Error(`File doesn't exist at path: ${uri}`);
    }
    
    // Get file extension
    const fileExt = uri.split('.').pop()?.toLowerCase() || 'jpg';
    const fileType = fileExt === 'jpg' ? 'jpeg' : fileExt;
    
    // Read the file as base64
    const base64 = await FileSystem.readAsStringAsync(uri, {
      encoding: FileSystem.EncodingType.Base64,
    });
    
    return { base64, fileType };
  } catch (error) {
    console.error('Error getting image data:', error);
    throw error;
  }
};

// Helper function to upload image to Supabase
const uploadImageToSupabase = async (uri: string, userId: string): Promise<string | null> => {
  try {
    console.log('Starting image upload with URI:', uri);
    
    // Get image data
    const { base64, fileType } = await getImageData(uri);
    console.log(`Got image data, fileType: ${fileType}, base64 length: ${base64.length}`);
    
    // Generate unique file name
    const fileName = `${userId}-${Date.now()}.${fileType}`;
    const filePath = `materials/${fileName}`;
    
    // Create bucket if it doesn't exist (requires admin privileges)
    try {
      const { data } = await supabase.storage.listBuckets();
      const buckets = data || [];
      const bucketExists = buckets.some(bucket => bucket.name === 'material-images');
      
      if (!bucketExists) {
        console.log('Bucket does not exist, creating it');
        await supabase.storage.createBucket('material-images', {
          public: true,
          fileSizeLimit: 5242880, // 5MB
          allowedMimeTypes: ['image/png', 'image/jpeg', 'image/jpg']
        });
        console.log('Bucket created successfully');
      }
    } catch (bucketError) {
      console.log('Error checking/creating bucket:', bucketError);
      // Continue anyway, the bucket might already exist
    }
    
    // Make sure we have the latest authentication
    const { data: authData } = await supabase.auth.getSession();
    if (!authData.session) {
      throw new Error('No authentication session found');
    }
    
    // Try signed URL approach first
    try {
      // Get a signed URL for upload (bypasses RLS)
      console.log('Getting signed upload URL');
      const { data: signedUrlData, error: signedUrlError } = await supabase.storage
        .from('material-images')
        .createSignedUploadUrl(filePath);
        
      if (signedUrlError || !signedUrlData) {
        throw new Error(`Failed to get signed URL: ${signedUrlError?.message || 'Unknown error'}`);
      }
      
      console.log('Got signed URL:', signedUrlData.signedUrl);
      
      // Upload directly to the signed URL
      console.log(`Uploading to signed URL with content type: image/${fileType}`);
      
      try {
        const uploadResult = await FileSystem.uploadAsync(signedUrlData.signedUrl, uri, {
          httpMethod: 'PUT',
          headers: {
            'Content-Type': `image/${fileType}`
          }
        });
        
        console.log(`Upload status: ${uploadResult.status}, response: ${uploadResult.body}`);
        
        if (uploadResult.status < 200 || uploadResult.status >= 300) {
          throw new Error(`Upload failed with status ${uploadResult.status}: ${uploadResult.body}`);
        }
        
        console.log('Upload successful using signed URL');
        return filePath;
      } catch (uploadError) {
        console.error('Error during upload to signed URL:', uploadError);
        throw new Error('Failed to upload to signed URL');
      }
    } catch (signedUrlError) {
      console.error('Signed URL upload failed:', signedUrlError);
      console.log('Falling back to direct upload...');
    }
    
    // Let's try a much simpler upload approach as fallback
    console.log('Using direct upload as fallback');
    
    try {
      // Get image file from URI as base64
      const { data: fileData, error: fileError } = await supabase.storage
        .from('material-images')
        .upload(filePath, base64, {
          contentType: `image/${fileType}`,
          upsert: true
        });
        
      if (fileError) {
        console.error('Direct upload failed:', fileError);
        throw fileError;
      }
      
      console.log('Direct upload successful:', fileData);
      return filePath;
    } catch (directUploadError) {
      console.error('All upload methods failed:', directUploadError);
      return null;
    }
  } catch (error) {
    console.error('Error in uploadImageToSupabase:', error);
    return null;
  }
};

// Helper function to get public URL for an image
const getImagePublicUrl = (path: string): string => {
  const { data } = supabase.storage
    .from('material-images')
    .getPublicUrl(path);
  return data.publicUrl;
};

export default function AddMaterialScreen() {
  const { id } = useLocalSearchParams();
  const colorScheme = useColorScheme();
  const [loading, setLoading] = useState(false);
  const [checkingPermission, setCheckingPermission] = useState(true);
  const [showDatePicker, setShowDatePicker] = useState(false);
  
  // Form state
  const [materialName, setMaterialName] = useState('');
  const [specifications, setSpecifications] = useState('');
  const [stockLevel, setStockLevel] = useState('');
  const [minimumStock, setMinimumStock] = useState('');
  const [unitOfMeasure, setUnitOfMeasure] = useState(UOM_OPTIONS[0]);
  const [price, setPrice] = useState('');
  const [category, setCategory] = useState('');
  const [receivedDate, setReceivedDate] = useState(new Date());
  const [invoiceNumber, setInvoiceNumber] = useState('');
  const [invoiceImage, setInvoiceImage] = useState<string | null>(null);
  const [imageLoading, setImageLoading] = useState(false);
  const [imageError, setImageError] = useState(false);
  
  // Check if user has permission to add materials
  useEffect(() => {
    const checkPermission = async () => {
      try {
        const { data: userData } = await supabase.auth.getUser();
        if (!userData.user) {
          router.replace('/');
          return;
        }
        
        const { data: memberData, error } = await supabase
          .from('site_members')
          .select('role')
          .eq('site_id', id)
          .eq('user_id', userData.user.id)
          .single();
        
        if (error || !memberData) {
          console.error('Error checking permissions:', error);
          Alert.alert('Access Denied', 'You do not have permission to access this site.');
          router.back();
          return;
        }
        
        if (memberData.role !== 'Admin' && memberData.role !== 'Super Admin') {
          Alert.alert('Permission Denied', 'You need Admin or Super Admin privileges to add materials.');
          router.back();
          return;
        }
        
        setCheckingPermission(false);
      } catch (error) {
        console.error('Error in checkPermission:', error);
        Alert.alert('Error', 'An error occurred while checking permissions.');
        router.back();
      }
    };
    
    checkPermission();
  }, [id]);
  
  // Handle date change
  const onDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setReceivedDate(selectedDate);
    }
  };
  
  // Show date picker
  const showDatePickerModal = () => {
    setShowDatePicker(true);
  };
  
  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString();
  };

  // Validate image before selecting it
  const validateImage = (asset: any): boolean => {
    // Check if the image is too large (> 5MB)
    const MAX_SIZE = 5 * 1024 * 1024; // 5MB in bytes
    if (asset.fileSize && asset.fileSize > MAX_SIZE) {
      Alert.alert(
        'Image Too Large',
        'The selected image is too large (over 5MB). Please select a smaller image.',
        [{ text: 'OK' }]
      );
      return false;
    }
    
    return true;
  };

  // Handle invoice image selection
  const selectInvoiceImage = async () => {
    try {
      // Request permissions
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please allow access to your photo library to upload images.');
        return;
      }

      // Launch image picker with better options
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 0.7, // Reduced quality to prevent large files
        aspect: [4, 3],
        exif: false, // Don't need EXIF data
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        console.log('Selected image details:', {
          uri: asset.uri,
          fileSize: asset.fileSize,
          type: asset.type,
          width: asset.width,
          height: asset.height
        });
        
        // Validate the image before setting it
        if (validateImage(asset)) {
          // Make sure we have a valid file URI that works on both iOS and Android
          setInvoiceImage(asset.uri);
        }
      }
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image. Please try again.');
    }
  };

  // Handle camera capture
  const captureInvoiceImage = async () => {
    try {
      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please allow access to your camera to capture images.');
        return;
      }

      // Launch camera with better options
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 0.7, // Reduced quality to prevent large files
        aspect: [4, 3],
        exif: false // Don't need EXIF data
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        console.log('Captured image details:', {
          uri: asset.uri,
          fileSize: asset.fileSize,
          type: asset.type,
          width: asset.width,
          height: asset.height
        });
        
        // Validate the image before setting it
        if (validateImage(asset)) {
          setInvoiceImage(asset.uri);
        }
      }
    } catch (error) {
      console.error('Error capturing image:', error);
      Alert.alert('Error', 'Failed to capture image. Please try again.');
    }
  };

  // Show image picker options
  const showImagePickerOptions = () => {
    Alert.alert(
      'Upload Invoice Image',
      'Choose an option',
      [
        { text: 'Camera', onPress: captureInvoiceImage },
        { text: 'Gallery', onPress: selectInvoiceImage },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };
  
  // Image load handlers
  const handleImageLoadStart = () => {
    setImageLoading(true);
    setImageError(false);
  };

  const handleImageLoadSuccess = () => {
    setImageLoading(false);
    setImageError(false);
  };

  const handleImageLoadError = () => {
    setImageLoading(false);
    setImageError(true);
  };
  
  // Handle form submission
  const handleSubmit = async () => {
    // Validate form
    if (!materialName.trim()) {
      Alert.alert('Error', 'Material name is required.');
      return;
    }
    
    if (!unitOfMeasure.trim()) {
      Alert.alert('Error', 'Unit of measure is required.');
      return;
    }
    
    // Convert numeric fields
    const stockLevelNum = parseFloat(stockLevel) || 0;
    const minimumStockNum = parseFloat(minimumStock) || 0;
    const priceNum = parseFloat(price) || 0;
    
    if (stockLevelNum < 0) {
      Alert.alert('Error', 'Stock level cannot be negative.');
      return;
    }
    
    if (minimumStockNum < 0) {
      Alert.alert('Error', 'Minimum stock level cannot be negative.');
      return;
    }
    
    if (priceNum < 0) {
      Alert.alert('Error', 'Price cannot be negative.');
      return;
    }
    
    setLoading(true);
    
    try {
      // Get current user
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) {
        Alert.alert('Error', 'User not authenticated.');
        setLoading(false);
        return;
      }
      
      // Format date for database
      const formattedDate = receivedDate.toISOString().split('T')[0];
      
      // Upload invoice image if provided
      let invoiceImageUrl = null;
      if (invoiceImage) {
        try {
          invoiceImageUrl = await uploadImageToSupabase(invoiceImage, userData.user.id);
          
          if (!invoiceImageUrl) {
            Alert.alert(
              'Image Upload Issue',
              'Could not upload the image. Do you want to continue without the image?',
              [
                { text: 'Continue', onPress: () => console.log('Continuing without image') },
                { text: 'Cancel', style: 'cancel', onPress: () => { setLoading(false); return; } }
              ]
            );
          } else {
            console.log('Image uploaded successfully, path:', invoiceImageUrl);
          }
        } catch (error) {
          console.error('Error processing image:', error);
          Alert.alert(
            'Image Upload Issue',
            'Could not process the image. Do you want to continue without the image?',
            [
              { text: 'Continue', onPress: () => console.log('Continuing without image') },
              { text: 'Cancel', style: 'cancel', onPress: () => { setLoading(false); return; } }
            ]
          );
        }
      }
      
      // Insert new material
      const { data, error } = await supabase
        .from('materials')
        .insert({
          site_id: id,
          name: materialName.trim(),
          specifications: specifications.trim() || null,
          stock_level: stockLevelNum,
          minimum_stock_level: minimumStockNum,
          unit_of_measure: unitOfMeasure.trim(),
          price: priceNum,
          category: category.trim() || null,
          received_date: formattedDate,
          created_by: userData.user.id,
          invoice_number: invoiceNumber.trim() || null,
          invoice_image_url: invoiceImageUrl
        })
        .select();
      
      if (error) {
        console.error('Error adding material:', error);
        Alert.alert('Error', 'Failed to add material.');
        setLoading(false);
        return;
      }
      
      Alert.alert(
        'Success',
        'Material added successfully!',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    } catch (error) {
      console.error('Error in handleSubmit:', error);
      Alert.alert('Error', 'Something went wrong while adding the material.');
    } finally {
      setLoading(false);
    }
  };
  
  if (checkingPermission) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#f97316" />
      </ThemedView>
    );
  }
  
  return (
    <ThemedView style={styles.container}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      
      <Stack.Screen
        options={{
          title: 'Add New Material',
          headerBackTitle: 'Cancel',
        }}
      />
      
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollViewContent}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.formSection}>
            <ThemedText style={styles.sectionTitle}>Material Information</ThemedText>
            
            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Name *</ThemedText>
              <TextInput
                style={styles.input}
                value={materialName}
                onChangeText={setMaterialName}
                placeholder="Material name"
                placeholderTextColor="#94a3b8"
              />
            </View>
            
            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Specifications</ThemedText>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={specifications}
                onChangeText={setSpecifications}
                placeholder="Size, description, or other details"
                placeholderTextColor="#94a3b8"
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>
            
            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Category</ThemedText>
              <TextInput
                style={styles.input}
                value={category}
                onChangeText={setCategory}
                placeholder="Category (optional)"
                placeholderTextColor="#94a3b8"
              />
            </View>
          </View>
          
          <View style={styles.formSection}>
            <ThemedText style={styles.sectionTitle}>Inventory Details</ThemedText>
            
            <View style={styles.rowContainer}>
              <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
                <ThemedText style={styles.inputLabel}>Stock Level *</ThemedText>
                <TextInput
                  style={styles.input}
                  value={stockLevel}
                  onChangeText={setStockLevel}
                  placeholder="0"
                  placeholderTextColor="#94a3b8"
                  keyboardType="numeric"
                />
              </View>
              
              <View style={[styles.inputContainer, { flex: 1, marginLeft: 8 }]}>
                <ThemedText style={styles.inputLabel}>Minimum Stock</ThemedText>
                <TextInput
                  style={styles.input}
                  value={minimumStock}
                  onChangeText={setMinimumStock}
                  placeholder="0"
                  placeholderTextColor="#94a3b8"
                  keyboardType="numeric"
                />
              </View>
            </View>
            
            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Unit of Measure *</ThemedText>
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={unitOfMeasure}
                  onValueChange={(itemValue) => setUnitOfMeasure(itemValue)}
                  style={styles.picker}
                >
                  {UOM_OPTIONS.map((option) => (
                    <Picker.Item key={option} label={option} value={option} />
                  ))}
                </Picker>
              </View>
            </View>
            
            <View style={styles.rowContainer}>
              <View style={[styles.inputContainer, { flex: 1, marginRight: 8 }]}>
                <ThemedText style={styles.inputLabel}>Price (optional)</ThemedText>
                <View style={styles.priceInputContainer}>
                  <ThemedText style={styles.currencySymbol}>₹</ThemedText>
                  <TextInput
                    style={styles.priceInput}
                    value={price}
                    onChangeText={setPrice}
                    placeholder="0.00"
                    placeholderTextColor="#94a3b8"
                    keyboardType="numeric"
                  />
                </View>
              </View>
              
              <View style={[styles.inputContainer, { flex: 1, marginLeft: 8 }]}>
                <ThemedText style={styles.inputLabel}>Received Date</ThemedText>
                <TouchableOpacity 
                  style={styles.datePickerButton}
                  onPress={showDatePickerModal}
                >
                  <ThemedText style={styles.datePickerText}>
                    {formatDate(receivedDate)}
                  </ThemedText>
                  <MaterialIcons name="calendar-today" size={18} color="#64748b" />
                </TouchableOpacity>
                
                {showDatePicker && (
                  <DateTimePicker
                    value={receivedDate}
                    mode="date"
                    display="default"
                    onChange={onDateChange}
                  />
                )}
              </View>
            </View>

            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Invoice Number (optional)</ThemedText>
              <TextInput
                style={styles.input}
                value={invoiceNumber}
                onChangeText={setInvoiceNumber}
                placeholder="Enter invoice number"
                placeholderTextColor="#94a3b8"
              />
            </View>

            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Invoice or Received Material Image (optional)</ThemedText>
              <TouchableOpacity
                style={styles.imagePickerButton}
                onPress={showImagePickerOptions}
              >
                {invoiceImage ? (
                  <View style={styles.imagePreviewContainer}>
                    <Image 
                      source={{ uri: invoiceImage }} 
                      style={styles.imagePreview} 
                      onLoadStart={handleImageLoadStart}
                      onLoad={handleImageLoadSuccess}
                      onError={handleImageLoadError}
                    />
                    {imageLoading && (
                      <View style={styles.imageLoadingOverlay}>
                        <ActivityIndicator size="large" color="#f97316" />
                      </View>
                    )}
                    {imageError && (
                      <View style={styles.imageErrorOverlay}>
                        <MaterialIcons name="broken-image" size={40} color="#ef4444" />
                        <ThemedText style={styles.imageErrorText}>Failed to load image</ThemedText>
                      </View>
                    )}
                    <TouchableOpacity
                      style={styles.changeImageButton}
                      onPress={showImagePickerOptions}
                    >
                      <ThemedText style={styles.changeImageText}>Change</ThemedText>
                    </TouchableOpacity>
                  </View>
                ) : (
                  <View style={styles.imagePickerContent}>
                    <MaterialIcons name="add-photo-alternate" size={24} color="#64748b" />
                    <ThemedText style={styles.imagePickerText}>Upload Invoice Image</ThemedText>
                  </View>
                )}
              </TouchableOpacity>
              <ThemedText style={styles.imageHelperText}>
                For best results, use an image under 5MB. JPG or PNG formats recommended.
              </ThemedText>
            </View>
          </View>
          
          <TouchableOpacity
            style={[styles.submitButton, loading && styles.submitButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <MaterialIcons name="add" size={18} color="#fff" />
                <ThemedText style={styles.submitButtonText}>Add Material</ThemedText>
              </>
            )}
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollViewContent: {
    padding: 16,
  },
  formSection: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#0f172a',
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 4,
    color: '#64748b',
  },
  input: {
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#0f172a',
  },
  textArea: {
    minHeight: 80,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    padding: 12,
  },
  datePickerText: {
    fontSize: 16,
    color: '#0f172a',
  },
  pickerContainer: {
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    overflow: 'hidden',
  },
  picker: {
    height: 50,
    width: '100%',
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    padding: 12,
  },
  currencySymbol: {
    fontSize: 16,
    color: '#64748b',
    marginRight: 6,
  },
  priceInput: {
    flex: 1,
    fontSize: 16,
    color: '#0f172a',
    padding: 0,
  },
  imagePickerButton: {
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    overflow: 'hidden',
    height: 120,
  },
  imagePickerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imagePickerText: {
    marginTop: 8,
    fontSize: 14,
    color: '#64748b',
  },
  imagePreviewContainer: {
    flex: 1,
    position: 'relative',
  },
  imagePreview: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  imageLoadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  imageErrorOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  imageErrorText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 8,
  },
  changeImageButton: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: 'rgba(0,0,0,0.6)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  changeImageText: {
    color: '#fff',
    fontSize: 12,
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f97316',
    borderRadius: 8,
    padding: 16,
    marginVertical: 16,
  },
  submitButtonDisabled: {
    backgroundColor: '#fdba74',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  imageHelperText: {
    fontSize: 12,
    color: '#64748b',
    marginTop: 8,
  },
}); 