import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Pressable,
  StyleSheet,
  Alert,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useColorScheme } from '@/hooks/useColorScheme';
import { nativeInAppUpdateManager } from '@/lib/nativeInAppUpdate';
import * as Application from 'expo-application';

export function InAppUpdateDebugger() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const [isInitialized, setIsInitialized] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [updateInfo, setUpdateInfo] = useState<any>(null);
  const [logs, setLogs] = useState<string[]>([]);

  const colors = {
    background: isDark ? '#1a1a1a' : '#ffffff',
    text: isDark ? '#ffffff' : '#000000',
    textSecondary: isDark ? '#a1a1a1' : '#6b7280',
    border: isDark ? '#333333' : '#e5e7eb',
    primary: '#f97316',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
  };

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]);
  };

  useEffect(() => {
    initializeUpdateManager();
  }, []);

  const initializeUpdateManager = async () => {
    try {
      addLog('Initializing native update manager...');
      const initialized = await nativeInAppUpdateManager.initialize();
      setIsInitialized(initialized);
      
      if (initialized) {
        addLog('✅ Native update manager initialized successfully');
      } else {
        addLog('❌ Failed to initialize native update manager');
      }
    } catch (error) {
      addLog(`❌ Error initializing: ${error}`);
    }
  };

  const checkForUpdates = async () => {
    if (!isInitialized) {
      Alert.alert('Error', 'Update manager not initialized');
      return;
    }

    setIsChecking(true);
    try {
      addLog('Checking for native updates...');
      const updateResult = await nativeInAppUpdateManager.checkForUpdate();
      setUpdateInfo(updateResult);
      
      addLog(`Update check result: ${JSON.stringify(updateResult, null, 2)}`);
      
      if (updateResult.updateAvailable) {
        addLog(`✅ Update found: available version ${updateResult.availableVersionCode}`);
        Alert.alert(
          'Update Available',
          `Current: ${updateResult.currentVersionCode || 'Unknown'}\nAvailable: ${updateResult.availableVersionCode}\nImmediate: ${updateResult.immediateUpdateAllowed}\nFlexible: ${updateResult.flexibleUpdateAllowed}`,
          [
            { text: 'OK' }
          ]
        );
      } else {
        addLog('ℹ️ No updates available');
        Alert.alert('No Updates', 'Your app is up to date!');
      }
    } catch (error) {
      addLog(`❌ Error checking updates: ${error}`);
      Alert.alert('Error', `Failed to check for updates: ${error}`);
    } finally {
      setIsChecking(false);
    }
  };

  const startImmediateUpdate = async () => {
    if (!updateInfo?.updateAvailable || !updateInfo?.immediateUpdateAllowed) {
      Alert.alert('Error', 'Immediate update not available');
      return;
    }

    try {
      addLog('Starting immediate update...');
      const success = await nativeInAppUpdateManager.startImmediateUpdate();
      
      if (success) {
        addLog('✅ Immediate update started successfully');
      } else {
        addLog('❌ Failed to start immediate update');
      }
    } catch (error) {
      addLog(`❌ Error starting immediate update: ${error}`);
      Alert.alert('Error', `Failed to start immediate update: ${error}`);
    }
  };

  const startFlexibleUpdate = async () => {
    if (!updateInfo?.updateAvailable || !updateInfo?.flexibleUpdateAllowed) {
      Alert.alert('Error', 'Flexible update not available');
      return;
    }

    try {
      addLog('Starting flexible update...');
      const success = await nativeInAppUpdateManager.startFlexibleUpdate();
      
      if (success) {
        addLog('✅ Flexible update started successfully');
      } else {
        addLog('❌ Failed to start flexible update');
      }
    } catch (error) {
      addLog(`❌ Error starting flexible update: ${error}`);
      Alert.alert('Error', `Failed to start flexible update: ${error}`);
    }
  };

  const getVersionInfo = () => {
    const version = Application.nativeApplicationVersion;
    const buildNumber = Application.nativeBuildVersion;
    
    addLog(`App version: ${version}, Build: ${buildNumber}`);
    Alert.alert(
      'Version Info',
      `Version: ${version}\nBuild: ${buildNumber}`
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Text style={[styles.title, { color: colors.text }]}>
        In-App Update Debugger
      </Text>

      {/* Status */}
      <View style={[styles.statusContainer, { borderColor: colors.border }]}>
        <View style={styles.statusRow}>
          <Ionicons 
            name={isInitialized ? "checkmark-circle" : "close-circle"} 
            size={20} 
            color={isInitialized ? colors.success : colors.error} 
          />
          <Text style={[styles.statusText, { color: colors.text }]}>
            Native Manager: {isInitialized ? 'Initialized' : 'Not Initialized'}
          </Text>
        </View>
        
        {updateInfo && (
          <View style={styles.updateInfoContainer}>
            <Text style={[styles.updateInfoText, { color: colors.textSecondary }]}>
              Current Version: {updateInfo.currentVersionCode || 'Unknown'}
            </Text>
            {updateInfo.updateAvailable && (
              <>
                <Text style={[styles.updateInfoText, { color: colors.textSecondary }]}>
                  Available Version: {updateInfo.availableVersionCode}
                </Text>
                <Text style={[styles.updateInfoText, { color: colors.textSecondary }]}>
                  Immediate Update: {updateInfo.immediateUpdateAllowed ? 'Yes' : 'No'}
                </Text>
                <Text style={[styles.updateInfoText, { color: colors.textSecondary }]}>
                  Flexible Update: {updateInfo.flexibleUpdateAllowed ? 'Yes' : 'No'}
                </Text>
              </>
            )}
          </View>
        )}
      </View>

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <Pressable
          style={[styles.button, { backgroundColor: colors.primary }]}
          onPress={checkForUpdates}
          disabled={!isInitialized || isChecking}
        >
          {isChecking ? (
            <ActivityIndicator color="#ffffff" size="small" />
          ) : (
            <Ionicons name="refresh" size={20} color="#ffffff" />
          )}
          <Text style={styles.buttonText}>Check for Updates</Text>
        </Pressable>

        <Pressable
          style={[styles.button, { backgroundColor: colors.warning }]}
          onPress={startImmediateUpdate}
          disabled={!isInitialized || !updateInfo?.updateAvailable || !updateInfo?.immediateUpdateAllowed}
        >
          <Ionicons name="download" size={20} color="#ffffff" />
          <Text style={styles.buttonText}>Start Immediate Update</Text>
        </Pressable>

        <Pressable
          style={[styles.button, { backgroundColor: colors.success }]}
          onPress={startFlexibleUpdate}
          disabled={!isInitialized || !updateInfo?.updateAvailable || !updateInfo?.flexibleUpdateAllowed}
        >
          <Ionicons name="download-outline" size={20} color="#ffffff" />
          <Text style={styles.buttonText}>Start Flexible Update</Text>
        </Pressable>

        <Pressable
          style={[styles.button, { backgroundColor: colors.textSecondary }]}
          onPress={getVersionInfo}
        >
          <Ionicons name="information-circle" size={20} color="#ffffff" />
          <Text style={styles.buttonText}>Version Info</Text>
        </Pressable>
      </View>

      {/* Logs */}
      <View style={[styles.logsContainer, { borderColor: colors.border }]}>
        <Text style={[styles.logsTitle, { color: colors.text }]}>Debug Logs</Text>
        <ScrollView style={styles.logsScroll} showsVerticalScrollIndicator={false}>
          {logs.map((log, index) => (
            <Text key={index} style={[styles.logText, { color: colors.textSecondary }]}>
              {log}
            </Text>
          ))}
          {logs.length === 0 && (
            <Text style={[styles.logText, { color: colors.textSecondary, fontStyle: 'italic' }]}>
              No logs yet...
            </Text>
          )}
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 20,
  },
  statusContainer: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '500',
  },
  updateInfoContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  updateInfoText: {
    fontSize: 14,
    marginBottom: 4,
  },
  buttonContainer: {
    gap: 12,
    marginBottom: 20,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  logsContainer: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
  },
  logsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  logsScroll: {
    flex: 1,
  },
  logText: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 4,
    lineHeight: 16,
  },
});
