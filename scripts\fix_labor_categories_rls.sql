-- Fix labor_categories RLS policies
-- IMPORTANT: This script is for ADDING site_id to the labor_categories table.
-- Only run this if you want site-specific categories.
-- If you don't need site-specific categories, use the simpler RLS policies from the README.

-- First, check if site_id column exists, and add it if not
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'labor_categories' AND column_name = 'site_id'
  ) THEN
    -- Add site_id column
    ALTER TABLE labor_categories ADD COLUMN site_id UUID REFERENCES sites(id) ON DELETE CASCADE;
    
    -- Add index for better performance
    CREATE INDEX IF NOT EXISTS idx_labor_categories_site_id ON labor_categories(site_id);
    
    RAISE NOTICE 'Added site_id column to labor_categories table';
  ELSE
    RAISE NOTICE 'site_id column already exists in labor_categories table';
  END IF;
END $$;

-- Enable RLS on the labor_categories table if not already enabled
ALTER TABLE labor_categories ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if any
DROP POLICY IF EXISTS "Labor categories are viewable by site members" ON labor_categories;
DROP POLICY IF EXISTS "Labor categories can be inserted by site admins" ON labor_categories;
DROP POLICY IF EXISTS "Labor categories can be updated by site admins" ON labor_categories;
DROP POLICY IF EXISTS "Labor categories can be deleted by site admins" ON labor_categories;
DROP POLICY IF EXISTS "Labor categories are viewable by authenticated users" ON labor_categories;
DROP POLICY IF EXISTS "Labor categories can be inserted by admins" ON labor_categories;
DROP POLICY IF EXISTS "Labor categories can be updated by admins" ON labor_categories;
DROP POLICY IF EXISTS "Labor categories can be deleted by admins" ON labor_categories;

-- IMPORTANT: Choose ONE of the following sets of policies:
-- Uncomment either the site-specific policies OR the global policies below:

-- OPTION 1: Site-specific policies (requires site_id column)
/*
-- Create policies for viewing labor categories
-- Allow site members to view labor categories for sites they are a member of
CREATE POLICY "Labor categories are viewable by site members" 
ON labor_categories
FOR SELECT 
USING (
  site_id IN (
    SELECT site_id FROM site_members
    WHERE user_id = auth.uid()
  )
);

-- Create policy for inserting labor categories
-- Only Super Admin and Admin roles can insert
CREATE POLICY "Labor categories can be inserted by site admins" 
ON labor_categories
FOR INSERT 
WITH CHECK (
  site_id IN (
    SELECT site_id FROM site_members
    WHERE user_id = auth.uid() AND role IN ('Super Admin', 'Admin')
  )
);

-- Create policy for updating labor categories
-- Only Super Admin and Admin roles can update
CREATE POLICY "Labor categories can be updated by site admins" 
ON labor_categories
FOR UPDATE 
USING (
  site_id IN (
    SELECT site_id FROM site_members
    WHERE user_id = auth.uid() AND role IN ('Super Admin', 'Admin')
  )
) 
WITH CHECK (
  site_id IN (
    SELECT site_id FROM site_members
    WHERE user_id = auth.uid() AND role IN ('Super Admin', 'Admin')
  )
);

-- Create policy for deleting labor categories
-- Only Super Admin and Admin roles can delete
CREATE POLICY "Labor categories can be deleted by site admins" 
ON labor_categories
FOR DELETE 
USING (
  site_id IN (
    SELECT site_id FROM site_members
    WHERE user_id = auth.uid() AND role IN ('Super Admin', 'Admin')
  )
);
*/

-- OPTION 2: Global categories (simpler, no site_id needed)
-- These policies allow any authenticated user to see all categories

-- View policy (allows all authenticated users to view categories)
CREATE POLICY "Labor categories are viewable by authenticated users" 
ON labor_categories
FOR SELECT 
USING (auth.role() = 'authenticated');

-- Insert policy (all authenticated users can insert)
CREATE POLICY "Labor categories can be inserted by admins" 
ON labor_categories
FOR INSERT 
WITH CHECK (auth.role() = 'authenticated');

-- Update policy (all authenticated users can update)
CREATE POLICY "Labor categories can be updated by admins" 
ON labor_categories
FOR UPDATE 
USING (auth.role() = 'authenticated')
WITH CHECK (auth.role() = 'authenticated');

-- Delete policy (all authenticated users can delete)
CREATE POLICY "Labor categories can be deleted by admins" 
ON labor_categories
FOR DELETE 
USING (auth.role() = 'authenticated'); 