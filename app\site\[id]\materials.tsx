import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { exportMaterialsAsExcel, exportMaterialsAsPDF } from '@/lib/export';
import { supabase } from '@/lib/supabase';
import { MaterialIcons } from '@expo/vector-icons';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useCallback, useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Dimensions,
    FlatList,
    Pressable,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    useColorScheme,
    View
} from 'react-native';

const { width } = Dimensions.get('window');

type Material = {
  id: string;
  name: string;
  specifications: string | null;
  stock_level: number;
  minimum_stock_level: number;
  unit_of_measure: string;
  price: number;
  category: string | null;
  received_date: string;
  created_at: string;
  updated_at: string;
  used_stock?: number;
};

type UserRole = 'Super Admin' | 'Admin' | 'Member' | null;

export default function MaterialsScreen() {
  const { id } = useLocalSearchParams();
  const colorScheme = useColorScheme();
  const [materials, setMaterials] = useState<Material[]>([]);
  const [filteredMaterials, setFilteredMaterials] = useState<Material[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [userRole, setUserRole] = useState<UserRole>(null);
  const [filterOption, setFilterOption] = useState('all');
  const [siteName, setSiteName] = useState<string>('');
  const [exportLoading, setExportLoading] = useState(false);

  // Fetch user role from site members
  const fetchUserRole = useCallback(async () => {
    try {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) return null;

      const { data: memberData, error } = await supabase
        .from('site_members')
        .select('role')
        .eq('site_id', id)
        .eq('user_id', userData.user.id)
        .single();

      if (error) {
        console.error('Error fetching user role:', error);
        return null;
      }

      return memberData?.role as UserRole;
    } catch (error) {
      console.error('Error in fetchUserRole:', error);
      return null;
    }
  }, [id]);

  // Fetch materials data
  const fetchMaterials = useCallback(async (isRefreshing = false) => {
    if (!id) return;

    try {
      if (!isRefreshing) {
        setLoading(true);
      }

      // Fetch site name
      const { data: siteData, error: siteError } = await supabase
        .from('sites')
        .select('name')
        .eq('id', id)
        .single();

      if (siteError) {
        console.error('Error fetching site name:', siteError);
      } else {
        setSiteName(siteData.name || 'Site');
      }

      const { data, error } = await supabase
        .from('materials')
        .select('*, used_stock')
        .eq('site_id', id)
        .order('name');

      if (error) {
        console.error('Error fetching materials:', error);
        Alert.alert('Error', 'Failed to load materials.');
        return;
      }

      setMaterials(data || []);
      setFilteredMaterials(data || []);
    } catch (error) {
      console.error('Error in fetchMaterials:', error);
      Alert.alert('Error', 'Something went wrong while loading materials.');
    } finally {
      setLoading(false);
      if (isRefreshing) {
        setRefreshing(false);
      }
    }
  }, [id]);

  // Handle refresh
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchMaterials(true);
  }, [fetchMaterials]);

  // Initial data load
  useEffect(() => {
    const loadData = async () => {
      const role = await fetchUserRole();
      setUserRole(role);
      await fetchMaterials();
    };
    
    loadData();
  }, [fetchUserRole, fetchMaterials]);

  // Handle search
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredMaterials(materials);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = materials.filter(
        material =>
          material.name.toLowerCase().includes(query) ||
          (material.category && material.category.toLowerCase().includes(query))
      );
      setFilteredMaterials(filtered);
    }
  }, [searchQuery, materials]);

  // Handle filtering by category
  const handleFilterChange = (option: string) => {
    setFilterOption(option);
    
    if (option === 'all') {
      setFilteredMaterials(materials);
    } else if (option === 'low_stock') {
      const lowStock = materials.filter(
        material => material.stock_level <= material.minimum_stock_level
      );
      setFilteredMaterials(lowStock);
    } else {
      // Filter by category
      const filtered = materials.filter(
        material => material.category === option
      );
      setFilteredMaterials(filtered);
    }
  };

  // Get unique categories
  const categories = [...new Set(materials.map(m => m.category).filter(Boolean))];

  // Open material details or edit screen
  const handleMaterialPress = (material: Material) => {
    if (userRole === 'Admin' || userRole === 'Super Admin') {
      router.push(`/site/${id}/edit-material?materialId=${material.id}`);
    } else {
      // For Members, just show details view
      router.push(`/site/${id}/material-details?materialId=${material.id}`);
    }
  };

  // Add new material
  const handleAddMaterial = () => {
    router.push(`/site/${id}/add-material`);
  };

  // Delete material
  const handleDeleteMaterial = async (materialId: string) => {
    if (userRole !== 'Admin' && userRole !== 'Super Admin') {
      Alert.alert('Permission Denied', 'You do not have permission to delete materials.');
      return;
    }

    Alert.alert(
      'Confirm Deletion',
      'Are you sure you want to delete this material? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('materials')
                .delete()
                .eq('id', materialId);

              if (error) {
                console.error('Error deleting material:', error);
                Alert.alert('Error', 'Failed to delete material.');
                return;
              }

              // Remove the material from state
              setMaterials(materials.filter(m => m.id !== materialId));
              setFilteredMaterials(filteredMaterials.filter(m => m.id !== materialId));
              Alert.alert('Success', 'Material deleted successfully.');
            } catch (error) {
              console.error('Error in handleDeleteMaterial:', error);
              Alert.alert('Error', 'Something went wrong while deleting the material.');
            }
          },
        },
      ]
    );
  };

  // Export inventory
  const handleExport = async (format: 'pdf' | 'csv') => {
    try {
      setExportLoading(true);
      
      if (format === 'csv') {
        await exportMaterialsAsExcel(materials, siteName);
        Alert.alert('Success', 'Materials exported as Excel file successfully.');
      } else if (format === 'pdf') {
        await exportMaterialsAsPDF(materials, siteName);
        Alert.alert('Success', 'Materials exported as PDF file successfully.');
      }
    } catch (error) {
      console.error(`Error exporting as ${format}:`, error);
      Alert.alert('Error', `Failed to export materials as ${format.toUpperCase()}.`);
    } finally {
      setExportLoading(false);
    }
  };

  // Render each material item
  const renderMaterialItem = ({ item }: { item: Material }) => {
    const isLowStock = item.stock_level <= item.minimum_stock_level;
    const formattedDate = new Date(item.updated_at).toLocaleDateString();
    const formattedTime = new Date(item.updated_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    const usedStock = item.used_stock || 0;
    const balanceStock = item.stock_level - usedStock;
    
    return (
      <Pressable
        style={styles.itemContainer}
        onPress={() => handleMaterialPress(item)}
      >
        <View style={styles.itemHeader}>
          <ThemedText style={styles.itemName}>{item.name}</ThemedText>
          {isLowStock && (
            <View style={styles.lowStockBadge}>
              <ThemedText style={styles.lowStockText}>Low Stock</ThemedText>
            </View>
          )}
        </View>
        
        {item.specifications && (
          <ThemedText style={styles.specifications} numberOfLines={1}>
            {item.specifications}
          </ThemedText>
        )}
        
        <View style={styles.detailsRow}>
          <View style={styles.detailItem}>
            <ThemedText style={styles.detailLabel}>Total Stock:</ThemedText>
            <ThemedText style={styles.detailValue}>
              {item.stock_level} {item.unit_of_measure}
            </ThemedText>
          </View>
          
          <View style={styles.detailItem}>
            <ThemedText style={styles.detailLabel}>Used:</ThemedText>
            <ThemedText style={styles.detailValue}>
              {usedStock} {item.unit_of_measure}
            </ThemedText>
          </View>
          
          <View style={styles.detailItem}>
            <ThemedText style={styles.detailLabel}>Price:</ThemedText>
            <ThemedText style={styles.detailValue}>
              ₹{item.price.toFixed(2)}
            </ThemedText>
          </View>
        </View>

        <View style={styles.secondaryDetailsRow}>
          <View style={styles.detailItem}>
            <ThemedText style={styles.detailLabel}>Balance Stock:</ThemedText>
            <ThemedText 
              style={[
                styles.detailValue,
                balanceStock < item.minimum_stock_level && styles.lowStockValue,
                balanceStock >= item.minimum_stock_level + 10 && styles.goodStockValue
              ]}
            >
              {balanceStock} {item.unit_of_measure}
            </ThemedText>
          </View>
          
          <View style={[styles.detailItem, styles.lastUpdatedItem]}>
            <ThemedText style={styles.detailLabel}>Last Updated:</ThemedText>
            <ThemedText style={styles.detailValue}>
              {formattedDate} at {formattedTime}
            </ThemedText>
          </View>
        </View>
        
        {/* Action buttons for Admin and Super Admin */}
        {(userRole === 'Admin' || userRole === 'Super Admin') && (
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleMaterialPress(item)}
            >
              <MaterialIcons name="edit" size={20} color="#f97316" />
            </TouchableOpacity>
            
            <TouchableOpacity
              style={styles.actionButton}
              onPress={() => handleDeleteMaterial(item.id)}
            >
              <MaterialIcons name="delete" size={20} color="#ef4444" />
            </TouchableOpacity>
          </View>
        )}
      </Pressable>
    );
  };

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#f97316" />
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      
      <Stack.Screen
        options={{
          title: 'Materials Inventory',
          headerBackTitle: 'Back',
        }}
      />
      
      {/* Search and filter bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <MaterialIcons name="search" size={20} color="#94a3b8" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search materials..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#94a3b8"
          />
        </View>
      </View>
      
      {/* Filter chips */}
      <View style={styles.filterChipsContainer}>
        <ScrollableChips
          options={[
            { label: 'All', value: 'all' },
            { label: 'Low Stock', value: 'low_stock' },
            ...categories.map(category => ({ 
              label: category || 'Uncategorized', 
              value: category || 'Uncategorized' 
            }))
          ]}
          selectedValue={filterOption}
          onSelect={handleFilterChange}
        />
      </View>
      
      {/* Materials list */}
      <FlatList
        data={filteredMaterials}
        renderItem={renderMaterialItem}
        keyExtractor={item => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContentContainer}
        refreshing={refreshing}
        onRefresh={onRefresh}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialIcons name="inventory" size={48} color="#94a3b8" />
            <ThemedText style={styles.emptyText}>
              No materials found. {userRole === 'Member' ? '' : 'Tap the + button to add one.'}
            </ThemedText>
          </View>
        }
      />
      
      {/* Export buttons */}
      {(userRole === 'Admin' || userRole === 'Super Admin') && (
        <View style={styles.exportButtonsContainer}>
          <TouchableOpacity
            style={[styles.exportButton, { backgroundColor: '#4f46e5' }]}
            onPress={() => handleExport('pdf')}
            disabled={exportLoading}
          >
            {exportLoading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <MaterialIcons name="picture-as-pdf" size={16} color="#fff" />
                <ThemedText style={styles.exportButtonText}>Export as PDF</ThemedText>
              </>
            )}
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.exportButton, { backgroundColor: '#16a34a' }]}
            onPress={() => handleExport('csv')}
            disabled={exportLoading}
          >
            {exportLoading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <MaterialIcons name="table-chart" size={16} color="#fff" />
                <ThemedText style={styles.exportButtonText}>Export as CSV</ThemedText>
              </>
            )}
          </TouchableOpacity>
        </View>
      )}
      
      {/* Add material button (only for Admin and Super Admin) */}
      {(userRole === 'Admin' || userRole === 'Super Admin') && (
        <TouchableOpacity
          style={styles.fabButton}
          onPress={handleAddMaterial}
        >
          <MaterialIcons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      )}
    </ThemedView>
  );
}

// Scrollable chips component for filtering
function ScrollableChips({ 
  options, 
  selectedValue, 
  onSelect 
}: { 
  options: { label: string; value: string }[];
  selectedValue: string;
  onSelect: (value: string) => void;
}) {
  return (
    <FlatList
      data={options}
      renderItem={({ item }) => (
        <TouchableOpacity
          style={[
            styles.filterChip,
            selectedValue === item.value && styles.filterChipSelected,
          ]}
          onPress={() => onSelect(item.value)}
        >
          <ThemedText
            style={[
              styles.filterChipText,
              selectedValue === item.value && styles.filterChipTextSelected,
            ]}
          >
            {item.label}
          </ThemedText>
        </TouchableOpacity>
      )}
      keyExtractor={item => item.value}
      horizontal
      showsHorizontalScrollIndicator={false}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    marginBottom: 12,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#f1f5f9',
    borderRadius: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: '#0f172a',
  },
  filterChipsContainer: {
    marginBottom: 12,
  },
  filterChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#f1f5f9',
    borderRadius: 16,
    marginRight: 8,
  },
  filterChipSelected: {
    backgroundColor: '#f97316',
  },
  filterChipText: {
    fontSize: 14,
    color: '#64748b',
  },
  filterChipTextSelected: {
    color: '#fff',
  },
  listContentContainer: {
    paddingBottom: 80,
  },
  itemContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  itemName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0f172a',
    flex: 1,
  },
  lowStockBadge: {
    backgroundColor: '#fef2f2',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8,
  },
  lowStockText: {
    color: '#ef4444',
    fontSize: 12,
    fontWeight: 'bold',
  },
  specifications: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 8,
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  secondaryDetailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f1f5f9',
  },
  detailItem: {
    flex: 1,
  },
  lastUpdatedItem: {
    flex: 2,
    alignItems: 'flex-end',
  },
  detailLabel: {
    fontSize: 12,
    color: '#94a3b8',
  },
  detailValue: {
    fontSize: 14,
    color: '#0f172a',
    fontWeight: '500',
  },
  lowStockValue: {
    color: '#ef4444',
    fontWeight: '600',
  },
  goodStockValue: {
    color: '#16a34a',
    fontWeight: '600',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    borderTopWidth: 1,
    borderTopColor: '#f1f5f9',
    paddingTop: 8,
    marginTop: 4,
  },
  actionButton: {
    padding: 8,
    marginLeft: 12,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
  },
  emptyText: {
    marginTop: 12,
    fontSize: 16,
    color: '#94a3b8',
    textAlign: 'center',
  },
  fabButton: {
    position: 'absolute',
    right: 24,
    bottom: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#f97316',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  exportButtonsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderTopWidth: 1,
    borderTopColor: '#f1f5f9',
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 4,
  },
  exportButtonText: {
    color: '#fff',
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
  },
}); 