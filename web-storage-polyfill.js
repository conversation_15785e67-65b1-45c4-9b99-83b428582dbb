// Global polyfill for web environments - must run before any other imports
// This file provides polyfills for window, localStorage, and AsyncStorage

// Detect environment
const isServer = typeof window === 'undefined';
const isNode = typeof process !== 'undefined' && process.versions && process.versions.node;

// Set up window object for server-side rendering
if (isServer) {
  const mockStorage = {
    getItem: (key) => null,
    setItem: (key, value) => {},
    removeItem: (key) => {},
    clear: () => {},
    key: (index) => null,
    length: 0
  };

  global.window = {
    localStorage: mockStorage,
    sessionStorage: mockStorage,
    location: {
      href: 'http://localhost:3000',
      origin: 'http://localhost:3000',
      protocol: 'http:',
      host: 'localhost:3000',
      hostname: 'localhost',
      port: '3000',
      pathname: '/',
      search: '',
      hash: ''
    },
    document: {
      addEventListener: () => {},
      removeEventListener: () => {},
      cookie: '',
      getElementById: () => null,
      querySelector: () => null,
      querySelectorAll: () => [],
      createElement: (tag) => ({ 
        setAttribute: () => {}, 
        style: {},
        addEventListener: () => {},
        removeEventListener: () => {}
      })
    },
    navigator: {
      userAgent: 'Mozilla/5.0 (compatible; Node.js SSR)',
      platform: 'Node'
    },
    addEventListener: () => {},
    removeEventListener: () => {},
    dispatchEvent: () => true,
    innerWidth: 1024,
    innerHeight: 768,
    screen: {
      width: 1024,
      height: 768
    },
    matchMedia: () => ({
      matches: false,
      addListener: () => {},
      removeListener: () => {}
    })
  };

  // Set up globals
  global.localStorage = mockStorage;
  global.sessionStorage = mockStorage;
  global.document = global.window.document;
  global.navigator = global.window.navigator;
}

// Universal AsyncStorage implementation
const createAsyncStorage = () => {
  // Helper to safely access localStorage
  const getStorage = () => {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        return window.localStorage;
      }
      if (typeof global !== 'undefined' && global.localStorage) {
        return global.localStorage;
      }
    } catch (e) {
      // Storage access might be disabled
    }
    return null;
  };

  return {
    getItem: async (key) => {
      try {
        const storage = getStorage();
        return storage ? storage.getItem(key) : null;
      } catch (error) {
        console.warn('AsyncStorage getItem error:', error);
        return null;
      }
    },

    setItem: async (key, value) => {
      try {
        const storage = getStorage();
        if (storage) {
          storage.setItem(key, value);
        }
      } catch (error) {
        console.warn('AsyncStorage setItem error:', error);
      }
    },

    removeItem: async (key) => {
      try {
        const storage = getStorage();
        if (storage) {
          storage.removeItem(key);
        }
      } catch (error) {
        console.warn('AsyncStorage removeItem error:', error);
      }
    },

    clear: async () => {
      try {
        const storage = getStorage();
        if (storage) {
          storage.clear();
        }
      } catch (error) {
        console.warn('AsyncStorage clear error:', error);
      }
    },

    getAllKeys: async () => {
      try {
        const storage = getStorage();
        if (storage) {
          return Object.keys(storage);
        }
        return [];
      } catch (error) {
        console.warn('AsyncStorage getAllKeys error:', error);
        return [];
      }
    },

    multiGet: async (keys) => {
      try {
        const storage = getStorage();
        if (storage) {
          return keys.map(key => [key, storage.getItem(key)]);
        }
        return keys.map(key => [key, null]);
      } catch (error) {
        console.warn('AsyncStorage multiGet error:', error);
        return keys.map(key => [key, null]);
      }
    },

    multiSet: async (keyValuePairs) => {
      try {
        const storage = getStorage();
        if (storage) {
          keyValuePairs.forEach(([key, value]) => {
            storage.setItem(key, value);
          });
        }
      } catch (error) {
        console.warn('AsyncStorage multiSet error:', error);
      }
    }
  };
};

// Create and set up AsyncStorage
const AsyncStoragePolyfill = createAsyncStorage();

// Set up global AsyncStorage
global.AsyncStorage = AsyncStoragePolyfill;

// Set on window if available
if (typeof window !== 'undefined') {
  window.AsyncStorage = AsyncStoragePolyfill;
}

// Export for module usage
export default AsyncStoragePolyfill; 