// @deno-types="https://deno.land/x/xhr@0.3.0/mod.d.ts"
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ShopPaymentUpdateRequest {
  razorpay_order_id: string;
  razorpay_payment_id?: string;
  razorpay_signature?: string;
  payment_status: 'success' | 'cancelled' | 'failed';
  order_id: number; // Shop order ID from database
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: authHeader },
        },
      }
    )

    // Get the current user
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser()
    if (userError || !user) {
      throw new Error('User not authenticated')
    }

    // Parse request body
    const { 
      razorpay_order_id, 
      razorpay_payment_id, 
      razorpay_signature, 
      payment_status, 
      order_id 
    }: ShopPaymentUpdateRequest = await req.json()

    // Validate required fields
    if (!razorpay_order_id || !payment_status || !order_id) {
      throw new Error('Missing required fields')
    }

    console.log('Updating shop order payment:', {
      order_id,
      razorpay_order_id,
      payment_status,
      user_id: user.id
    })

    // Update shop order with payment details
    const { data: updatedOrder, error: orderError } = await supabaseClient
      .from('shop_orders')
      .update({
        razorpay_payment_id: razorpay_payment_id,
        razorpay_signature: razorpay_signature,
        payment_status: payment_status,
        status: payment_status === 'success' ? 'confirmed' : 'cancelled',
        updated_at: new Date().toISOString(),
        // Set estimated delivery time for successful orders (35 minutes from now)
        estimated_delivery_time: payment_status === 'success' 
          ? new Date(Date.now() + 35 * 60 * 1000).toISOString()
          : null
      })
      .eq('id', order_id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (orderError) {
      console.error('Error updating shop order:', orderError)
      throw new Error('Failed to update shop order')
    }

    console.log('Shop order updated successfully:', updatedOrder.id)

    // Update razorpay_orders table
    const { error: razorpayError } = await supabaseClient
      .from('razorpay_orders')
      .update({
        status: payment_status === 'success' ? 'paid' : 'cancelled',
        updated_at: new Date().toISOString()
      })
      .eq('order_id', razorpay_order_id)

    if (razorpayError) {
      console.error('Error updating razorpay order:', razorpayError)
      // Don't throw error here as the main order update was successful
    }

    // Clear user's cart after successful payment
    if (payment_status === 'success') {
      const { error: cartError } = await supabaseClient
        .from('shop_cart')
        .delete()
        .eq('user_id', user.id)

      if (cartError) {
        console.error('Error clearing cart:', cartError)
        // Don't throw error here as the payment was successful
      } else {
        console.log('Cart cleared successfully for user:', user.id)
      }
    }

    return new Response(
      JSON.stringify({
        success: true,
        order: updatedOrder,
        message: payment_status === 'success' 
          ? 'Order confirmed successfully!' 
          : 'Order cancelled'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in update-shop-order-payment function:', error)
    return new Response(
      JSON.stringify({
        error: error.message,
        message: 'Failed to update shop order payment'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})
