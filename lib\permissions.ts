/**
 * Permissions utility functions to manage role-based access control
 */

export type UserRole = 'Super Admin' | 'Admin' | 'Member';

/**
 * Check if user has permission to view reports
 * Only Admin and Super Admin can view reports
 */
export function canViewReports(role: UserRole | null | undefined): boolean {
  return role === 'Admin' || role === 'Super Admin';
}

/**
 * Check if user has permission to export/download reports
 * Only Admin and Super Admin can export reports
 */
export function canExportReports(role: UserRole | null | undefined): boolean {
  return role === 'Admin' || role === 'Super Admin';
}

/**
 * Check if user has permission to manage report templates
 * Only Super Admin can manage templates
 */
export function canManageReportTemplates(role: UserRole | null | undefined): boolean {
  return role === 'Super Admin';
}

/**
 * Check if user has full access to the reports section
 * Only Super Admin has full access
 */
export function hasFullReportAccess(role: UserRole | null | undefined): boolean {
  return role === 'Super Admin';
} 