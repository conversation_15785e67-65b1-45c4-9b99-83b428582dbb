# Google Play In-App Updates Implementation

This document explains the Google Play in-app updates implementation that has been added to the Infratask Android app.

## Overview

The app now automatically checks for updates when it starts and handles both **IMMEDIATE** and **FLEXIBLE** update flows as recommended by Google.

## Implementation Details

### Dependencies Added

The following dependencies were added to `android/app/build.gradle`:

```gradle
// Google Play In-App Update
implementation 'com.google.android.play:app-update:2.1.0'
implementation 'com.google.android.play:app-update-ktx:2.1.0'
```

### Update Logic

The implementation follows Google's best practices:

1. **Update Check**: Automatically checks for updates when the app starts (`onCreate`)
2. **Update Strategy**: 
   - **IMMEDIATE updates**: For critical updates or updates that are 7+ days old
   - **FLEXIBLE updates**: For newer, non-critical updates
3. **Resume Handling**: Resumes interrupted immediate updates when the app comes to foreground
4. **Proper Cleanup**: Unregisters listeners to prevent memory leaks

### Update Flow Decision

```kotlin
when {
  // Critical/old updates → Immediate update
  stalenessDays >= DAYS_FOR_FLEXIBLE_UPDATE && appUpdateInfo.isImmediateUpdateAllowed -> {
    startImmediateUpdate(appUpdateInfo)
  }
  // Regular updates → Flexible update
  appUpdateInfo.isFlexibleUpdateAllowed -> {
    startFlexibleUpdate(appUpdateInfo)
  }
}
```

### Key Features

1. **Immediate Updates**:
   - Full-screen UI that blocks app usage until update is complete
   - Automatically resumes if interrupted
   - Best for critical security updates

2. **Flexible Updates**:
   - Downloads in background while user continues using the app
   - Shows notification when download is complete
   - Automatically completes the update

3. **Error Handling**:
   - Comprehensive logging for debugging
   - Graceful handling of update failures
   - Proper listener management

## Configuration

### Customizable Parameters

- `DAYS_FOR_FLEXIBLE_UPDATE = 7`: Number of days before switching from flexible to immediate updates
- `MY_REQUEST_CODE = 500`: Request code for update activity results

## TROUBLESHOOTING: Why In-App Updates Don't Work

### Common Issue: Version Code Problem

**Symptom**: No updates detected even after increasing version code

**Root Cause**: Version code must be properly managed for testing

**Solution**: Follow this exact testing workflow:

### Step-by-Step Testing Workflow

#### Step 1: Prepare Base Version (Lower Version Code)
```bash
# Set initial version code
node scripts/bump-version.js
# This will set version code to 2 in app.json
```

#### Step 2: Build and Upload Base Version to Play Console
```bash
# Build release APK/AAB with version code 2
eas build --platform android --profile production

# Upload this build to Google Play Console Internal Testing
# Install this version on your test device from Play Store
```

#### Step 3: Create Higher Version for Testing
```bash
# Bump version code again
node scripts/bump-version.js
# This will set version code to 3 in app.json
```

#### Step 4: Upload Test Version to Play Console
```bash
# Build with higher version code (3)
eas build --platform android --profile production

# Upload to Google Play Console Internal Testing
# DO NOT install this on device - it should be available for download only
```

#### Step 5: Test In-App Update
- Open the app with version code 2 (installed from Play Store)
- The app should detect version code 3 is available
- In-app update should trigger

### Critical Requirements Checklist

- [ ] **Play Store Installation**: App must be installed from Google Play Store (not sideloaded)
- [ ] **Version Code Sequence**: Device version < Play Console version
- [ ] **Same Package ID**: Both versions must have identical `applicationId`
- [ ] **Same Signing**: Both versions must be signed with same certificate
- [ ] **Owner Account**: Testing account must own the app or have testing access
- [ ] **Internal Testing**: Upload to Internal Testing track first

### Version Code Management

Use the provided script to manage version codes:

```bash
# Bump version code before each build
node scripts/bump-version.js

# Check current version
grep -r "versionCode" app.json
```

### Debug Commands

Monitor in-app update logs:
```bash
# Filter logs for update information
adb logcat -s MainActivity:D

# Look for these key messages:
# "Update available"
# "No update available" 
# "Starting immediate update"
# "Starting flexible update"
```

### Testing

To test in-app updates:

1. **Internal Testing**: Upload your app to Google Play Console internal testing track
2. **Version Testing**: Upload a newer version to test the update flow
3. **Debug Logs**: Check logcat for update status messages with tag "MainActivity"

## Logs to Monitor

The implementation provides detailed logging:

```
D/MainActivity: Update available
D/MainActivity: Starting immediate update
D/MainActivity: Starting flexible update
D/MainActivity: Update downloaded, showing complete update UI
D/MainActivity: Update installed successfully
```

## Requirements

- Android 5.0 (API level 21) or higher
- App must be downloaded from Google Play Store
- App must be signed with the same certificate as the store version

## Important Notes

1. **Testing Limitation**: In-app updates only work with apps installed from Google Play Store
2. **Development**: For development builds, the update check will not show updates
3. **Production**: This feature only activates when users have the production app installed from Play Store

## Google Play Console Setup

To use this feature effectively:

1. Upload your app to Google Play Console
2. Use staged rollouts for testing
3. Monitor update adoption rates in Play Console analytics
4. Set appropriate update priorities for different release types

## Security Considerations

- Updates are cryptographically verified by Google Play
- Only genuine updates from the same developer account are allowed
- Users maintain control over when flexible updates are applied 