#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Building development build with Razorpay support...\n');

// Check if EAS CLI is installed
try {
  execSync('eas --version', { stdio: 'ignore' });
} catch (error) {
  console.log('📦 Installing EAS CLI...');
  execSync('npm install -g @expo/eas-cli', { stdio: 'inherit' });
}

// Check if logged in to EAS
try {
  execSync('eas whoami', { stdio: 'ignore' });
} catch (error) {
  console.log('🔐 Please login to EAS:');
  execSync('eas login', { stdio: 'inherit' });
}

console.log('🔨 Building development build for Android...');
console.log('This will create a development build that includes native modules like Razorpay.\n');

try {
  // Build development build
  execSync('eas build --platform android --profile development', { stdio: 'inherit' });
  
  console.log('\n✅ Development build completed!');
  console.log('\n📱 To install and run:');
  console.log('1. Download the APK from the EAS build page');
  console.log('2. Install it on your device');
  console.log('3. Run: npx expo start --dev-client');
  console.log('4. Scan the QR code with the development build app');
  console.log('\n💡 This development build includes Razorpay native module support!');
  
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
