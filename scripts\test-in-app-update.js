#!/usr/bin/env node

/**
 * Script to help test in-app update configuration
 * Usage: node scripts/test-in-app-update.js
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 In-App Update Configuration Test\n');

function checkVersionConfiguration() {
  const appJsonPath = path.join(__dirname, '..', 'app.json');
  
  try {
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    const versionCode = appJson.expo.android.versionCode;
    const version = appJson.expo.version;
    
    console.log('📱 Current Version Configuration:');
    console.log(`   Version: ${version}`);
    console.log(`   Version Code: ${versionCode}`);
    console.log('');
    
    if (versionCode === 1) {
      console.log('⚠️  WARNING: Version code is 1 - you need to bump it for testing');
      console.log('   Run: node scripts/bump-version.js');
      console.log('');
    } else {
      console.log('✅ Version code looks good for testing');
      console.log('');
    }
    
    return versionCode;
  } catch (error) {
    console.error('❌ Error reading app.json:', error.message);
    return null;
  }
}

function printTestingInstructions(versionCode) {
  console.log('📋 Testing Instructions:');
  console.log('');
  console.log('To test in-app updates, you need TWO versions:');
  console.log('');
  console.log('1️⃣ BASE VERSION (Install on device):');
  console.log(`   • Keep current version code: ${versionCode}`);
  console.log('   • Build: eas build --platform android --profile production');
  console.log('   • Upload to Google Play Console Internal Testing');
  console.log('   • Install on device from Play Store');
  console.log('');
  console.log('2️⃣ UPDATE VERSION (For testing):');
  console.log('   • Run: node scripts/bump-version.js');
  console.log(`   • This will create version code: ${versionCode + 1}`);
  console.log('   • Build: eas build --platform android --profile production');
  console.log('   • Upload to Google Play Console (DO NOT install on device)');
  console.log('');
  console.log('3️⃣ TEST:');
  console.log('   • Open the app (version ' + versionCode + ') on device');
  console.log('   • App should detect version ' + (versionCode + 1) + ' available');
  console.log('   • In-app update should trigger');
  console.log('');
}

function printDebuggingCommands() {
  console.log('🐛 Debugging Commands:');
  console.log('');
  console.log('Monitor update logs:');
  console.log('   adb logcat -s MainActivity:D');
  console.log('');
  console.log('Check current app version on device:');
  console.log('   adb shell dumpsys package com.infratasks.app | grep versionCode');
  console.log('');
  console.log('Key log messages to look for:');
  console.log('   • "Update available" - Update detected');
  console.log('   • "No update available" - No update found');
  console.log('   • "Starting immediate update" - Critical update');
  console.log('   • "Starting flexible update" - Regular update');
  console.log('');
}

function printRequirements() {
  console.log('✅ Requirements Checklist:');
  console.log('');
  console.log('   [ ] App installed from Google Play Store (not sideloaded)');
  console.log('   [ ] Device version < Play Console version');
  console.log('   [ ] Same package ID (com.infratasks.app)');
  console.log('   [ ] Same signing certificate');
  console.log('   [ ] Testing account owns the app or has testing access');
  console.log('   [ ] Higher version uploaded to Internal Testing track');
  console.log('');
}

// Run the checks
const versionCode = checkVersionConfiguration();
if (versionCode) {
  printTestingInstructions(versionCode);
  printRequirements();
  printDebuggingCommands();
}