-- Function to search profiles by name, email, or phone
CREATE OR REPLACE FUNCTION search_profiles(search_term TEXT) 
RETURNS SETOF profiles AS $$
BEGIN
  RETURN QUERY 
  SELECT * 
  FROM profiles 
  WHERE 
    (full_name ILIKE '%' || search_term || '%') OR
    (email ILIKE '%' || search_term || '%') OR
    (phone_number ILIKE '%' || search_term || '%')
  LIMIT 50;
END;
$$ LANGUAGE plpgsql;

-- <PERSON> execute permission on the function to authenticated users and anon users
GRANT EXECUTE ON FUNCTION search_profiles(TEXT) TO authenticated, anon;

-- Example usage:
-- SELECT * FROM search_profiles('john');
-- Will return any profiles where 'john' appears in the name, email, or phone number 