#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing Razorpay integration for development...\n');

// Check current setup
console.log('📋 Current setup:');
console.log('- Expo SDK: 53');
console.log('- react-native-razorpay: 2.3.0');
console.log('- Platform: Development build required\n');

console.log('🎯 Solutions available:\n');

console.log('1. 🚀 RECOMMENDED: Create Development Build');
console.log('   - Includes native Razorpay module');
console.log('   - Full payment functionality');
console.log('   - Run: npm run build-dev\n');

console.log('2. 🌐 QUICK FIX: Web-based Payment (Already implemented)');
console.log('   - Uses WebBrowser for payments');
console.log('   - Works in current development setup');
console.log('   - Limited functionality\n');

console.log('3. 🧪 TESTING: Mock Payments');
console.log('   - For development testing only');
console.log('   - No real payment processing\n');

const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

rl.question('Choose option (1, 2, or 3): ', (answer) => {
  switch(answer) {
    case '1':
      console.log('\n🚀 Starting development build...');
      try {
        execSync('node scripts/build-dev.js', { stdio: 'inherit' });
      } catch (error) {
        console.error('Build failed. Please run manually: npm run build-dev');
      }
      break;
      
    case '2':
      console.log('\n🌐 Web-based payment is already configured!');
      console.log('The app will automatically use WebBrowser fallback when native module is not available.');
      console.log('\nTo test: Try making a payment in the app - it will open a web-based checkout.');
      break;
      
    case '3':
      console.log('\n🧪 Mock payments are available in the test transaction section.');
      console.log('Use the "Test Transaction" button to simulate payments without real processing.');
      break;
      
    default:
      console.log('\n❌ Invalid option. Please run the script again.');
  }
  
  rl.close();
});
