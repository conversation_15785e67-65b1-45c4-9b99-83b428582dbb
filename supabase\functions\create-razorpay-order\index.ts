// @deno-types="https://deno.land/x/xhr@0.3.0/mod.d.ts"
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface OrderRequest {
  amount: number; // Amount in paise
  currency?: string;
  receipt?: string;
  partial_payment?: boolean;
  notes?: Record<string, string>;
  user_id?: string;
  plan_id?: string;
  order_type?: 'subscription' | 'shop'; // New field to distinguish order types
  cart_items?: Array<{
    product_id: number;
    quantity: number;
    unit_price: number;
    total_price: number;
  }>; // For shop orders
  delivery_address?: string; // For shop orders
}

interface RazorpayOrderResponse {
  id: string;
  entity: string;
  amount: number;
  amount_paid: number;
  amount_due: number;
  currency: string;
  receipt: string;
  offer_id: string | null;
  status: string;
  attempts: number;
  notes: Record<string, string>;
  created_at: number;
}

console.log("Starting Razorpay Order Creation Function")

serve(async (req) => {
  console.log(`${req.method} ${req.url}`)

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get authorization header
    const authHeader = req.headers.get('authorization')
    console.log('Auth header present:', !!authHeader)

    // Initialize Supabase client for JWT verification
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY')!
    const supabase = createClient(supabaseUrl, supabaseAnonKey, {
      global: {
        headers: {
          Authorization: authHeader!,
        },
      },
    })

    // Verify user authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      console.error('Authentication failed:', authError)
      return new Response(
        JSON.stringify({ error: 'Authentication required' }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('Authenticated user:', user.id)

    // Get Razorpay credentials from environment variables
    // Check if we should use live keys (production environment only)
    // This ensures development builds use test keys consistently with client-side logic
    const isProduction = Deno.env.get('ENVIRONMENT') === 'production'

    const RAZORPAY_KEY_ID = isProduction
      ? (Deno.env.get('RAZORPAY_KEY_ID_LIVE') || '***********************')
      : (Deno.env.get('RAZORPAY_KEY_ID') || 'rzp_test_NQ26LCch0J1GHa')

    const RAZORPAY_KEY_SECRET = isProduction
      ? (Deno.env.get('RAZORPAY_KEY_SECRET_LIVE') || 'DgGurBMtCbIaIDRas7YxIYVg')
      : (Deno.env.get('RAZORPAY_KEY_SECRET') || 'gsedvop5Yvz7fKnilyqy8L37')

    console.log('Edge Function Environment Detection:')
    console.log('- ENVIRONMENT:', Deno.env.get('ENVIRONMENT'))
    console.log('- isProduction:', isProduction)
    console.log('Environment:', isProduction ? 'Production (Live Keys)' : 'Development (Test Keys)')
    console.log('Razorpay Key ID:', RAZORPAY_KEY_ID ? RAZORPAY_KEY_ID.substring(0, 12) + '...' : 'Missing')
    console.log('Razorpay Key Secret:', RAZORPAY_KEY_SECRET ? 'Present' : 'Missing')

    if (!RAZORPAY_KEY_ID || !RAZORPAY_KEY_SECRET) {
      throw new Error('Razorpay credentials not found')
    }

    // Parse request body
    const requestBody = await req.text()
    console.log('Request body:', requestBody)

    let parsedBody: OrderRequest
    try {
      parsedBody = JSON.parse(requestBody)
    } catch (parseError) {
      console.error('JSON parse error:', parseError)
      return new Response(
        JSON.stringify({ error: 'Invalid JSON in request body' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    const { amount, currency = 'INR', receipt, partial_payment = false, notes = {}, plan_id } = parsedBody

    console.log('Parsed request:', { amount, currency, receipt, user_id: user.id, plan_id })

    // Validate required fields
    if (!amount || amount <= 0) {
      return new Response(
        JSON.stringify({ error: 'Amount is required and must be greater than 0' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    // Generate receipt if not provided
    const orderReceipt = receipt || `receipt_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`

    // Prepare Razorpay order data
    const orderData = {
      amount: amount, // Amount in paise
      currency: currency,
      receipt: orderReceipt,
      partial_payment: partial_payment,
      notes: {
        ...notes,
        user_id: user.id,
        plan_id: plan_id || '',
        created_via: 'supabase_edge_function'
      }
    }

    console.log('Order data to send to Razorpay:', orderData)

    // Create Basic Auth header for Razorpay API
    const authString = `${RAZORPAY_KEY_ID}:${RAZORPAY_KEY_SECRET}`
    const razorpayAuthHeader = btoa(authString)

    console.log('Making request to Razorpay API...')

    // Call Razorpay Orders API
    const razorpayResponse = await fetch('https://api.razorpay.com/v1/orders', {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${razorpayAuthHeader}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderData)
    })

    console.log('Razorpay response status:', razorpayResponse.status)

    if (!razorpayResponse.ok) {
      const errorData = await razorpayResponse.text()
      console.error('Razorpay API Error:', errorData)
      throw new Error(`Razorpay API error: ${razorpayResponse.status} ${errorData}`)
    }

    const razorpayOrder: RazorpayOrderResponse = await razorpayResponse.json()
    console.log('Razorpay order created:', razorpayOrder.id)

    // Initialize Supabase client with service role for database operations
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)

    // Log the order creation in database (optional)
    if (user.id) {
      try {
        console.log('Logging order to database...')
        const { error: logError } = await supabaseAdmin
          .from('razorpay_orders')
          .insert([
            {
              order_id: razorpayOrder.id,
              user_id: user.id,
              amount: razorpayOrder.amount,
              currency: razorpayOrder.currency,
              receipt: razorpayOrder.receipt,
              status: razorpayOrder.status,
              plan_id: plan_id,
              notes: razorpayOrder.notes,
              razorpay_created_at: new Date(razorpayOrder.created_at * 1000).toISOString()
            }
          ])

        if (logError) {
          console.error('Error logging order to database:', logError)
          // Don't fail the request if logging fails
        } else {
          console.log('Order logged to database successfully')
        }
      } catch (dbError) {
        console.error('Database logging error:', dbError)
        // Continue without failing the request
      }
    }

    // Return the Razorpay order response
    const response = {
      success: true,
      order: razorpayOrder,
      message: 'Order created successfully'
    }

    console.log('Returning success response')
    return new Response(
      JSON.stringify(response),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error: any) {
    console.error('Error creating Razorpay order:', error)

    return new Response(
      JSON.stringify({
        error: 'Failed to create order',
        message: error.message
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
