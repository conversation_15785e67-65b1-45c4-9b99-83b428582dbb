import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { supabase } from '@/lib/supabase';
import { Feather, MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import * as FileSystem from 'expo-file-system';
import * as Print from 'expo-print';
import { Stack, useLocalSearchParams, useNavigation } from 'expo-router';
import * as Sharing from 'expo-sharing';
import { StatusBar } from 'expo-status-bar';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Keyboard,
  KeyboardAvoidingView,
  Modal,
  Platform,
  RefreshControl,
  ScrollView,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  useColorScheme,
  View
} from 'react-native';

// Types
type SubContractor = {
  id: string;
  name: string;
  category: string;
  site_id: string;
  created_at: string;
  updated_at: string;
};

type SubContractorLaborer = {
  id: string;
  full_name: string;
  phone_number: string;
  sub_contractor_id: string;
  site_id: string;
  category: string;
  remarks: string | null;
  daily_wage: number | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
};

type SubContractorAttendanceSummary = {
  attendance_date: string;
  present_count: number;
  absent_count: number;
  half_day_count: number;
  overtime_count: number;
  total_overtime_hours: number;
  total_laborers: number;
  marked_laborers: number;
  overall_status: 'no_laborers' | 'not_marked' | 'present' | 'absent' | 'mixed';
};

type LaborerAttendanceRecord = {
  id: string;
  laborer_id: string;
  status: 'present' | 'absent' | 'half_day' | 'overtime';
  attendance_date: string;
  overtime_hours?: number;
};

type Site = {
  id: string;
  name: string;
  organization_name: string;
};

// Add Laborer Modal component (defined outside main component to prevent re-renders)
const AddLaborerModal = React.memo(({ 
  visible,
  onClose,
  colorScheme,
  newLaborerName,
  setNewLaborerName,
  newLaborerPhone,
  setNewLaborerPhone,
  newLaborerCategory,
  setNewLaborerCategory,
  newLaborerDailyWage,
  setNewLaborerDailyWage,
  newLaborerRemarks,
  setNewLaborerRemarks,
  onAddLaborer,
  phoneInputRef,
  categoryInputRef,
  dailyWageInputRef,
  remarksInputRef
}: {
  visible: boolean;
  onClose: () => void;
  colorScheme: 'light' | 'dark' | null | undefined;
  newLaborerName: string;
  setNewLaborerName: (value: string) => void;
  newLaborerPhone: string;
  setNewLaborerPhone: (value: string) => void;
  newLaborerCategory: string;
  setNewLaborerCategory: (value: string) => void;
  newLaborerDailyWage: string;
  setNewLaborerDailyWage: (value: string) => void;
  newLaborerRemarks: string;
  setNewLaborerRemarks: (value: string) => void;
  onAddLaborer: () => void;
  phoneInputRef: React.RefObject<TextInput | null>;
  categoryInputRef: React.RefObject<TextInput | null>;
  dailyWageInputRef: React.RefObject<TextInput | null>;
  remarksInputRef: React.RefObject<TextInput | null>;
}) => (
  <Modal
    visible={visible}
    transparent={true}
    animationType="fade"
    onRequestClose={onClose}
  >
    <View style={styles.modalContainer}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <View style={[
          styles.addLaborerModalContent,
          { backgroundColor: colorScheme === 'dark' ? '#1f2937' : '#ffffff' }
        ]}>
          <View style={styles.modalHeader}>
            <ThemedText style={styles.modalTitle}>Add Laborer</ThemedText>
            <TouchableOpacity onPress={() => {
              Keyboard.dismiss();
              onClose();
            }}>
              <MaterialIcons name="close" size={24} color="#64748b" />
            </TouchableOpacity>
          </View>

          <ScrollView
            style={styles.addLaborerForm}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
            contentContainerStyle={styles.formScrollContent}
          >
            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Full Name *</ThemedText>
              <TextInput
                style={[
                  styles.formInput,
                  {
                    backgroundColor: colorScheme === 'dark' ? '#374151' : '#f8fafc',
                    color: colorScheme === 'dark' ? '#ffffff' : '#1f2937',
                    borderColor: colorScheme === 'dark' ? '#4b5563' : '#e2e8f0',
                  },
                ]}
                value={newLaborerName}
                onChangeText={setNewLaborerName}
                placeholder="Enter laborer's full name"
                placeholderTextColor="#94a3b8"
                autoFocus
                blurOnSubmit={false}
                returnKeyType="next"
                onSubmitEditing={() => phoneInputRef.current?.focus()}
                selectionColor="#f97316"
              />
            </View>

            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Phone Number</ThemedText>
              <TextInput
                ref={phoneInputRef}
                style={[
                  styles.formInput,
                  {
                    backgroundColor: colorScheme === 'dark' ? '#374151' : '#f8fafc',
                    color: colorScheme === 'dark' ? '#ffffff' : '#1f2937',
                    borderColor: colorScheme === 'dark' ? '#4b5563' : '#e2e8f0',
                  },
                ]}
                value={newLaborerPhone}
                onChangeText={setNewLaborerPhone}
                placeholder="Enter phone number"
                placeholderTextColor="#94a3b8"
                keyboardType="phone-pad"
                blurOnSubmit={false}
                returnKeyType="next"
                onSubmitEditing={() => categoryInputRef.current?.focus()}
                selectionColor="#f97316"
              />
            </View>

            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Category</ThemedText>
              <TextInput
                ref={categoryInputRef}
                style={[
                  styles.formInput,
                  {
                    backgroundColor: colorScheme === 'dark' ? '#374151' : '#f8fafc',
                    color: colorScheme === 'dark' ? '#ffffff' : '#1f2937',
                    borderColor: colorScheme === 'dark' ? '#4b5563' : '#e2e8f0',
                  },
                ]}
                value={newLaborerCategory}
                onChangeText={setNewLaborerCategory}
                placeholder="e.g., Helper, Skilled Worker"
                placeholderTextColor="#94a3b8"
                blurOnSubmit={false}
                returnKeyType="next"
                onSubmitEditing={() => dailyWageInputRef.current?.focus()}
                selectionColor="#f97316"
              />
            </View>

            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Daily Wage (₹)</ThemedText>
              <TextInput
                ref={dailyWageInputRef}
                style={[
                  styles.formInput,
                  {
                    backgroundColor: colorScheme === 'dark' ? '#374151' : '#f8fafc',
                    color: colorScheme === 'dark' ? '#ffffff' : '#1f2937',
                    borderColor: colorScheme === 'dark' ? '#4b5563' : '#e2e8f0',
                  },
                ]}
                value={newLaborerDailyWage}
                onChangeText={setNewLaborerDailyWage}
                placeholder="Enter daily wage"
                placeholderTextColor="#94a3b8"
                keyboardType="numeric"
                blurOnSubmit={false}
                returnKeyType="next"
                onSubmitEditing={() => remarksInputRef.current?.focus()}
                selectionColor="#f97316"
              />
            </View>

            <View style={styles.inputContainer}>
              <ThemedText style={styles.inputLabel}>Remarks</ThemedText>
              <TextInput
                ref={remarksInputRef}
                style={[
                  styles.formInput,
                  styles.textArea,
                  {
                    backgroundColor: colorScheme === 'dark' ? '#374151' : '#f8fafc',
                    color: colorScheme === 'dark' ? '#ffffff' : '#1f2937',
                    borderColor: colorScheme === 'dark' ? '#4b5563' : '#e2e8f0',
                  },
                ]}
                value={newLaborerRemarks}
                onChangeText={setNewLaborerRemarks}
                placeholder="Any additional notes"
                placeholderTextColor="#94a3b8"
                multiline
                numberOfLines={3}
                textAlignVertical="top"
                blurOnSubmit={true}
                returnKeyType="done"
                onSubmitEditing={onAddLaborer}
                selectionColor="#f97316"
              />
            </View>
          </ScrollView>

          <View style={styles.modalActions}>
            <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton]}
              onPress={() => {
                Keyboard.dismiss();
                onClose();
              }}
            >
              <ThemedText style={styles.cancelButtonText}>Cancel</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.modalButton, styles.confirmButton]}
              onPress={() => {
                Keyboard.dismiss();
                onAddLaborer();
              }}
            >
              <ThemedText style={styles.confirmButtonText}>Add Laborer</ThemedText>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </View>
  </Modal>
));

// Custom calendar component
const CompactWeekCalendar = ({ 
  selectedDate, 
  onDateChange 
}: { 
  selectedDate: string, 
  onDateChange: (date: string) => void 
}) => {
  // Generate 7 days - 3 days before, current day, and 3 days after
  const generateWeekDays = () => {
    const today = new Date(selectedDate);
    const days = [];
    
    for (let i = -3; i <= 3; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      days.push({
        dateString: date.toISOString().split('T')[0],
        day: date.getDate(),
        month: date.getMonth(),
        year: date.getFullYear(),
        dayName: date.toLocaleDateString('en-US', { weekday: 'short' }),
      });
    }
    
    return days;
  };
  
  const weekDays = generateWeekDays();
  
  return (
    <View style={styles.compactCalendarContainer}>
      <TouchableOpacity style={styles.calendarArrow} onPress={() => {
        const prevDate = new Date(selectedDate);
        prevDate.setDate(prevDate.getDate() - 7);
        onDateChange(prevDate.toISOString().split('T')[0]);
      }}>
        <Feather name="chevron-left" size={24} color="#4A5568" />
      </TouchableOpacity>
      
      <View style={styles.weekDaysContainer}>
        {weekDays.map((day) => (
          <TouchableOpacity
            key={day.dateString}
            style={[
              styles.dayContainer,
              selectedDate === day.dateString && styles.selectedDayContainer,
              day.dateString === new Date().toISOString().split('T')[0] && styles.todayContainer
            ]}
            onPress={() => onDateChange(day.dateString)}
          >
            <ThemedText style={[
              styles.dayNameText,
              selectedDate === day.dateString && styles.selectedDayText
            ]}>
              {day.dayName}
            </ThemedText>
            <View style={[
              styles.dayCircle,
              selectedDate === day.dateString && styles.selectedDayCircle
            ]}>
              <ThemedText style={[
                styles.dayNumberText,
                selectedDate === day.dateString && styles.selectedDayNumberText
              ]}>
                {day.day}
              </ThemedText>
            </View>
          </TouchableOpacity>
        ))}
      </View>
      
      <TouchableOpacity style={styles.calendarArrow} onPress={() => {
        const nextDate = new Date(selectedDate);
        nextDate.setDate(nextDate.getDate() + 7);
        onDateChange(nextDate.toISOString().split('T')[0]);
      }}>
        <Feather name="chevron-right" size={24} color="#4A5568" />
      </TouchableOpacity>
    </View>
  );
};

// Add Report Modal component
const ReportModal = React.memo(({ 
  visible,
  onClose,
  colorScheme,
  onGenerateReport,
  loading
}: {
  visible: boolean;
  onClose: () => void;
  colorScheme: 'light' | 'dark' | null | undefined;
  onGenerateReport: (startDate: string, endDate: string, reportType: string, format: string) => void;
  loading: boolean;
}) => {
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const [showStartPicker, setShowStartPicker] = useState(false);
  const [showEndPicker, setShowEndPicker] = useState(false);
  const [reportType, setReportType] = useState('summary'); // 'summary', 'detailed', 'payroll'
  const [reportFormat, setReportFormat] = useState('csv'); // 'csv', 'pdf'

  // Get screen dimensions for responsive design
  const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
  const isSmallScreen = screenWidth < 400;

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleGenerateReport = () => {
    if (startDate > endDate) {
      Alert.alert('Error', 'Start date cannot be after end date');
      return;
    }

    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 90) {
      Alert.alert('Error', 'Date range cannot exceed 90 days');
      return;
    }

    onGenerateReport(
      startDate.toISOString().split('T')[0],
      endDate.toISOString().split('T')[0],
      reportType,
      reportFormat
    );
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.keyboardAvoidingView}
        >
          <View style={[
            styles.reportModalContent,
            { 
              backgroundColor: colorScheme === 'dark' ? '#1f2937' : '#ffffff',
              width: isSmallScreen ? '85%' : '75%',
              maxHeight: '90%'
            }
          ]}>
            <View style={styles.modalHeader}>
              <ThemedText style={styles.modalTitle}>Generate Report</ThemedText>
              <TouchableOpacity 
                onPress={onClose} 
                disabled={loading}
                style={styles.closeButton}
              >
                <MaterialIcons name="close" size={24} color="#64748b" />
              </TouchableOpacity>
            </View>

            <ScrollView 
              style={styles.reportFormScrollable}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.reportFormContent}
            >
              {/* Report Format Selection */}
              <View style={styles.inputContainer}>
                <ThemedText style={styles.inputLabel}>Report Format</ThemedText>
                <View style={styles.formatSelectionContainer}>
                  {[
                    { key: 'csv', label: 'CSV', icon: 'table-chart', desc: 'Excel compatible spreadsheet' },
                    { key: 'pdf', label: 'PDF', icon: 'picture-as-pdf', desc: 'Professional document format' }
                  ].map((format) => (
                    <TouchableOpacity
                      key={format.key}
                      style={[
                        styles.formatOption,
                        {
                          borderColor: colorScheme === 'dark' ? '#4b5563' : '#e2e8f0',
                          backgroundColor: reportFormat === format.key 
                            ? (colorScheme === 'dark' ? '#374151' : '#f0f7ff')
                            : 'transparent'
                        },
                        reportFormat === format.key && styles.selectedFormatOption
                      ]}
                      onPress={() => setReportFormat(format.key)}
                    >
                      <MaterialIcons 
                        name={format.icon as any} 
                        size={24} 
                        color={reportFormat === format.key ? '#f97316' : '#64748b'} 
                      />
                      <View style={styles.formatOptionText}>
                        <ThemedText style={[
                          styles.formatLabel,
                          reportFormat === format.key && { color: '#f97316', fontWeight: '600' }
                        ]}>
                          {format.label}
                        </ThemedText>
                        <ThemedText style={styles.formatDesc}>{format.desc}</ThemedText>
                      </View>
                      {reportFormat === format.key && (
                        <MaterialIcons name="check-circle" size={20} color="#f97316" />
                      )}
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Report Type Selection */}
              <View style={styles.inputContainer}>
                <ThemedText style={styles.inputLabel}>Report Type</ThemedText>
                <View style={styles.reportTypeContainer}>
                  {[
                    { key: 'summary', label: 'Summary Report', desc: 'Daily attendance overview' },
                    { key: 'detailed', label: 'Detailed Report', desc: 'Individual laborer records' },
                    { key: 'payroll', label: 'Payroll Report', desc: 'Wage calculations & overtime' }
                  ].map((type) => (
                    <TouchableOpacity
                      key={type.key}
                      style={[
                        styles.reportTypeOption,
                        {
                          borderColor: colorScheme === 'dark' ? '#4b5563' : '#e2e8f0',
                          backgroundColor: reportType === type.key 
                            ? (colorScheme === 'dark' ? '#374151' : '#f8fafc')
                            : 'transparent'
                        },
                        reportType === type.key && styles.selectedReportType
                      ]}
                      onPress={() => setReportType(type.key)}
                    >
                      <View style={styles.reportTypeHeader}>
                        <ThemedText style={[
                          styles.reportTypeLabel,
                          reportType === type.key && { color: '#f97316', fontWeight: '600' }
                        ]}>
                          {type.label}
                        </ThemedText>
                        {reportType === type.key && (
                          <MaterialIcons name="check-circle" size={20} color="#f97316" />
                        )}
                      </View>
                      <ThemedText style={styles.reportTypeDesc}>{type.desc}</ThemedText>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              {/* Date Range Selection */}
              <View style={styles.inputContainer}>
                <ThemedText style={styles.inputLabel}>Date Range</ThemedText>
                
                <TouchableOpacity
                  style={[
                    styles.datePickerButton,
                    {
                      backgroundColor: colorScheme === 'dark' ? '#374151' : '#f8fafc',
                      borderColor: colorScheme === 'dark' ? '#4b5563' : '#e2e8f0',
                    }
                  ]}
                  onPress={() => setShowStartPicker(true)}
                >
                  <MaterialIcons name="calendar-today" size={20} color="#f97316" />
                  <ThemedText style={styles.datePickerText}>
                    From: {formatDate(startDate)}
                  </ThemedText>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.datePickerButton,
                    {
                      backgroundColor: colorScheme === 'dark' ? '#374151' : '#f8fafc',
                      borderColor: colorScheme === 'dark' ? '#4b5563' : '#e2e8f0',
                    }
                  ]}
                  onPress={() => setShowEndPicker(true)}
                >
                  <MaterialIcons name="calendar-today" size={20} color="#f97316" />
                  <ThemedText style={styles.datePickerText}>
                    To: {formatDate(endDate)}
                  </ThemedText>
                </TouchableOpacity>
              </View>

              {/* Quick Date Range Options */}
              <View style={styles.inputContainer}>
                <ThemedText style={styles.inputLabel}>Quick Select</ThemedText>
                <View style={styles.quickDateContainer}>
                  {[
                    { label: 'Last 7 Days', days: 7 },
                    { label: 'Last 15 Days', days: 15 },
                    { label: 'Last 30 Days', days: 30 },
                    { label: 'This Month', days: 'month' }
                  ].map((option) => (
                    <TouchableOpacity
                      key={option.label}
                      style={[
                        styles.quickDateButton,
                        {
                          backgroundColor: colorScheme === 'dark' ? '#374151' : '#f1f5f9',
                          borderColor: colorScheme === 'dark' ? '#4b5563' : '#e2e8f0',
                        }
                      ]}
                      onPress={() => {
                        const end = new Date();
                        let start = new Date();
                        
                        if (option.days === 'month') {
                          start = new Date(end.getFullYear(), end.getMonth(), 1);
                        } else {
                          start.setDate(end.getDate() - (option.days as number));
                        }
                        
                        setStartDate(start);
                        setEndDate(end);
                      }}
                    >
                      <ThemedText style={styles.quickDateText}>{option.label}</ThemedText>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </ScrollView>

            <View style={styles.modalActions}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={onClose}
                disabled={loading}
              >
                <ThemedText style={styles.cancelButtonText}>Cancel</ThemedText>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.modalButton, 
                  styles.confirmButton,
                  loading && { opacity: 0.6 }
                ]}
                onPress={handleGenerateReport}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator size="small" color="#ffffff" />
                ) : (
                  <View style={styles.generateButtonContent}>
                    <MaterialIcons 
                      name={reportFormat === 'pdf' ? 'picture-as-pdf' : 'file-download'} 
                      size={20} 
                      color="#ffffff" 
                    />
                    <ThemedText style={styles.confirmButtonText}>
                      Generate {reportFormat.toUpperCase()}
                    </ThemedText>
                  </View>
                )}
              </TouchableOpacity>
            </View>

            {/* Date Pickers */}
            {showStartPicker && (
              <DateTimePicker
                value={startDate}
                mode="date"
                display="default"
                onChange={(event, selectedDate) => {
                  setShowStartPicker(false);
                  if (selectedDate) {
                    setStartDate(selectedDate);
                  }
                }}
              />
            )}

            {showEndPicker && (
              <DateTimePicker
                value={endDate}
                mode="date"
                display="default"
                onChange={(event, selectedDate) => {
                  setShowEndPicker(false);
                  if (selectedDate) {
                    setEndDate(selectedDate);
                  }
                }}
              />
            )}
          </View>
        </KeyboardAvoidingView>
      </View>
    </Modal>
  );
});

export default function SubContractorAttendanceScreen() {
  const params = useLocalSearchParams();
  const siteId = typeof params.id === 'string' ? params.id : Array.isArray(params.id) ? params.id[0] : '';
  const contractorId = typeof params.contractorId === 'string' ? params.contractorId : '';
  const contractorName = typeof params.contractorName === 'string' ? decodeURIComponent(params.contractorName) : '';
  const colorScheme = useColorScheme();
  const navigation = useNavigation();
  
  // States
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [subContractor, setSubContractor] = useState<SubContractor | null>(null);
  const [site, setSite] = useState<Site | null>(null);
  const [attendanceSummary, setAttendanceSummary] = useState<SubContractorAttendanceSummary[]>([]);
  const [currentSummary, setCurrentSummary] = useState<SubContractorAttendanceSummary | null>(null);
  const [userRole, setUserRole] = useState<'Super Admin' | 'Admin' | 'Member' | null>(null);
  
  // Laborers management
  const [laborers, setLaborers] = useState<SubContractorLaborer[]>([]);
  const [laborerAttendance, setLaborerAttendance] = useState<Record<string, LaborerAttendanceRecord>>({});
  const [showAddLaborer, setShowAddLaborer] = useState(false);
  
  // Add laborer form states
  const [newLaborerName, setNewLaborerName] = useState('');
  const [newLaborerPhone, setNewLaborerPhone] = useState('');
  const [newLaborerCategory, setNewLaborerCategory] = useState('');
  const [newLaborerRemarks, setNewLaborerRemarks] = useState('');
  const [newLaborerDailyWage, setNewLaborerDailyWage] = useState('');
  
  // OT Modal related states
  const [showOTModal, setShowOTModal] = useState(false);
  const [overtimeHours, setOvertimeHours] = useState('');
  const [selectedLaborerId, setSelectedLaborerId] = useState<string | null>(null);

  // Report related states
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportLoading, setReportLoading] = useState(false);

  const phoneInputRef = useRef<TextInput>(null);
  const categoryInputRef = useRef<TextInput>(null);
  const dailyWageInputRef = useRef<TextInput>(null);
  const remarksInputRef = useRef<TextInput>(null);

  const canEdit = userRole === 'Super Admin' || userRole === 'Admin';
  const canEditPastDates = userRole === 'Super Admin';
  const canEditCurrentDate = userRole === 'Super Admin' || userRole === 'Admin';
  const canAddEditLaborers = userRole === 'Super Admin' || userRole === 'Admin';
  const canViewOnly = userRole === 'Member';

  // Load initial data
  const loadData = async () => {
    if (!siteId || !contractorId) return;
    
    try {
      // Load site info
      const { data: siteData, error: siteError } = await supabase
        .from('sites')
        .select('id, name, organization_name')
        .eq('id', siteId)
        .single();

      if (siteError) {
        console.error('Error loading site:', siteError);
        Alert.alert('Error', 'Failed to load site information');
        return;
      }
      setSite(siteData);

      // Load sub-contractor info
      const { data: contractorData, error: contractorError } = await supabase
        .from('sub_contractors')
        .select('*')
        .eq('id', contractorId)
        .single();

      if (contractorError) {
        console.error('Error loading sub-contractor:', contractorError);
        Alert.alert('Error', 'Failed to load sub-contractor information');
        return;
      }
      setSubContractor(contractorData);

      // Load user role
      await fetchUserRole();

      // Load attendance summary
      await fetchAttendanceSummary();

      // Load laborers
      await fetchLaborers();

      // Load laborer attendance for the selected date
      await fetchLaborerAttendanceForDate();

    } catch (error) {
      console.error('Error loading data:', error);
      Alert.alert('Error', 'Failed to load data. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Fetch user role for permission checking
  const fetchUserRole = async () => {
    try {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) return;

      const { data: memberData, error } = await supabase
        .from('site_members')
        .select('role')
        .eq('site_id', siteId)
        .eq('user_id', userData.user.id)
        .single();

      if (error) {
        console.error('Error fetching user role:', error);
        return;
      }

      setUserRole(memberData?.role || 'Member');
    } catch (error) {
      console.error('Error in fetchUserRole:', error);
    }
  };

  // Fetch attendance summary for the sub-contractor
  const fetchAttendanceSummary = async () => {
    if (!contractorId) return;

    try {
      const { data: summaryData, error } = await supabase
        .from('sub_contractor_attendance_summary')
        .select('*')
        .eq('sub_contractor_id', contractorId)
        .order('attendance_date', { ascending: false })
        .limit(30); // Last 30 days

      if (error) {
        console.error('Error loading attendance summary:', error);
        return;
      }

      setAttendanceSummary(summaryData || []);
      
      // Find current date summary
      const todaySummary = summaryData?.find(
        record => record.attendance_date === selectedDate
      );
      setCurrentSummary(todaySummary || null);

    } catch (error) {
      console.error('Error in fetchAttendanceSummary:', error);
    }
  };

  // Fetch attendance summary for selected date
  const fetchAttendanceForDate = async () => {
    if (!contractorId) return;

    try {
      const { data: summaryData, error } = await supabase
        .from('sub_contractor_attendance_summary')
        .select('*')
        .eq('sub_contractor_id', contractorId)
        .eq('attendance_date', selectedDate)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
        console.error('Error loading attendance summary for date:', error);
        return;
      }

      setCurrentSummary(summaryData || null);

    } catch (error) {
      console.error('Error in fetchAttendanceForDate:', error);
    }
  };

  useEffect(() => {
    loadData();
  }, [siteId, contractorId]);

  useEffect(() => {
    if (!loading && contractorId) {
      fetchAttendanceForDate();
      fetchLaborerAttendanceForDate();
      fetchLaborers(); // Refetch laborers based on selected date
    }
  }, [selectedDate, contractorId]);

  // Set initial title from URL params, then update when data loads
  useEffect(() => {
    // Set initial title from URL params (contractorName is already decoded)
    navigation.setOptions({
      title: contractorName || 'Sub-Contractor Attendance',
    });
  }, [navigation, contractorName]);

  // Update header title when subContractor data loads (if different from URL param)
  useEffect(() => {
    if (subContractor?.name && subContractor.name !== contractorName) {
      navigation.setOptions({
        title: subContractor.name,
      });
    }
  }, [subContractor?.name, navigation, contractorName]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadData();
  }, []);

  // Memoized callbacks for the modal
  const handleCloseModal = useCallback(() => {
    setShowAddLaborer(false);
  }, []);

  const memoizedSetNewLaborerName = useCallback((value: string) => {
    setNewLaborerName(value);
  }, []);

  const memoizedSetNewLaborerPhone = useCallback((value: string) => {
    setNewLaborerPhone(value);
  }, []);

  const memoizedSetNewLaborerCategory = useCallback((value: string) => {
    setNewLaborerCategory(value);
  }, []);

  const memoizedSetNewLaborerDailyWage = useCallback((value: string) => {
    setNewLaborerDailyWage(value);
  }, []);

  const memoizedSetNewLaborerRemarks = useCallback((value: string) => {
    setNewLaborerRemarks(value);
  }, []);

  // Fetch laborers for the sub-contractor based on selected date
  const fetchLaborers = async () => {
    if (!contractorId) return;

    try {
      // For current and future dates, show only active laborers
      // For past dates, show laborers who were active on that date (including those later deactivated)
      const selectedDateObj = new Date(selectedDate);
      const currentDateObj = new Date();
      const isPastDate = selectedDateObj < currentDateObj;
      
      let query = supabase
        .from('sub_contractor_laborers')
        .select('*')
        .eq('sub_contractor_id', contractorId);
      
      if (isPastDate) {
        // For past dates: show laborers who were created on or before the selected date
        // This includes both active and inactive laborers (to preserve historical data)
        query = query.lte('created_at', selectedDate + 'T23:59:59.999Z');
      } else {
        // For current and future dates: show only currently active laborers
        query = query.eq('is_active', true);
      }
      
      const { data: laborersData, error } = await query.order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading laborers:', error);
        return;
      }

      setLaborers(laborersData || []);
    } catch (error) {
      console.error('Error in fetchLaborers:', error);
    }
  };

  // Fetch laborer attendance for selected date
  const fetchLaborerAttendanceForDate = async () => {
    if (!contractorId) return;

    try {
      const { data: attendanceData, error } = await supabase
        .from('sub_contractor_laborer_attendance')
        .select('*')
        .eq('sub_contractor_id', contractorId)
        .eq('attendance_date', selectedDate);

      if (error) {
        console.error('Error loading laborer attendance:', error);
        return;
      }

      let attendanceMap: Record<string, LaborerAttendanceRecord> = {};
      
      if (attendanceData && attendanceData.length > 0) {
        attendanceData.forEach(record => {
          attendanceMap[record.laborer_id] = record;
        });
      }
      
      setLaborerAttendance(attendanceMap);
    } catch (error) {
      console.error('Error in fetchLaborerAttendanceForDate:', error);
    }
  };

  // Handle OT confirmation
  const handleOTConfirm = async () => {
    if (!overtimeHours || !contractorId || !siteId) {
      Alert.alert('Error', 'Please enter overtime hours');
      return;
    }
    
    // Check permissions based on date
    const selectedDateObj = new Date(selectedDate);
    const currentDateObj = new Date();
    const isCurrentDate = selectedDateObj.toDateString() === currentDateObj.toDateString();
    const isPastDate = selectedDateObj < currentDateObj;
    
    if (canViewOnly) {
      Alert.alert('Permission Denied', 'You can only view attendance records.');
      return;
    }
    
    if (isPastDate && !canEditPastDates) {
      Alert.alert('Permission Denied', 'You can only modify current date attendance. Contact Super Admin to modify past records.');
      return;
    }
    
    const hours = parseFloat(overtimeHours);
    if (isNaN(hours) || hours <= 0) {
      Alert.alert('Error', 'Please enter a valid number of hours');
      return;
    }
    
    try {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) return;
      
      if (selectedLaborerId) {
        // Handle laborer overtime
        const existingRecord = laborerAttendance[selectedLaborerId];
        
        if (existingRecord) {
          const { error } = await supabase
            .from('sub_contractor_laborer_attendance')
            .update({
              status: 'overtime',
              overtime_hours: hours,
              updated_at: new Date().toISOString(),
              marked_by: userData.user.id
            })
            .eq('id', existingRecord.id);
            
          if (error) {
            console.error('Error updating laborer attendance with OT:', error);
            Alert.alert('Error', 'Failed to update laborer overtime status');
            return;
          }
        } else {
          const { error } = await supabase
            .from('sub_contractor_laborer_attendance')
            .insert({
              laborer_id: selectedLaborerId,
              sub_contractor_id: contractorId,
              site_id: siteId,
              attendance_date: selectedDate,
              status: 'overtime',
              overtime_hours: hours,
              marked_by: userData.user.id
            });
            
          if (error) {
            console.error('Error creating laborer attendance record with OT:', error);
            Alert.alert('Error', 'Failed to create laborer overtime record');
            return;
          }
        }
        
        await fetchLaborerAttendanceForDate();
        await fetchAttendanceForDate(); // Refresh summary
      }
      
      // Close modal and reset states
      setShowOTModal(false);
      setSelectedLaborerId(null);
      
    } catch (error) {
      console.error('Error marking overtime attendance:', error);
      Alert.alert('Error', 'Failed to update overtime attendance');
    }
  };

  // Handle laborer attendance change
  const handleLaborerAttendanceChange = async (laborerId: string, status: string) => {
    if (!contractorId || !siteId) return;
    
    // Check permissions based on date
    const selectedDateObj = new Date(selectedDate);
    const currentDateObj = new Date();
    const isCurrentDate = selectedDateObj.toDateString() === currentDateObj.toDateString();
    const isPastDate = selectedDateObj < currentDateObj;
    
    if (canViewOnly) {
      Alert.alert('Permission Denied', 'You can only view attendance records.');
      return;
    }
    
    if (isPastDate && !canEditPastDates) {
      Alert.alert('Permission Denied', 'You can only modify current date attendance. Contact Super Admin to modify past records.');
      return;
    }
    
    if (!isCurrentDate && !canEditPastDates && !canEditCurrentDate) {
      Alert.alert('Permission Denied', 'You don\'t have permission to modify attendance for this date.');
      return;
    }
    
    // Show OT Modal if status is overtime
    if (status === 'overtime') {
      setSelectedLaborerId(laborerId);
      setOvertimeHours('');
      setShowOTModal(true);
      return;
    }
    
    try {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) return;
      
      const existingRecord = laborerAttendance[laborerId];
      
      if (existingRecord) {
        // Update existing record
        const { error } = await supabase
          .from('sub_contractor_laborer_attendance')
          .update({
            status,
            updated_at: new Date().toISOString(),
            marked_by: userData.user.id,
            overtime_hours: null
          })
          .eq('id', existingRecord.id);
        
        if (error) {
          console.error('Error updating laborer attendance:', error);
          Alert.alert('Error', 'Failed to update laborer attendance status');
          return;
        }
      } else {
        // Create new record
        const { error } = await supabase
          .from('sub_contractor_laborer_attendance')
          .insert({
            laborer_id: laborerId,
            sub_contractor_id: contractorId,
            site_id: siteId,
            attendance_date: selectedDate,
            status,
            marked_by: userData.user.id
          });
        
        if (error) {
          console.error('Error creating laborer attendance record:', error);
          Alert.alert('Error', 'Failed to create laborer attendance record');
          return;
        }
      }
      
      // Refresh data
      await fetchLaborerAttendanceForDate();
      
    } catch (error) {
      console.error('Error marking laborer attendance:', error);
      Alert.alert('Error', 'Failed to update laborer attendance');
    }
  };

  // Add new laborer
  const handleAddLaborer = async () => {
    if (!newLaborerName.trim()) {
      Alert.alert('Error', 'Please enter laborer name');
      return;
    }

    if (!canAddEditLaborers) {
      Alert.alert('Permission Denied', 'You don\'t have permission to add laborers. Only Admins and Super Admins can add laborers.');
      return;
    }

    try {
      const { data, error } = await supabase
        .from('sub_contractor_laborers')
        .insert([
          {
            full_name: newLaborerName.trim(),
            phone_number: newLaborerPhone.trim() || null,
            sub_contractor_id: contractorId,
            site_id: siteId,
            category: newLaborerCategory.trim() || null,
            remarks: newLaborerRemarks.trim() || null,
            daily_wage: newLaborerDailyWage ? parseFloat(newLaborerDailyWage) : null,
            is_active: true
          }
        ])
        .select()
        .single();

      if (error) {
        console.error('Error adding laborer:', error);
        Alert.alert('Error', 'Failed to add laborer');
        return;
      }

      // Reset form and close modal
      setNewLaborerName('');
      setNewLaborerPhone('');
      setNewLaborerCategory('');
      setNewLaborerRemarks('');
      setNewLaborerDailyWage('');
      setShowAddLaborer(false);

      // Refresh laborers list
      await fetchLaborers();
      
      Alert.alert('Success', 'Laborer added successfully');
    } catch (error) {
      console.error('Error in handleAddLaborer:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    }
  };

  // Delete laborer (soft delete - keeps past attendance records)
  const handleDeleteLaborer = async (laborerId: string) => {
    if (!canAddEditLaborers) {
      Alert.alert('Permission Denied', 'You don\'t have permission to delete laborers. Only Admins and Super Admins can delete laborers.');
      return;
    }

    Alert.alert(
      'Remove Laborer',
      'Are you sure you want to remove this laborer? They will no longer appear in future attendance marking, but their past attendance records will be preserved for reporting purposes.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('sub_contractor_laborers')
                .update({ 
                  is_active: false,
                  updated_at: new Date().toISOString()
                })
                .eq('id', laborerId);

              if (error) {
                console.error('Error removing laborer:', error);
                Alert.alert('Error', 'Failed to remove laborer');
                return;
              }

              await fetchLaborers();
              Alert.alert('Success', 'Laborer removed successfully. Their past attendance records have been preserved.');
            } catch (error) {
              console.error('Error in handleDeleteLaborer:', error);
              Alert.alert('Error', 'An unexpected error occurred');
            }
          }
        }
      ]
    );
  };

  // Get status color
  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'present': return '#10b981';
      case 'absent': return '#ef4444';
      case 'half_day': return '#f59e0b';
      case 'overtime': return '#f97316';
      default: return '#64748b';
    }
  };

  // Get status text
  const getStatusText = (status?: string) => {
    switch (status) {
      case 'present': return 'Present';
      case 'absent': return 'Absent';
      case 'half_day': return 'Half Day';
      case 'overtime': return 'Overtime';
      default: return 'Not Marked';
    }
  };

  // OT Modal component
  const OTModal = () => (
    <Modal
      visible={showOTModal}
      transparent
      animationType="slide"
      onRequestClose={() => setShowOTModal(false)}
    >
      <View style={styles.modalContainer}>
        <View style={[
          styles.modalContent,
          { backgroundColor: colorScheme === 'dark' ? '#1f2937' : '#ffffff' }
        ]}>
          <View style={styles.modalHeader}>
            <ThemedText style={styles.modalTitle}>Set Overtime Hours</ThemedText>
            <TouchableOpacity onPress={() => setShowOTModal(false)}>
              <MaterialIcons name="close" size={24} color="#64748b" />
            </TouchableOpacity>
          </View>

                     <View style={styles.modalBody}>
             <ThemedText style={styles.inputLabel}>
               Overtime Hours for {laborers.find(l => l.id === selectedLaborerId)?.full_name || 'Laborer'}
             </ThemedText>
            <TextInput
              style={[
                styles.otInput,
                {
                  backgroundColor: colorScheme === 'dark' ? '#374151' : '#f8fafc',
                  color: colorScheme === 'dark' ? '#ffffff' : '#1f2937'
                }
              ]}
              value={overtimeHours}
              onChangeText={setOvertimeHours}
              placeholder="Enter hours (e.g., 2.5)"
              placeholderTextColor="#94a3b8"
              keyboardType="numeric"
              autoFocus
            />
          </View>

          <View style={styles.modalActions}>
            <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton]}
              onPress={() => setShowOTModal(false)}
            >
              <ThemedText style={styles.cancelButtonText}>Cancel</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.modalButton, styles.confirmButton]}
              onPress={handleOTConfirm}
            >
              <ThemedText style={styles.confirmButtonText}>Confirm</ThemedText>
            </TouchableOpacity>
          </View>
        </View>
      </View>
         </Modal>
   );



  // Render attendance summary
  const renderAttendanceSummary = () => {
    const summary = currentSummary;
    
    return (
      <View style={styles.statusButtonsContainer}>
        <ThemedText style={styles.sectionTitle}>
          Sub-Contractor Summary - {new Date(selectedDate).toLocaleDateString()}
        </ThemedText>
        
        {summary ? (
          <View style={styles.summaryContainer}>
            <View style={styles.summaryRow}>
              <View style={styles.summaryItem}>
                <ThemedText style={[styles.summaryCount, { color: getStatusColor('present') }]}>
                  {summary.present_count}
                </ThemedText>
                <ThemedText style={styles.summaryLabel}>Present</ThemedText>
              </View>
              
              <View style={styles.summaryItem}>
                <ThemedText style={[styles.summaryCount, { color: getStatusColor('absent') }]}>
                  {summary.absent_count}
                </ThemedText>
                <ThemedText style={styles.summaryLabel}>Absent</ThemedText>
              </View>
              
              <View style={styles.summaryItem}>
                <ThemedText style={[styles.summaryCount, { color: getStatusColor('half_day') }]}>
                  {summary.half_day_count}
                </ThemedText>
                <ThemedText style={styles.summaryLabel}>Half Day</ThemedText>
              </View>
              
              <View style={styles.summaryItem}>
                <ThemedText style={[styles.summaryCount, { color: getStatusColor('overtime') }]}>
                  {summary.overtime_count}
                </ThemedText>
                <ThemedText style={styles.summaryLabel}>Overtime</ThemedText>
              </View>
            </View>
            
            <View style={styles.summaryFooter}>
              <ThemedText style={styles.summaryFooterText}>
                {summary.marked_laborers} of {summary.total_laborers} laborers marked
              </ThemedText>
              {summary.total_overtime_hours > 0 && (
                <ThemedText style={[styles.summaryFooterText, { color: getStatusColor('overtime') }]}>
                  Total OT: {summary.total_overtime_hours}h
                </ThemedText>
              )}
            </View>
          </View>
        ) : (
          <View style={styles.noDataContainer}>
            <ThemedText style={styles.noDataText}>
              {laborers.length === 0 ? 
                'No laborers added yet' : 
                'No attendance marked for this date'}
            </ThemedText>
          </View>
        )}
      </View>
    );
  };

  // Render attendance history
  const renderAttendanceHistory = () => {
    if (attendanceSummary.length === 0) {
      return (
        <View style={styles.emptyHistoryContainer}>
          <MaterialIcons name="history" size={48} color="#94a3b8" />
          <ThemedText style={styles.emptyHistoryText}>No attendance history yet</ThemedText>
        </View>
      );
    }

    return (
      <View style={styles.historyContainer}>
        <ThemedText style={styles.sectionTitle}>Attendance History</ThemedText>
        <View style={styles.historyList}>
          {attendanceSummary.map((item) => (
            <View key={item.attendance_date} style={[
              styles.historyItem,
              { backgroundColor: colorScheme === 'dark' ? '#1f2937' : '#ffffff' }
            ]}>
              <View style={styles.historyDateContainer}>
                <ThemedText style={styles.historyDate}>
                  {new Date(item.attendance_date).toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                  })}
                </ThemedText>
              </View>
              
              <View style={styles.historySummaryContainer}>
                <View style={styles.historySummaryRow}>
                  <View style={styles.historySummaryItem}>
                    <ThemedText style={[styles.historySummaryCount, { color: getStatusColor('present') }]}>
                      {item.present_count}
                    </ThemedText>
                    <ThemedText style={styles.historySummaryLabel}>P</ThemedText>
                  </View>
                  <View style={styles.historySummaryItem}>
                    <ThemedText style={[styles.historySummaryCount, { color: getStatusColor('absent') }]}>
                      {item.absent_count}
                    </ThemedText>
                    <ThemedText style={styles.historySummaryLabel}>A</ThemedText>
                  </View>
                  <View style={styles.historySummaryItem}>
                    <ThemedText style={[styles.historySummaryCount, { color: getStatusColor('half_day') }]}>
                      {item.half_day_count}
                    </ThemedText>
                    <ThemedText style={styles.historySummaryLabel}>H</ThemedText>
                  </View>
                  <View style={styles.historySummaryItem}>
                    <ThemedText style={[styles.historySummaryCount, { color: getStatusColor('overtime') }]}>
                      {item.overtime_count}
                    </ThemedText>
                    <ThemedText style={styles.historySummaryLabel}>OT</ThemedText>
                  </View>
                </View>
                <ThemedText style={styles.historyTotalText}>
                  {item.marked_laborers}/{item.total_laborers} laborers
                  {item.total_overtime_hours > 0 && ` • ${item.total_overtime_hours}h OT`}
                </ThemedText>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  // Render laborers section
  const renderLaborersSection = () => {
    const LaborerItem = ({ laborer }: { laborer: SubContractorLaborer }) => {
      const attendanceStatus = laborerAttendance[laborer.id]?.status;
      const overtimeHours = laborerAttendance[laborer.id]?.overtime_hours;
      
      return (
        <View style={[
          styles.laborerItem,
          { backgroundColor: colorScheme === 'dark' ? '#1f2937' : '#ffffff' }
        ]}>
          <View style={styles.laborerInfo}>
            <View style={styles.laborerIconContainer}>
              <MaterialIcons name="person" size={20} color="#f97316" />
            </View>
            <View style={styles.laborerDetails}>
              <ThemedText style={styles.laborerName}>{laborer.full_name}</ThemedText>
              {laborer.category && (
                <ThemedText style={styles.laborerCategory}>{laborer.category}</ThemedText>
              )}
              {laborer.daily_wage && (
                <ThemedText style={styles.laborerWage}>₹{laborer.daily_wage}/day</ThemedText>
              )}
            </View>
            
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={() => handleDeleteLaborer(laborer.id)}
              disabled={!canAddEditLaborers}
            >
              <MaterialIcons name="delete" size={18} color={canAddEditLaborers ? "#ef4444" : "#94a3b8"} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.laborerAttendanceButtons}>
            {['present', 'absent', 'half_day', 'overtime'].map((status) => {
              const isSelected = attendanceStatus === status;
              const color = getStatusColor(status);
              
              // Check if the user can edit attendance for this date
              const selectedDateObj = new Date(selectedDate);
              const currentDateObj = new Date();
              const isCurrentDate = selectedDateObj.toDateString() === currentDateObj.toDateString();
              const isPastDate = selectedDateObj < currentDateObj;
              
              const canEditThisDate = canViewOnly ? false : 
                (isPastDate ? canEditPastDates : (isCurrentDate ? canEditCurrentDate : canEditCurrentDate));
              
              return (
                <TouchableOpacity
                  key={status}
                  style={[
                    styles.laborerStatusButton,
                    isSelected && { backgroundColor: `${color}15`, borderColor: color },
                    !canEditThisDate && styles.disabledButton
                  ]}
                  onPress={() => handleLaborerAttendanceChange(laborer.id, status)}
                  disabled={!canEditThisDate}
                >
                  <ThemedText 
                    style={[
                      styles.laborerStatusText,
                      isSelected && { color: color, fontWeight: '600' },
                      !canEditThisDate && { color: '#94a3b8' }
                    ]}
                  >
                    {status === 'present' ? 'P' : 
                     status === 'absent' ? 'A' : 
                     status === 'half_day' ? 'H' : 'OT'}
                  </ThemedText>
                  {status === 'overtime' && overtimeHours && (
                    <ThemedText style={[styles.laborerOTHours, { color: color }]}>
                      {overtimeHours}h
                    </ThemedText>
                  )}
                </TouchableOpacity>
              );
            })}
          </View>
        </View>
      );
    };

    if (laborers.length === 0) {
      return (
        <View style={styles.emptyLaborersContainer}>
          <MaterialIcons name="groups" size={48} color="#94a3b8" />
          <ThemedText style={styles.emptyLaborersText}>No laborers added yet</ThemedText>
          <ThemedText style={styles.emptyLaborersSubtext}>
            Add laborers to track individual attendance
          </ThemedText>
        </View>
      );
    }

    return (
      <View style={styles.laborersContainer}>
        <ThemedText style={styles.sectionTitle}>
          Laborers ({laborers.length})
        </ThemedText>
        {laborers.map((laborer) => (
          <LaborerItem key={laborer.id} laborer={laborer} />
        ))}
      </View>
    );
  };

  // Generate report functionality
  const generateReport = async (startDate: string, endDate: string, reportType: string, format: string) => {
    if (!contractorId || !siteId || !subContractor) return;

    setReportLoading(true);

    try {
      // Fetch data based on report type
      let reportData;
      
      if (reportType === 'summary') {
        reportData = await generateSummaryReport(startDate, endDate);
      } else if (reportType === 'detailed') {
        reportData = await generateDetailedReport(startDate, endDate);
      } else if (reportType === 'payroll') {
        reportData = await generatePayrollReport(startDate, endDate);
      }

      if (reportData) {
        await downloadAndShareReport(reportData, reportType, startDate, endDate, format);
      }

    } catch (error) {
      console.error('Error generating report:', error);
      Alert.alert('Error', 'Failed to generate report. Please try again.');
    } finally {
      setReportLoading(false);
      setShowReportModal(false);
    }
  };

  // Generate summary report
  const generateSummaryReport = async (startDate: string, endDate: string) => {
    try {
      const { data: summaryData, error } = await supabase
        .from('sub_contractor_attendance_summary')
        .select('*')
        .eq('sub_contractor_id', contractorId)
        .gte('attendance_date', startDate)
        .lte('attendance_date', endDate)
        .order('attendance_date', { ascending: true });

      if (error) throw error;

      return {
        type: 'summary',
        data: summaryData || [],
        contractor: subContractor,
        site: site,
        dateRange: { startDate, endDate }
      };
    } catch (error) {
      console.error('Error generating summary report:', error);
      throw error;
    }
  };

  // Generate detailed report
  const generateDetailedReport = async (startDate: string, endDate: string) => {
    try {
      const { data: attendanceData, error: attendanceError } = await supabase
        .from('sub_contractor_laborer_attendance')
        .select(`
          *,
          sub_contractor_laborers!inner(
            id,
            full_name,
            category,
            daily_wage,
            phone_number
          )
        `)
        .eq('sub_contractor_id', contractorId)
        .gte('attendance_date', startDate)
        .lte('attendance_date', endDate)
        .order('attendance_date', { ascending: true });

      if (attendanceError) throw attendanceError;

      return {
        type: 'detailed',
        data: attendanceData || [],
        contractor: subContractor,
        site: site,
        dateRange: { startDate, endDate }
      };
    } catch (error) {
      console.error('Error generating detailed report:', error);
      throw error;
    }
  };

  // Generate payroll report
  const generatePayrollReport = async (startDate: string, endDate: string) => {
    try {
      const { data: attendanceData, error } = await supabase
        .from('sub_contractor_laborer_attendance')
        .select(`
          *,
          sub_contractor_laborers!inner(
            id,
            full_name,
            category,
            daily_wage,
            phone_number
          )
        `)
        .eq('sub_contractor_id', contractorId)
        .gte('attendance_date', startDate)
        .lte('attendance_date', endDate)
        .order('attendance_date', { ascending: true });

      if (error) throw error;

      // Calculate payroll data
      const payrollData = calculatePayrollData(attendanceData || []);

      return {
        type: 'payroll',
        data: payrollData,
        contractor: subContractor,
        site: site,
        dateRange: { startDate, endDate }
      };
    } catch (error) {
      console.error('Error generating payroll report:', error);
      throw error;
    }
  };

  // Calculate payroll data
  const calculatePayrollData = (attendanceData: any[]) => {
    const laborerPayroll: Record<string, any> = {};

    attendanceData.forEach(record => {
      const laborerId = record.laborer_id;
      const laborer = record.sub_contractor_laborers;
      const dailyWage = laborer.daily_wage || 0;

      if (!laborerPayroll[laborerId]) {
        laborerPayroll[laborerId] = {
          laborer: laborer,
          presentDays: 0,
          halfDays: 0,
          overtimeDays: 0,
          totalOvertimeHours: 0,
          absentDays: 0,
          totalWage: 0,
          overtimeWage: 0,
          finalAmount: 0
        };
      }

      const payrollRecord = laborerPayroll[laborerId];

      switch (record.status) {
        case 'present':
          payrollRecord.presentDays++;
          payrollRecord.totalWage += dailyWage;
          break;
        case 'half_day':
          payrollRecord.halfDays++;
          payrollRecord.totalWage += dailyWage * 0.5;
          break;
        case 'overtime':
          payrollRecord.overtimeDays++;
          payrollRecord.totalOvertimeHours += record.overtime_hours || 0;
          payrollRecord.totalWage += dailyWage;
          // Assuming overtime rate is 1.5x of hourly wage (daily wage / 8 hours)
          const hourlyWage = dailyWage / 8;
          payrollRecord.overtimeWage += (record.overtime_hours || 0) * hourlyWage * 1.5;
          break;
        case 'absent':
          payrollRecord.absentDays++;
          break;
      }

      payrollRecord.finalAmount = payrollRecord.totalWage + payrollRecord.overtimeWage;
    });

    return Object.values(laborerPayroll);
  };

  // Download and share report
  const downloadAndShareReport = async (reportData: any, reportType: string, startDate: string, endDate: string, format: string) => {
    try {
      let fileUri: string;
      let fileName: string;

      if (format === 'pdf') {
        // Generate PDF
        const htmlContent = generateHTMLContent(reportData, reportType);
        const { uri } = await Print.printToFileAsync({
          html: htmlContent,
          base64: false
        });
        fileUri = uri;
        fileName = `${subContractor?.name}_${reportType}_report_${startDate}_to_${endDate}.pdf`;
        
        // Copy to documents directory with proper name
        const documentsUri = `${FileSystem.documentDirectory}${fileName}`;
        await FileSystem.copyAsync({
          from: uri,
          to: documentsUri
        });
        fileUri = documentsUri;
      } else {
        // Generate CSV
        const csvContent = generateCSVContent(reportData, reportType);
        fileName = `${subContractor?.name}_${reportType}_report_${startDate}_to_${endDate}.csv`;
        fileUri = `${FileSystem.documentDirectory}${fileName}`;
        
        await FileSystem.writeAsStringAsync(fileUri, csvContent, {
          encoding: FileSystem.EncodingType.UTF8,
        });
      }

      // Show options to user
      Alert.alert(
        'Report Generated',
        `Your ${format.toUpperCase()} report has been generated successfully. What would you like to do?`,
        [
          {
            text: 'Share',
            onPress: async () => {
              if (await Sharing.isAvailableAsync()) {
                await Sharing.shareAsync(fileUri, {
                  mimeType: format === 'pdf' ? 'application/pdf' : 'text/csv',
                  dialogTitle: `Share ${reportType} Report`,
                });
              } else {
                Alert.alert('Error', 'Sharing is not available on this device');
              }
            }
          },
          {
            text: 'Save to Files',
            onPress: () => {
              Alert.alert('Success', `Report saved as ${fileName} in your documents folder`);
            }
          }
        ]
      );

    } catch (error) {
      console.error('Error saving/sharing report:', error);
      Alert.alert('Error', 'Failed to save or share the report');
    }
  };

  // Generate HTML content for PDF reports
  const generateHTMLContent = (reportData: any, reportType: string) => {
    const { data, contractor, site, dateRange } = reportData;
    const { startDate, endDate } = dateRange;

    let tableRows = '';
    let tableHeaders = '';

    if (reportType === 'summary') {
      tableHeaders = `
        <tr style="background-color: #f8fafc;">
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: left;">Date</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: center;">Present</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: center;">Absent</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: center;">Half Day</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: center;">Overtime</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: center;">OT Hours</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: center;">Total</th>
        </tr>
      `;
      
      data.forEach((record: any) => {
        tableRows += `
          <tr>
            <td style="padding: 8px; border: 1px solid #e2e8f0;">${new Date(record.attendance_date).toLocaleDateString()}</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0; text-align: center; color: #10b981;">${record.present_count}</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0; text-align: center; color: #ef4444;">${record.absent_count}</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0; text-align: center; color: #f59e0b;">${record.half_day_count}</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0; text-align: center; color: #f97316;">${record.overtime_count}</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0; text-align: center;">${record.total_overtime_hours}h</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0; text-align: center; font-weight: bold;">${record.marked_laborers}/${record.total_laborers}</td>
          </tr>
        `;
      });
    } else if (reportType === 'detailed') {
      tableHeaders = `
        <tr style="background-color: #f8fafc;">
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: left;">Date</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: left;">Laborer</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: left;">Category</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: center;">Status</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: center;">OT Hours</th>
        </tr>
      `;
      
      data.forEach((record: any) => {
        const statusColor = record.status === 'present' ? '#10b981' : 
                           record.status === 'absent' ? '#ef4444' : 
                           record.status === 'half_day' ? '#f59e0b' : '#f97316';
        
        tableRows += `
          <tr>
            <td style="padding: 8px; border: 1px solid #e2e8f0;">${new Date(record.attendance_date).toLocaleDateString()}</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0;">${record.sub_contractor_laborers.full_name}</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0;">${record.sub_contractor_laborers.category || 'N/A'}</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0; text-align: center; color: ${statusColor}; font-weight: bold;">${record.status.replace('_', ' ').toUpperCase()}</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0; text-align: center;">${record.overtime_hours || 0}h</td>
          </tr>
        `;
      });
    } else if (reportType === 'payroll') {
      tableHeaders = `
        <tr style="background-color: #f8fafc;">
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: left;">Laborer</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: center;">Present</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: center;">Half Days</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: center;">OT Days</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: center;">OT Hours</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: right;">Daily Wage</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: right;">Total Wage</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: right;">OT Amount</th>
          <th style="padding: 12px; border: 1px solid #e2e8f0; text-align: right; background-color: #f0f7ff;">Final Amount</th>
        </tr>
      `;
      
      data.forEach((record: any) => {
        tableRows += `
          <tr>
            <td style="padding: 8px; border: 1px solid #e2e8f0;">${record.laborer.full_name}</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0; text-align: center;">${record.presentDays}</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0; text-align: center;">${record.halfDays}</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0; text-align: center;">${record.overtimeDays}</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0; text-align: center;">${record.totalOvertimeHours}h</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0; text-align: right;">₹${record.laborer.daily_wage || 0}</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0; text-align: right;">₹${record.totalWage.toFixed(2)}</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0; text-align: right;">₹${record.overtimeWage.toFixed(2)}</td>
            <td style="padding: 8px; border: 1px solid #e2e8f0; text-align: right; font-weight: bold; background-color: #f0f7ff;">₹${record.finalAmount.toFixed(2)}</td>
          </tr>
        `;
      });
    }

    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>${reportType.toUpperCase()} Report</title>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              margin: 20px;
              color: #1f2937;
              line-height: 1.5;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              padding-bottom: 20px;
              border-bottom: 2px solid #f97316;
            }
                          .company-name {
                font-size: 24px;
                font-weight: bold;
                color: #f97316;
                margin-bottom: 5px;
              }
            .report-title {
              font-size: 20px;
              font-weight: 600;
              margin-bottom: 10px;
            }
            .report-info {
              display: flex;
              justify-content: space-between;
              margin-bottom: 20px;
              padding: 15px;
              background-color: #f8fafc;
              border-radius: 8px;
                              border-left: 4px solid #f97316;
            }
            .info-section {
              flex: 1;
            }
            .info-label {
              font-weight: 600;
              color: #6b7280;
              font-size: 14px;
            }
            .info-value {
              font-weight: 500;
              color: #1f2937;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 20px;
              font-size: 14px;
            }
            th {
              font-weight: 600;
              color: #374151;
            }
            .footer {
              margin-top: 30px;
              padding-top: 20px;
              border-top: 1px solid #e5e7eb;
              text-align: center;
              color: #6b7280;
              font-size: 12px;
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="company-name">${site?.organization_name || 'Construction Management'}</div>
            <div class="report-title">${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report</div>
          </div>
          
          <div class="report-info">
            <div class="info-section">
              <div class="info-label">Site</div>
              <div class="info-value">${site?.name || 'N/A'}</div>
            </div>
            <div class="info-section">
              <div class="info-label">Sub-Contractor</div>
              <div class="info-value">${contractor?.name || 'N/A'}</div>
            </div>
            <div class="info-section">
              <div class="info-label">Date Range</div>
              <div class="info-value">${new Date(startDate).toLocaleDateString()} - ${new Date(endDate).toLocaleDateString()}</div>
            </div>
            <div class="info-section">
              <div class="info-label">Generated</div>
              <div class="info-value">${new Date().toLocaleString()}</div>
            </div>
          </div>

          <table>
            ${tableHeaders}
            ${tableRows}
          </table>

          <div class="footer">
            Generated by Infratask Construction Management System
          </div>
        </body>
      </html>
    `;
  };

  // Generate CSV content based on report type
  const generateCSVContent = (reportData: any, reportType: string) => {
    const { data, contractor, site, dateRange } = reportData;
    const { startDate, endDate } = dateRange;

    let csvContent = '';
    
    // Header information
    csvContent += `Report Type: ${reportType.toUpperCase()}\n`;
    csvContent += `Site: ${site?.name || 'N/A'}\n`;
    csvContent += `Organization: ${site?.organization_name || 'N/A'}\n`;
    csvContent += `Sub-Contractor: ${contractor?.name || 'N/A'}\n`;
    csvContent += `Date Range: ${startDate} to ${endDate}\n`;
    csvContent += `Generated On: ${new Date().toLocaleString()}\n\n`;

    if (reportType === 'summary') {
      csvContent += 'Date,Present,Absent,Half Day,Overtime,Total OT Hours,Total Laborers,Marked Laborers\n';
      data.forEach((record: any) => {
        csvContent += `${record.attendance_date},${record.present_count},${record.absent_count},${record.half_day_count},${record.overtime_count},${record.total_overtime_hours},${record.total_laborers},${record.marked_laborers}\n`;
      });
    } else if (reportType === 'detailed') {
      csvContent += 'Date,Laborer Name,Category,Phone,Status,Overtime Hours\n';
      data.forEach((record: any) => {
        csvContent += `${record.attendance_date},"${record.sub_contractor_laborers.full_name}","${record.sub_contractor_laborers.category || 'N/A'}","${record.sub_contractor_laborers.phone_number || 'N/A'}",${record.status},${record.overtime_hours || 0}\n`;
      });
    } else if (reportType === 'payroll') {
      csvContent += 'Laborer Name,Category,Phone,Present Days,Half Days,Overtime Days,Total OT Hours,Daily Wage,Total Wage,Overtime Amount,Final Amount\n';
      data.forEach((record: any) => {
        csvContent += `"${record.laborer.full_name}","${record.laborer.category || 'N/A'}","${record.laborer.phone_number || 'N/A'}",${record.presentDays},${record.halfDays},${record.overtimeDays},${record.totalOvertimeHours},${record.laborer.daily_wage || 0},${record.totalWage.toFixed(2)},${record.overtimeWage.toFixed(2)},${record.finalAmount.toFixed(2)}\n`;
      });
    }

    return csvContent;
  };

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#f97316" />
        <ThemedText style={styles.loadingText}>Loading attendance...</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      
      <Stack.Screen
        options={{
          title: subContractor?.name || contractorName || 'Sub-Contractor Attendance',
          headerBackTitle: 'Back',
        }}
      />

      <ScrollView
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={['#f97316']} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Date Selector */}
        <CompactWeekCalendar
          selectedDate={selectedDate}
          onDateChange={setSelectedDate}
        />

        {/* Attendance Summary */}
        {renderAttendanceSummary()}

        {/* Permission Info */}
        {userRole && (
          <View style={styles.permissionInfo}>
            <MaterialIcons name="info" size={16} color="#64748b" />
            <ThemedText style={styles.permissionText}>
              {userRole === 'Member' && 'View only access - Contact Admin to modify attendance'}
              {userRole === 'Admin' && 'You can modify current date attendance and manage laborers'}
              {userRole === 'Super Admin' && 'Full access - You can modify all attendance records'}
            </ThemedText>
          </View>
        )}

        {/* Laborers Section */}
        {renderLaborersSection()}

        {/* Attendance History */}
        {renderAttendanceHistory()}
      </ScrollView>

      {/* Floating Action Buttons */}
      <View style={styles.fabContainer}>
        {/* Download Report FAB */}
        <TouchableOpacity
          style={[styles.fab, styles.reportFab]}
          onPress={() => setShowReportModal(true)}
          activeOpacity={0.8}
        >
          <MaterialIcons name="file-download" size={24} color="#ffffff" />
        </TouchableOpacity>

        {/* Add Laborer FAB */}
        {canAddEditLaborers && (
          <TouchableOpacity
            style={styles.fab}
            onPress={() => setShowAddLaborer(true)}
            activeOpacity={0.8}
          >
            <MaterialIcons name="person-add" size={24} color="#ffffff" />
          </TouchableOpacity>
        )}
      </View>

      {/* OT Modal */}
      <OTModal />
       
      {/* Add Laborer Modal */}
      <AddLaborerModal
        visible={showAddLaborer}
        onClose={handleCloseModal}
        colorScheme={colorScheme}
        newLaborerName={newLaborerName}
        setNewLaborerName={memoizedSetNewLaborerName}
        newLaborerPhone={newLaborerPhone}
        setNewLaborerPhone={memoizedSetNewLaborerPhone}
        newLaborerCategory={newLaborerCategory}
        setNewLaborerCategory={memoizedSetNewLaborerCategory}
        newLaborerDailyWage={newLaborerDailyWage}
        setNewLaborerDailyWage={memoizedSetNewLaborerDailyWage}
        newLaborerRemarks={newLaborerRemarks}
        setNewLaborerRemarks={memoizedSetNewLaborerRemarks}
        onAddLaborer={handleAddLaborer}
        phoneInputRef={phoneInputRef}
        categoryInputRef={categoryInputRef}
        dailyWageInputRef={dailyWageInputRef}
        remarksInputRef={remarksInputRef}
      />

      {/* Report Modal */}
      <ReportModal
        visible={showReportModal}
        onClose={() => setShowReportModal(false)}
        colorScheme={colorScheme}
        onGenerateReport={generateReport}
        loading={reportLoading}
      />
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#64748b',
  },

  // Keyboard Avoiding View
  keyboardAvoidingView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Add Laborer Modal
  addLaborerModalContent: {
    width: '90%',
    maxWidth: 500,
    maxHeight: '80%',
    borderRadius: 16,
    padding: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 10,
  },
  addLaborerForm: {
    maxHeight: 400,
    paddingHorizontal: 20,
  },
  formScrollContent: {
    paddingBottom: 20,
  },
  inputContainer: {
    marginBottom: 16,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    minHeight: 48,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  
  // Calendar styles
  compactCalendarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'rgba(249, 115, 22, 0.05)',
  },
  calendarArrow: {
    padding: 8,
  },
  weekDaysContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
    marginHorizontal: 16,
  },
  dayContainer: {
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    minWidth: 40,
  },
  selectedDayContainer: {
    backgroundColor: '#f97316',
  },
  todayContainer: {
    backgroundColor: 'rgba(249, 115, 22, 0.1)',
  },
  dayNameText: {
    fontSize: 12,
    color: '#64748b',
    marginBottom: 4,
  },
  selectedDayText: {
    color: '#ffffff',
  },
  dayCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedDayCircle: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  dayNumberText: {
    fontSize: 14,
    fontWeight: '500',
  },
  selectedDayNumberText: {
    color: '#ffffff',
    fontWeight: 'bold',
  },

  // Status buttons
  statusButtonsContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  statusButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 16,
  },
  statusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    backgroundColor: '#f8fafc',
    gap: 8,
    minWidth: '45%',
  },
  disabledButton: {
    opacity: 0.5,
  },
  statusButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  overtimeHours: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 'auto',
  },
  currentStatusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 16,
  },
  currentStatusLabel: {
    fontSize: 14,
    color: '#64748b',
    marginRight: 8,
  },
  currentStatusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  currentStatusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  permissionNote: {
    fontSize: 12,
    color: '#ef4444',
    fontStyle: 'italic',
    marginTop: 8,
  },

  // History
  historyContainer: {
    padding: 16,
  },
  historyList: {
    maxHeight: 300,
  },
  historyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  historyDateContainer: {
    minWidth: 80,
  },
  historyDate: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  historySummaryContainer: {
    flex: 1,
  },
  historySummaryRow: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 4,
  },
  historySummaryItem: {
    alignItems: 'center',
    minWidth: 24,
  },
  historySummaryCount: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  historySummaryLabel: {
    fontSize: 10,
    color: '#64748b',
    fontWeight: '500',
  },
  historyTotalText: {
    fontSize: 12,
    color: '#64748b',
  },
  emptyHistoryContainer: {
    alignItems: 'center',
    padding: 32,
  },
  emptyHistoryText: {
    fontSize: 16,
    color: '#64748b',
    marginTop: 8,
  },

  // Modal
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },

  modalContent: {
    width: '90%',
    maxWidth: 400,
    borderRadius: 16,
    padding: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  closeButton: {
    padding: 4,
    borderRadius: 4,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalBody: {
    padding: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    color: '#374151',
  },
  otInput: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    padding: 20,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#f1f5f9',
  },
  confirmButton: {
    backgroundColor: '#f97316',
  },
  cancelButtonText: {
    color: '#64748b',
    fontSize: 16,
    fontWeight: '600',
  },
  confirmButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },

  // FAB Container
  fabContainer: {
    position: 'absolute',
    bottom: 32,
    right: 20,
    alignItems: 'center',
    gap: 16,
  },

  // Floating Action Button
  fab: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#f97316',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 8,
  },

  // Report FAB (different color)
  reportFab: {
    backgroundColor: '#10b981',
  },

  // Report Modal Styles
  reportModalContent: {
    width: '85%',
    maxWidth: 400,
    maxHeight: '85%',
    borderRadius: 16,
    padding: 0,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 10,
  },

  reportFormScrollable: {
    maxHeight: 500,
    paddingHorizontal: 20,
  },

  reportFormContent: {
    paddingBottom: 20,
  },

  // Report Type Selection
  reportTypeContainer: {
    gap: 12,
  },

  reportTypeOption: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
  },

  selectedReportType: {
    borderColor: '#f97316',
    borderWidth: 2,
  },

  reportTypeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },

  reportTypeLabel: {
    fontSize: 16,
    fontWeight: '600',
  },

  reportTypeDesc: {
    fontSize: 14,
    color: '#64748b',
  },

  // Date Picker Styles
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderWidth: 1,
    borderRadius: 8,
    marginBottom: 12,
    gap: 12,
  },

  datePickerText: {
    fontSize: 16,
    fontWeight: '500',
  },

  // Quick Date Selection
  quickDateContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },

  quickDateButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },

  quickDateText: {
    fontSize: 14,
    fontWeight: '500',
  },

  // Laborers Section
  laborersContainer: {
    padding: 16,
  },
  laborerItem: {
    borderRadius: 12,
    marginBottom: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  laborerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  laborerIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(249, 115, 22, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  laborerDetails: {
    flex: 1,
  },
  laborerName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  laborerCategory: {
    fontSize: 12,
    color: '#64748b',
    marginBottom: 2,
  },
  laborerWage: {
    fontSize: 12,
    color: '#10b981',
    fontWeight: '500',
  },
  deleteButton: {
    padding: 8,
  },
  laborerAttendanceButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  laborerStatusButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 4,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    backgroundColor: '#f8fafc',
    alignItems: 'center',
    minHeight: 36,
  },
  laborerStatusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#374151',
  },
  laborerOTHours: {
    fontSize: 10,
    fontWeight: '600',
    marginTop: 2,
  },
  emptyLaborersContainer: {
    alignItems: 'center',
    padding: 32,
  },
  emptyLaborersText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 8,
    color: '#374151',
  },
  emptyLaborersSubtext: {
    fontSize: 14,
    color: '#64748b',
    marginTop: 4,
    textAlign: 'center',
  },

  // Summary styles
  summaryContainer: {
    backgroundColor: '#f8fafc',
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 12,
  },
  summaryItem: {
    alignItems: 'center',
    flex: 1,
  },
  summaryCount: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 12,
    color: '#64748b',
    fontWeight: '500',
  },
  summaryFooter: {
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  summaryFooterText: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
  noDataContainer: {
    alignItems: 'center',
    padding: 24,
  },
  noDataText: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
  },

  // Permission info styles
  permissionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: 'rgba(100, 116, 139, 0.1)',
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 8,
    gap: 8,
  },
  permissionText: {
    fontSize: 12,
    color: '#64748b',
    flex: 1,
    fontStyle: 'italic',
  },

  // Format Selection
  formatSelectionContainer: {
    flexDirection: 'row',
    gap: 12,
  },

  formatOption: {
    borderWidth: 2,
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
  },

  selectedFormatOption: {
    borderColor: '#f97316',
    backgroundColor: '#f0f7ff',
  },

  formatOptionText: {
    marginTop: 4,
  },

  formatLabel: {
    fontSize: 14,
    fontWeight: '600',
  },

  formatDesc: {
    fontSize: 12,
    color: '#64748b',
  },

  generateButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
});