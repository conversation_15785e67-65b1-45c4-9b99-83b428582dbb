import * as FileSystem from 'expo-file-system';
import * as Print from 'expo-print';
import * as Sharing from 'expo-sharing';
import { Platform } from 'react-native';
import * as XLSX from 'xlsx';

type Material = {
  id: string;
  name: string;
  specifications: string | null;
  stock_level: number;
  minimum_stock_level: number;
  unit_of_measure: string;
  price: number;
  category: string | null;
  received_date: string;
  created_at: string;
  updated_at: string;
  used_stock?: number;
};

type Task = {
  id: string;
  site_id: string;
  name: string;
  work_category: string;
  status: 'pending' | 'in progress' | 'completed';
  overall_progress: number;
  due_date: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  description?: string;
};

type TaskSubcategory = {
  id: string;
  task_id: string;
  name: string;
  quantity: number;
  completed_quantity: number;
  unit_of_measure: string;
  progress_percentage: number;
  created_at: string;
  updated_at: string;
};

type Labor = {
  id: string;
  full_name: string;
  labor_category: string;
  daily_wage: number;
  phone?: string;
  site_id: string;
  created_at: string;
  updated_at: string;
};

type Attendance = {
  id: string;
  laborer_id: string;
  site_id: string;
  attendance_date: string;
  status: 'present' | 'absent' | 'half_day' | 'overtime';
  overtime_hours?: number;
  created_at: string;
  updated_at: string;
  laborer_name?: string;
  labor_category?: string;
  daily_wage?: number;
  site_name?: string;
};

// Export types
export type { Attendance, Labor, Material, Task, TaskSubcategory };

/**
 * Exports materials data as a CSV file
 */
export async function exportMaterialsAsExcel(materials: Material[], siteName: string): Promise<string> {
  try {
    // Convert the materials data to worksheet format
    const worksheet = XLSX.utils.json_to_sheet(materials.map(material => {
      const usedStock = material.used_stock || 0;
      const balanceStock = material.stock_level - usedStock;
      
      return {
        Name: material.name,
        Specifications: material.specifications || '',
        'Total Stock': material.stock_level,
        'Used': usedStock,
        'Balance Stock': balanceStock,
        'Minimum Stock': material.minimum_stock_level,
        'Unit of Measure': material.unit_of_measure,
        Price: material.price,
        Category: material.category || 'Uncategorized',
        'Received Date': material.received_date ? new Date(material.received_date).toLocaleDateString() : '',
        'Last Updated': new Date(material.updated_at).toLocaleDateString(),
      };
    }));

    // Create workbook with the worksheet
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Materials');

    // Generate the Excel file content
    const excelOutput = XLSX.write(workbook, { type: 'base64', bookType: 'xlsx' });

    // Generate a filename with the date
    const date = new Date().toISOString().split('T')[0];
    const sanitizedSiteName = siteName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const fileName = `${sanitizedSiteName}_materials_${date}.xlsx`;
    const fileUri = FileSystem.documentDirectory + fileName;

    // Write to file
    await FileSystem.writeAsStringAsync(fileUri, excelOutput, {
      encoding: FileSystem.EncodingType.Base64,
    });

    // Share the file
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      await Sharing.shareAsync(fileUri);
    }

    return fileUri;
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    throw error;
  }
}

/**
 * Exports materials data as a PDF file
 */
export async function exportMaterialsAsPDF(materials: Material[], siteName: string): Promise<string> {
  try {
    // Create an HTML table from the materials data
    const tableRows = materials.map(material => {
      const isLowStock = material.stock_level <= material.minimum_stock_level;
      const stockLevelClass = isLowStock ? 'low-stock' : '';
      const usedStock = material.used_stock || 0;
      const balanceStock = material.stock_level - usedStock;
      const balanceStockClass = balanceStock < material.minimum_stock_level ? 'low-stock' : 
                              balanceStock >= material.minimum_stock_level + 10 ? 'good-stock' : '';
      
      return `
      <tr>
        <td>${material.name}</td>
        <td>${material.specifications || ''}</td>
        <td class="${stockLevelClass}">${material.stock_level} ${material.unit_of_measure}</td>
        <td>${usedStock} ${material.unit_of_measure}</td>
        <td class="${balanceStockClass}">${balanceStock} ${material.unit_of_measure}</td>
        <td>${material.minimum_stock_level} ${material.unit_of_measure}</td>
        <td>₹${material.price.toFixed(2)}</td>
        <td>${material.category || 'Uncategorized'}</td>
        <td>${material.received_date ? new Date(material.received_date).toLocaleDateString() : ''}</td>
        <td>${new Date(material.updated_at).toLocaleDateString()}</td>
      </tr>
    `}).join('');

    // Create the full HTML document
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Materials Inventory</title>
          <style>
            body { font-family: 'Helvetica', sans-serif; margin: 0; padding: 20px; color: #333; }
            h1 { color: #f97316; text-align: center; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th { background-color: #f97316; color: white; text-align: left; padding: 10px; }
            td { padding: 8px; border-bottom: 1px solid #ddd; }
            tr:nth-child(even) { background-color: #f9f9f9; }
            .site-info { margin-bottom: 20px; text-align: center; }
            .low-stock { color: #ef4444; font-weight: bold; }
            .good-stock { color: #16a34a; font-weight: bold; }
            .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <h1>Materials Inventory</h1>
          <div class="site-info">
            <p><strong>Site:</strong> ${siteName}</p>
            <p><strong>Date Generated:</strong> ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}</p>
            <p><strong>Total Materials:</strong> ${materials.length}</p>
          </div>
          <table>
            <thead>
              <tr>
                <th>Name</th>
                <th>Specifications</th>
                <th>Total Stock</th>
                <th>Used</th>
                <th>Balance Stock</th>
                <th>Minimum Stock</th>
                <th>Price</th>
                <th>Category</th>
                <th>Received Date</th>
                <th>Last Updated</th>
              </tr>
            </thead>
            <tbody>
              ${tableRows}
            </tbody>
          </table>
          <div class="footer">
            <p>Generated by InfraTask App</p>
          </div>
        </body>
      </html>
    `;

    // Generate a filename with the date
    const date = new Date().toISOString().split('T')[0];
    const sanitizedSiteName = siteName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    
    // Create a PDF file using expo-print
    const { uri } = await Print.printToFileAsync({ 
      html: htmlContent,
      base64: false
    });
    
    // Generate a better filename
    const newFileUri = FileSystem.documentDirectory + `${sanitizedSiteName}_materials_${date}.pdf`;
    
    // Copy to a more appropriate location with a cleaner filename
    await FileSystem.copyAsync({
      from: uri,
      to: newFileUri
    });
    
    // Delete the temporary file
    await FileSystem.deleteAsync(uri, { idempotent: true });
    
    // Share the file
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      await Sharing.shareAsync(newFileUri, {
        mimeType: 'application/pdf',
        UTI: 'com.adobe.pdf'
      });
    }
    
    return newFileUri;
  } catch (error) {
    console.error('Error exporting to PDF:', error);
    throw error;
  }
}

/**
 * Exports task data as an Excel file
 */
export async function exportTaskReportAsExcel(
  task: Task,
  subcategories: TaskSubcategory[],
  siteName: string,
  creatorName: string
): Promise<string> {
  try {
    // Convert task data to worksheet format
    const taskData = {
      'Task Name': task.name,
      'Work Category': task.work_category,
      'Status': task.status.charAt(0).toUpperCase() + task.status.slice(1),
      'Overall Progress': `${task.overall_progress}%`,
      'Due Date': new Date(task.due_date).toLocaleDateString(),
      'Created By': creatorName,
      'Created On': new Date(task.created_at).toLocaleDateString(),
      'Last Updated': new Date(task.updated_at).toLocaleDateString(),
    };

    // Create task overview worksheet
    const taskWorksheet = XLSX.utils.json_to_sheet([taskData]);

    // Convert subcategories data to worksheet format
    const subcategoriesData = subcategories.map(sub => ({
      'Name': sub.name,
      'Total Quantity': sub.quantity,
      'Completed Quantity': sub.completed_quantity,
      'Unit of Measure': sub.unit_of_measure,
      'Progress': `${sub.progress_percentage}%`,
      'Last Updated': new Date(sub.updated_at).toLocaleDateString(),
    }));

    // Create subcategories worksheet
    const subcategoriesWorksheet = XLSX.utils.json_to_sheet(subcategoriesData);

    // Create workbook and append worksheets
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, taskWorksheet, 'Task Overview');
    XLSX.utils.book_append_sheet(workbook, subcategoriesWorksheet, 'Subcategories');

    // Generate the Excel file content
    const excelOutput = XLSX.write(workbook, { type: 'base64', bookType: 'xlsx' });

    // Generate a filename with the date
    const date = new Date().toISOString().split('T')[0];
    const sanitizedSiteName = siteName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const sanitizedTaskName = task.name.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const fileName = `${sanitizedSiteName}_${sanitizedTaskName}_${date}.xlsx`;
    const fileUri = FileSystem.documentDirectory + fileName;

    // Write to file
    await FileSystem.writeAsStringAsync(fileUri, excelOutput, {
      encoding: FileSystem.EncodingType.Base64,
    });

    // Share the file
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      await Sharing.shareAsync(fileUri);
    }

    return fileUri;
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    throw error;
  }
}

/**
 * Exports task report data as a PDF file
 */
export async function exportTaskReportAsPDF(
  tasks: Task[], 
  subcategories: Record<string, TaskSubcategory[]>, 
  siteName: string,
  dateRange?: { start: string, end: string },
  category?: string
): Promise<string> {
  try {
    // Create an HTML table from the tasks data
    const tableRows = tasks.map(task => {
      const statusClass = 
        task.status === 'completed' ? 'completed-status' : 
        task.status === 'in progress' ? 'in-progress-status' : 'pending-status';
      
      // Safely format dates
      let dueDateFormatted = 'N/A';
      let createdAtFormatted = 'N/A';
      
      try {
        const dueDate = new Date(task.due_date);
        if (!isNaN(dueDate.getTime())) {
          dueDateFormatted = dueDate.toLocaleDateString();
        }
      } catch (error) {
        console.error('Invalid due date:', task.due_date);
      }
      
      try {
        const createdAt = new Date(task.created_at);
        if (!isNaN(createdAt.getTime())) {
          createdAtFormatted = createdAt.toLocaleDateString();
        }
      } catch (error) {
        console.error('Invalid created date:', task.created_at);
      }
      
      return `
      <tr>
        <td>${task.name}</td>
        <td>${task.description || ''}</td>
        <td>${task.work_category}</td>
        <td class="${statusClass}">${task.status.charAt(0).toUpperCase() + task.status.slice(1)}</td>
        <td>${task.overall_progress}%</td>
        <td>${dueDateFormatted}</td>
        <td>${createdAtFormatted}</td>
      </tr>
    `}).join('');

    // Create task details sections with subcategories
    const taskDetails = tasks.map(task => {
      const taskSubcategories = subcategories[task.id] || [];
      
      if (taskSubcategories.length === 0) {
        return '';
      }
      
      const subcategoryRows = taskSubcategories.map(sub => `
        <tr>
          <td>${sub.name}</td>
          <td>${sub.completed_quantity} / ${sub.quantity} ${sub.unit_of_measure}</td>
          <td>${sub.progress_percentage}%</td>
        </tr>
      `).join('');
      
      // Safely format due date
      let dueDateFormatted = 'N/A';
      try {
        const dueDate = new Date(task.due_date);
        if (!isNaN(dueDate.getTime())) {
          dueDateFormatted = dueDate.toLocaleDateString();
        }
      } catch (error) {
        console.error('Invalid due date in task details:', task.due_date);
      }
      
      return `
        <div class="task-detail">
          <h3>${task.name}</h3>
          <p><strong>Category:</strong> ${task.work_category}</p>
          <p><strong>Status:</strong> ${task.status.charAt(0).toUpperCase() + task.status.slice(1)}</p>
          <p><strong>Overall Progress:</strong> ${task.overall_progress}%</p>
          <p><strong>Due Date:</strong> ${dueDateFormatted}</p>
          ${task.description ? `<p><strong>Description:</strong> ${task.description}</p>` : ''}
          
          <h4>Subcategories</h4>
          <table class="subcategory-table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Completion</th>
                <th>Progress</th>
              </tr>
            </thead>
            <tbody>
              ${subcategoryRows}
            </tbody>
          </table>
        </div>
        <div class="page-break"></div>
      `;
    }).join('');

    // Filter details display
    let filterInfo = '';
    if (category && category !== 'All Tasks') {
      filterInfo += `<p><strong>Category Filter:</strong> ${category}</p>`;
    }
    if (dateRange && dateRange.start) {
      try {
        const startDate = new Date(dateRange.start);
        if (!isNaN(startDate.getTime())) {
          filterInfo += `<p><strong>From:</strong> ${startDate.toLocaleDateString()}</p>`;
        }
      } catch (error) {
        console.error('Invalid start date in PDF generation:', dateRange.start);
      }
    }
    if (dateRange && dateRange.end) {
      try {
        const endDate = new Date(dateRange.end);
        if (!isNaN(endDate.getTime())) {
          filterInfo += `<p><strong>To:</strong> ${endDate.toLocaleDateString()}</p>`;
        }
      } catch (error) {
        console.error('Invalid end date in PDF generation:', dateRange.end);
      }
    }

    // Create the full HTML document
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Task Report</title>
          <style>
            body { font-family: 'Helvetica', sans-serif; margin: 0; padding: 20px; color: #333; }
            h1 { color: #f97316; text-align: center; }
            h2 { color: #f97316; margin-top: 30px; }
            h3 { color: #f97316; margin-top: 20px; margin-bottom: 10px; }
            h4 { margin-top: 15px; margin-bottom: 5px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th { background-color: #f97316; color: white; text-align: left; padding: 10px; }
            td { padding: 8px; border-bottom: 1px solid #ddd; }
            tr:nth-child(even) { background-color: #f9f9f9; }
            .site-info { margin-bottom: 20px; text-align: center; }
            .pending-status { color: #f59e0b; font-weight: bold; }
            .in-progress-status { color: #3b82f6; font-weight: bold; }
            .completed-status { color: #10b981; font-weight: bold; }
            .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
            .task-detail { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; page-break-inside: avoid; }
            .subcategory-table { margin-top: 10px; }
            .page-break { page-break-after: always; height: 0; }
            .progress-bar-container { width: 100%; height: 20px; background-color: #f3f4f6; border-radius: 10px; overflow: hidden; }
            .progress-bar { height: 100%; background-color: #f97316; }
            .summary-box { display: inline-block; padding: 15px; margin: 10px; min-width: 120px; text-align: center; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .summary-number { font-size: 24px; font-weight: bold; margin: 5px 0; }
            .summary-label { font-size: 14px; color: #666; }
          </style>
        </head>
        <body>
          <h1>Task Report</h1>
          <div class="site-info">
            <p><strong>Site:</strong> ${siteName}</p>
            <p><strong>Date Generated:</strong> ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}</p>
            <p><strong>Total Tasks:</strong> ${tasks.length}</p>
            ${filterInfo}
          </div>
          
          <div style="text-align: center; margin: 20px 0;">
            <div class="summary-box" style="background-color: #fee2e2;">
              <div class="summary-number">${tasks.filter(t => t.status === 'pending').length}</div>
              <div class="summary-label">Pending</div>
            </div>
            <div class="summary-box" style="background-color: #dbeafe;">
              <div class="summary-number">${tasks.filter(t => t.status === 'in progress').length}</div>
              <div class="summary-label">In Progress</div>
            </div>
            <div class="summary-box" style="background-color: #dcfce7;">
              <div class="summary-number">${tasks.filter(t => t.status === 'completed').length}</div>
              <div class="summary-label">Completed</div>
            </div>
          </div>
          
          <h2>Tasks Summary</h2>
          <table>
            <thead>
              <tr>
                <th>Name</th>
                <th>Description</th>
                <th>Category</th>
                <th>Status</th>
                <th>Progress</th>
                <th>Due Date</th>
                <th>Created</th>
              </tr>
            </thead>
            <tbody>
              ${tableRows}
            </tbody>
          </table>
          
          <div class="page-break"></div>
          
          <h2>Detailed Task Information</h2>
          ${taskDetails}
          
          <div class="footer">
            <p>Generated by InfraTask App</p>
          </div>
        </body>
      </html>
    `;

    // Generate a filename with the date
    const date = new Date().toISOString().split('T')[0];
    const sanitizedSiteName = siteName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    
    // Create a PDF file using expo-print
    const { uri } = await Print.printToFileAsync({ 
      html: htmlContent,
      base64: false
    });
    
    // Generate a better filename
    const newFileUri = FileSystem.documentDirectory + `${sanitizedSiteName}_tasks_report_${date}.pdf`;
    
    // Copy to a more appropriate location with a cleaner filename
    await FileSystem.copyAsync({
      from: uri,
      to: newFileUri
    });
    
    // Delete the temporary file
    await FileSystem.deleteAsync(uri, { idempotent: true });
    
    // Share the file
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      await Sharing.shareAsync(newFileUri, {
        mimeType: 'application/pdf',
        UTI: 'com.adobe.pdf'
      });
    }
    
    return newFileUri;
  } catch (error) {
    console.error('Error exporting task report to PDF:', error);
    throw error;
  }
}

/**
 * Exports attendance data as Excel
 */
export async function exportAttendanceAsExcel(
  attendance: Attendance[],
  siteName: string,
  dateRange?: { start: string, end: string },
  categoryFilter?: string
): Promise<string> {
  try {
    // First, determine if this is a single-day report using direct string comparison
    let isSingleDay = false;
    if (dateRange && dateRange.start && dateRange.end) {
      // If the raw date strings are identical, it's a single day report
      isSingleDay = dateRange.start === dateRange.end;
      console.log(`Excel Report is for a single day: ${isSingleDay} (${dateRange.start} to ${dateRange.end})`);
    }
    
    // Group attendance by labor
    const laborMap = new Map<string, { 
      name: string, 
      category: string, 
      dailyWage: number,
      records: { date: string, status: string, hoursWorked: number }[],
      totalDays: number,
      totalHours: number,
      totalPayout: number 
    }>();
    
    // Log first few records for debugging
    console.log(`Processing ${attendance.length} attendance records`);
    if (attendance.length > 0) {
      console.log('Sample labor data structure:', attendance[0].labor);
    }
    
    // Process attendance data
    attendance.forEach(record => {
      const laborId = record.laborer_id;
      const laborName = record.laborer_name || 'Unknown';
      const category = record.labor_category || 'Unspecified';
      const dailyWage = record.daily_wage || 0;
      
      // Initialize entry if not exists
      if (!laborMap.has(laborId)) {
        laborMap.set(laborId, {
          name: laborName,
          category,
          dailyWage,
          records: [],
          totalDays: 0,
          totalHours: 0,
          totalPayout: 0
        });
      }
      
      const labor = laborMap.get(laborId)!;
      
      // Add record with safe date parsing
      let formattedDate = 'Unknown Date';
      try {
        const dateObj = new Date(record.attendance_date);
        if (!isNaN(dateObj.getTime())) {
          formattedDate = dateObj.toLocaleDateString();
        } else {
          console.log('Invalid date format in record:', record.id, record.attendance_date);
        }
      } catch (error) {
        console.log('Error parsing date in record:', record.id, record.attendance_date, error);
      }
      
      // Calculate pay multiplier based on status
      let hoursWorked = 8; // Default full day
      let payMultiplier = 0;

      switch (record.status) {
        case 'present':
          payMultiplier = 1;
          hoursWorked = 8; // Full day
          break;
        case 'half_day':
          payMultiplier = 0.5;
          hoursWorked = 4; // Half day
          break;
        case 'overtime':
          payMultiplier = 1;
          hoursWorked = 8 + (record.overtime_hours || 0); // Regular + overtime
          break;
        case 'absent':
          payMultiplier = 0;
          hoursWorked = 0;
          break;
      }
      
      labor.records.push({
        date: formattedDate,
        status: record.status,
        hoursWorked
      });
      
      // Always accumulate hours and payout
      labor.totalHours += hoursWorked;
      labor.totalPayout += dailyWage * payMultiplier;
      
      // Handle totalDays calculation (will be adjusted later for single-day reports)
      labor.totalDays += payMultiplier;
      
      // Update the map
      laborMap.set(laborId, labor);
    });
    
    // Force cap on days worked for single-day reports
    if (isSingleDay) {
      console.log('Applying single-day constraint to Excel report');
      laborMap.forEach((labor) => {
        // First, get the highest status pay multiplier for this single day
        let maxMultiplier = 0;
        let bestStatus = '';
        
        // Find the record with the highest pay multiplier
        labor.records.forEach(record => {
          const recordMultiplier = 
            record.status === 'present' ? 1 : 
            record.status === 'half-day' ? 0.5 : 
            record.status === 'late' ? 0.75 : 0;
            
          if (recordMultiplier > maxMultiplier) {
            maxMultiplier = recordMultiplier;
            bestStatus = record.status;
          }
        });
        
        // Cap days at 1.0 for single day
        if (labor.totalDays > 1.0) {
          console.warn(`Forcing cap on days worked for ${labor.name} from ${labor.totalDays} to 1.0`);
          labor.totalDays = 1.0;
        }
        
        // Set standard hours based on the status with highest multiplier
        let standardHours = 0;
        switch(bestStatus) {
          case 'present':
            standardHours = 8; // Full day standard
            break;
          case 'half-day':
            standardHours = 4; // Half day standard
            break;
          case 'late':
            standardHours = 6; // Late day standard
            break;
          default:
            standardHours = 0; // Absent/leave
        }
        
        // Update hours and recalculate payout based on the corrected days
        labor.totalHours = standardHours;
        labor.totalPayout = labor.dailyWage * labor.totalDays;
        
        console.log(`${labor.name}: Status=${bestStatus}, Days=${labor.totalDays}, Hours=${labor.totalHours}, Payout=${labor.totalPayout}`);
      });
    }
    
    // Use all the data without additional filtering, as the data is already filtered upstream
    const filteredLaborMap = laborMap;
    
    // Calculate total days for report summary
    let totalDays = 0;
    let totalPayout = 0;
    let totalHours = 0;
    
    filteredLaborMap.forEach(labor => {
      totalPayout += labor.totalPayout;
      totalDays += labor.totalDays;
      totalHours += labor.totalHours;
    });
    
    // Additional safety check for single-day reports
    if (isSingleDay && totalDays > filteredLaborMap.size) {
      console.warn(`Capping total days from ${totalDays} to ${filteredLaborMap.size} (single day report)`);
      totalDays = filteredLaborMap.size;
    }
    
    // Prepare data for Excel sheet
    const summaryData = Array.from(filteredLaborMap.values()).map(labor => {
      return {
        'Name': labor.name,
        'Category': labor.category,
        'Daily Wage': labor.dailyWage,
        'Total Days': labor.totalDays,
        'Total Hours': labor.totalHours,
        'Total Payout': labor.totalPayout
      };
    });
    
    // Add a total row
    summaryData.push({
      'Name': 'TOTAL',
      'Category': '',
      'Daily Wage': 0,
      'Total Days': totalDays,
      'Total Hours': totalHours,
      'Total Payout': totalPayout
    });
    
    // Create detailed data (one row per attendance record)
    const detailedData: any[] = [];
    filteredLaborMap.forEach((labor, laborId) => {
      labor.records.forEach(record => {
        detailedData.push({
          'Name': labor.name,
          'Category': labor.category,
          'Date': record.date,
          'Status': record.status.charAt(0).toUpperCase() + record.status.slice(1),
          'Hours Worked': record.hoursWorked,
          'Daily Wage': labor.dailyWage,
          'Payout': record.status === 'present' ? labor.dailyWage : 
                   record.status === 'half-day' ? labor.dailyWage * 0.5 :
                   record.status === 'late' ? labor.dailyWage * 0.75 : 0
        });
      });
    });
    
    // Create workbook with worksheets
    const workbook = XLSX.utils.book_new();
    
    // Add summary sheet
    const summarySheet = XLSX.utils.json_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
    
    // Add detailed attendance sheet
    const detailedSheet = XLSX.utils.json_to_sheet(detailedData);
    XLSX.utils.book_append_sheet(workbook, detailedSheet, 'Detailed Attendance');
    
    // Generate the Excel file content
    const excelOutput = XLSX.write(workbook, { type: 'base64', bookType: 'xlsx' });

    // Generate a filename with the date
    const date = new Date().toISOString().split('T')[0];
    const sanitizedSiteName = siteName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const fileName = `${sanitizedSiteName}_attendance_report_${date}.xlsx`;
    const fileUri = FileSystem.documentDirectory + fileName;

    // Write to file
    await FileSystem.writeAsStringAsync(fileUri, excelOutput, {
      encoding: FileSystem.EncodingType.Base64,
    });

    // Share the file
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      await Sharing.shareAsync(fileUri);
    }

    return fileUri;
  } catch (error) {
    console.error('Error exporting attendance to Excel:', error);
    throw error;
  }
}

/**
 * Exports attendance data as PDF
 */
export async function exportAttendanceAsPDF(
  attendance: Attendance[],
  siteName: string,
  dateRange?: { start: string, end: string },
  categoryFilter?: string
): Promise<string> {
  try {
    // First, determine if this is a single-day report using direct string comparison
    let isSingleDay = false;
    if (dateRange && dateRange.start && dateRange.end) {
      // If the raw date strings are identical, it's a single day report
      isSingleDay = dateRange.start === dateRange.end;
      console.log(`PDF Report is for a single day: ${isSingleDay} (${dateRange.start} to ${dateRange.end})`);
    }
    
    // Group attendance by labor
    const laborMap = new Map<string, { 
      name: string, 
      category: string, 
      dailyWage: number,
      records: { date: string, status: string, hoursWorked: number }[],
      totalDays: number,
      totalHours: number,
      totalPayout: number 
    }>();
    
    // Process attendance data
    attendance.forEach(record => {
      const laborId = record.laborer_id;
      const laborName = record.laborer_name || 'Unknown';
      const category = record.labor_category || 'Unspecified';
      const dailyWage = record.daily_wage || 0;
      
      // Initialize entry if not exists
      if (!laborMap.has(laborId)) {
        laborMap.set(laborId, {
          name: laborName,
          category,
          dailyWage,
          records: [],
          totalDays: 0,
          totalHours: 0,
          totalPayout: 0
        });
      }
      
      const labor = laborMap.get(laborId)!;
      
      // Add record with safe date parsing
      let formattedDate = 'Unknown Date';
      try {
        const dateObj = new Date(record.attendance_date);
        if (!isNaN(dateObj.getTime())) {
          formattedDate = dateObj.toLocaleDateString();
        } else {
          console.log('Invalid date format in record:', record.id, record.attendance_date);
        }
      } catch (error) {
        console.log('Error parsing date in record:', record.id, record.attendance_date, error);
      }
      
      // Calculate pay multiplier based on status
      let hoursWorked = 8; // Default full day
      let payMultiplier = 0;

      switch (record.status) {
        case 'present':
          payMultiplier = 1;
          hoursWorked = 8; // Full day
          break;
        case 'half_day':
          payMultiplier = 0.5;
          hoursWorked = 4; // Half day
          break;
        case 'overtime':
          payMultiplier = 1;
          hoursWorked = 8 + (record.overtime_hours || 0); // Regular + overtime
          break;
        case 'absent':
          payMultiplier = 0;
          hoursWorked = 0;
          break;
      }
      
      labor.records.push({
        date: formattedDate,
        status: record.status,
        hoursWorked
      });
      
      // Always accumulate hours and payout
      labor.totalHours += hoursWorked;
      labor.totalPayout += dailyWage * payMultiplier;
      
      // Handle totalDays calculation (will be adjusted later for single-day reports)
      labor.totalDays += payMultiplier;
      
      // Update the map
      laborMap.set(laborId, labor);
    });
    
    // Force cap on days worked for single-day reports
    if (isSingleDay) {
      console.log('Applying single-day constraint to PDF report');
      laborMap.forEach((labor) => {
        // First, get the highest status pay multiplier for this single day
        let maxMultiplier = 0;
        let bestStatus = '';
        
        // Find the record with the highest pay multiplier
        labor.records.forEach(record => {
          const recordMultiplier = 
            record.status === 'present' ? 1 : 
            record.status === 'half-day' ? 0.5 : 
            record.status === 'late' ? 0.75 : 0;
            
          if (recordMultiplier > maxMultiplier) {
            maxMultiplier = recordMultiplier;
            bestStatus = record.status;
          }
        });
        
        // Cap days at 1.0 for single day
        if (labor.totalDays > 1.0) {
          console.warn(`Forcing cap on days worked for ${labor.name} from ${labor.totalDays} to 1.0`);
          labor.totalDays = 1.0;
        }
        
        // Set standard hours based on the status with highest multiplier
        let standardHours = 0;
        switch(bestStatus) {
          case 'present':
            standardHours = 8; // Full day standard
            break;
          case 'half-day':
            standardHours = 4; // Half day standard
            break;
          case 'late':
            standardHours = 6; // Late day standard
            break;
          default:
            standardHours = 0; // Absent/leave
        }
        
        // Update hours and recalculate payout based on the corrected days
        labor.totalHours = standardHours;
        labor.totalPayout = labor.dailyWage * labor.totalDays;
        
        console.log(`${labor.name}: Status=${bestStatus}, Days=${labor.totalDays}, Hours=${labor.totalHours}, Payout=${labor.totalPayout}`);
      });
    }
    
    // Use all the data without additional filtering, as the data is already filtered upstream
    const filteredLaborMap = laborMap;
    
    // Create the summary table rows
    const summaryRows = Array.from(filteredLaborMap.values()).map(labor => `
      <tr>
        <td>${labor.name}</td>
        <td>${labor.category}</td>
        <td>₹${labor.dailyWage.toFixed(2)}</td>
        <td>${labor.totalDays.toFixed(1)}</td>
        <td>${labor.totalHours}</td>
        <td>₹${labor.totalPayout.toFixed(2)}</td>
      </tr>
    `).join('');
    
    // Calculate totals
    const totalLabor = filteredLaborMap.size;
    let totalPayout = 0;
    let totalDays = 0;
    let totalHours = 0;
    
    filteredLaborMap.forEach(labor => {
      totalPayout += labor.totalPayout;
      totalDays += labor.totalDays;
      totalHours += labor.totalHours;
    });
    
    // Additional safety check for single-day reports
    if (isSingleDay && totalDays > totalLabor) {
      console.warn(`Capping total days from ${totalDays} to ${totalLabor} (single day report)`);
      totalDays = totalLabor;
    }
    
    // Create the detailed attendance table
    const detailedRows: string[] = [];
    filteredLaborMap.forEach((labor) => {
      // Create a section for each labor
      detailedRows.push(`
        <tr class="labor-header">
          <td colspan="5">
            <strong>${labor.name}</strong> (${labor.category}) - Daily Wage: ₹${labor.dailyWage.toFixed(2)}
          </td>
        </tr>
      `);
      
      // Add records for the labor
      labor.records.forEach(record => {
        const payout = record.status === 'present' ? labor.dailyWage : 
                      record.status === 'half-day' ? labor.dailyWage * 0.5 :
                      record.status === 'late' ? labor.dailyWage * 0.75 : 0;
        
        const statusClass = 
          record.status === 'present' ? 'present-status' : 
          record.status === 'absent' ? 'absent-status' : 
          record.status === 'half-day' ? 'half-day-status' : 
          record.status === 'late' ? 'late-status' : 'leave-status';
        
        detailedRows.push(`
          <tr>
            <td>${record.date}</td>
            <td class="${statusClass}">${record.status.charAt(0).toUpperCase() + record.status.slice(1)}</td>
            <td>${record.hoursWorked}</td>
            <td>₹${payout.toFixed(2)}</td>
          </tr>
        `);
      });
      
      // Add a summary row for the labor
      detailedRows.push(`
        <tr class="labor-summary">
          <td colspan="2">Total</td>
          <td>${labor.totalHours}</td>
          <td>₹${labor.totalPayout.toFixed(2)}</td>
        </tr>
        <tr class="labor-spacer"><td colspan="5"></td></tr>
      `);
    });
    
    // Filter details display
    let filterInfo = '';
    if (categoryFilter) {
      filterInfo += `<p><strong>${categoryFilter}</strong></p>`;
    }
    if (dateRange && dateRange.start) {
      try {
        const startDate = new Date(dateRange.start);
        if (!isNaN(startDate.getTime())) {
          filterInfo += `<p><strong>From:</strong> ${startDate.toLocaleDateString()}</p>`;
        } else {
          console.warn('Invalid start date format:', dateRange.start);
          filterInfo += `<p><strong>From:</strong> ${dateRange.start}</p>`;
        }
      } catch (error) {
        console.error('Error formatting start date:', error);
        // Use the raw date string as fallback
        filterInfo += `<p><strong>From:</strong> ${dateRange.start}</p>`;
      }
    }
    if (dateRange && dateRange.end) {
      try {
        const endDate = new Date(dateRange.end);
        if (!isNaN(endDate.getTime())) {
          filterInfo += `<p><strong>To:</strong> ${endDate.toLocaleDateString()}</p>`;
        } else {
          console.warn('Invalid end date format:', dateRange.end);
          filterInfo += `<p><strong>To:</strong> ${dateRange.end}</p>`;
        }
      } catch (error) {
        console.error('Error formatting end date:', error);
        // Use the raw date string as fallback
        filterInfo += `<p><strong>To:</strong> ${dateRange.end}</p>`;
      }
    }
    
    // Create the full HTML document
    const htmlContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Attendance Report</title>
          <style>
            body { font-family: 'Helvetica', sans-serif; margin: 0; padding: 20px; color: #333; }
            h1 { color: #10b981; text-align: center; }
            h2 { color: #10b981; margin-top: 30px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th { background-color: #10b981; color: white; text-align: left; padding: 10px; }
            td { padding: 8px; border-bottom: 1px solid #ddd; }
            tr:nth-child(even) { background-color: #f9f9f9; }
            .site-info { margin-bottom: 20px; text-align: center; }
            .present-status { color: #16a34a; font-weight: bold; }
            .absent-status { color: #ef4444; font-weight: bold; }
            .half-day-status { color: #f59e0b; font-weight: bold; }
            .late-status { color: #3b82f6; font-weight: bold; }
            .leave-status { color: #9333ea; font-weight: bold; }
            .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
            .summary-total { background-color: #ecfdf5; font-weight: bold; }
            .labor-header { background-color: #f0fdf4; }
            .labor-summary { background-color: #f0fdf4; font-weight: bold; }
            .labor-spacer { height: 10px; border: none; }
            .summary-box { display: inline-block; padding: 15px; margin: 10px; min-width: 120px; text-align: center; border-radius: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .summary-number { font-size: 24px; font-weight: bold; margin: 5px 0; }
            .summary-label { font-size: 14px; color: #666; }
            .page-break { page-break-after: always; height: 0; }
          </style>
        </head>
        <body>
          <h1>Attendance Report</h1>
          <div class="site-info">
            <p><strong>Site:</strong> ${siteName}</p>
            <p><strong>Date Generated:</strong> ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}</p>
            <p><strong>Total Labor:</strong> ${totalLabor}</p>
            ${filterInfo}
          </div>
          
          <div style="text-align: center; margin: 20px 0;">
            <div class="summary-box" style="background-color: #ecfdf5;">
              <div class="summary-number">₹${totalPayout.toFixed(2)}</div>
              <div class="summary-label">Total Payout</div>
            </div>
            <div class="summary-box" style="background-color: #f0f9ff;">
              <div class="summary-number">${totalDays.toFixed(1)}</div>
              <div class="summary-label">Total Worker Days</div>
            </div>
            <div class="summary-box" style="background-color: #f9fafb;">
              <div class="summary-number">${totalLabor}</div>
              <div class="summary-label">Total Workers</div>
            </div>
          </div>
          
          <h2>Summary</h2>
          <table>
            <thead>
              <tr>
                <th>Name</th>
                <th>Category</th>
                <th>Daily Wage</th>
                <th>Days Worked</th>
                <th>Hours Worked</th>
                <th>Total Payout</th>
              </tr>
            </thead>
            <tbody>
              ${summaryRows}
              <tr class="summary-total">
                <td colspan="3">Total</td>
                <td>${totalDays.toFixed(1)}</td>
                <td>${totalHours}</td>
                <td>₹${totalPayout.toFixed(2)}</td>
              </tr>
            </tbody>
          </table>
          
          <div class="page-break"></div>
          
          <h2>Detailed Attendance</h2>
          <table>
            <thead>
              <tr>
                <th>Date</th>
                <th>Status</th>
                <th>Hours Worked</th>
                <th>Payout</th>
              </tr>
            </thead>
            <tbody>
              ${detailedRows.join('')}
            </tbody>
          </table>
          
          <div class="footer">
            <p>Generated by InfraTask App</p>
          </div>
        </body>
      </html>
    `;

    // Generate a filename with the date
    const date = new Date().toISOString().split('T')[0];
    const sanitizedSiteName = siteName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    
    // Create a PDF file using expo-print
    const { uri } = await Print.printToFileAsync({ 
      html: htmlContent,
      base64: false
    });
    
    // Generate a better filename
    const newFileUri = FileSystem.documentDirectory + `${sanitizedSiteName}_attendance_report_${date}.pdf`;
    
    // Copy to a more appropriate location with a cleaner filename
    await FileSystem.copyAsync({
      from: uri,
      to: newFileUri
    });
    
    // Delete the temporary file
    await FileSystem.deleteAsync(uri, { idempotent: true });
    
    // Share the file
    if (Platform.OS === 'ios' || Platform.OS === 'android') {
      await Sharing.shareAsync(newFileUri, {
        mimeType: 'application/pdf',
        UTI: 'com.adobe.pdf'
      });
    }
    
    return newFileUri;
  } catch (error) {
    console.error('Error exporting attendance report to PDF:', error);
    throw error;
  }
} 