import * as Application from 'expo-application';
import { Alert, Platform } from 'react-native';
// Dynamic import for development manager to avoid circular dependencies
import { nativeInAppUpdateManager, NativeUpdateInfo } from './nativeInAppUpdate';
import { playStoreUpdateManager, UpdateInfo } from './playStoreUpdates';

export interface ComprehensiveUpdateInfo {
  updateAvailable: boolean;
  version?: string;
  versionCode?: number;
  mandatory?: boolean;
  releaseNotes?: string;
  updateType: 'immediate' | 'flexible';
  source: 'native' | 'custom';
  nativeInfo?: NativeUpdateInfo;
}

export interface UpdateManagerConfig {
  enableNativeUpdates: boolean;
  enableCustomUpdates: boolean;
  prioritizeNativeUpdates: boolean;
  autoCheckInterval: number; // in milliseconds
  staleDaysForImmediate: number; // days after which to force immediate update
  highPriorityThreshold: number; // priority threshold for immediate updates
}

class ComprehensiveUpdateManager {
  private static instance: ComprehensiveUpdateManager;
  private config: UpdateManagerConfig = {
    enableNativeUpdates: true,
    enableCustomUpdates: true,
    prioritizeNativeUpdates: true,
    autoCheckInterval: 24 * 60 * 60 * 1000, // 24 hours
    staleDaysForImmediate: 7, // 7 days
    highPriorityThreshold: 4, // priority 4 or higher
  };

  private constructor() {}

  static getInstance(): ComprehensiveUpdateManager {
    if (!ComprehensiveUpdateManager.instance) {
      ComprehensiveUpdateManager.instance = new ComprehensiveUpdateManager();
    }
    return ComprehensiveUpdateManager.instance;
  }

  /**
   * Configure the update manager
   */
  configure(config: Partial<UpdateManagerConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Initialize the update manager
   */
  async initialize(): Promise<boolean> {
    if (Platform.OS !== 'android') {
      console.log('Comprehensive update manager: Android only');
      return false;
    }

    // In development builds, use development update manager
    if (__DEV__) {
      console.log('Development build detected - using development update manager');
      this.config.enableNativeUpdates = false;
      this.config.enableCustomUpdates = true;
      try {
        const { developmentUpdateManager } = await import('./developmentUpdateManager');
        await developmentUpdateManager.initialize();
      } catch (error) {
        console.error('Failed to load development update manager:', error);
      }
      return true;
    }

    let nativeInitialized = false;

    if (this.config.enableNativeUpdates) {
      try {
        nativeInitialized = await nativeInAppUpdateManager.initialize();
        console.log('Native in-app updates initialized:', nativeInitialized);
      } catch (error) {
        console.error('Failed to initialize native updates:', error);
      }
    }

    return nativeInitialized || this.config.enableCustomUpdates;
  }

  /**
   * Check for updates using both native and custom methods
   */
  async checkForUpdates(forceCheck: boolean = false): Promise<ComprehensiveUpdateInfo | null> {
    if (Platform.OS !== 'android') {
      return null;
    }

    // In development mode, use development update manager
    if (__DEV__) {
      try {
        const { developmentUpdateManager } = await import('./developmentUpdateManager');
        const mockUpdateInfo = await developmentUpdateManager.checkForUpdates(forceCheck);
        if (mockUpdateInfo) {
          return {
            updateAvailable: true,
            version: mockUpdateInfo.version,
            versionCode: mockUpdateInfo.versionCode,
            mandatory: mockUpdateInfo.mandatory,
            releaseNotes: mockUpdateInfo.releaseNotes,
            updateType: mockUpdateInfo.updateType,
            source: 'custom'
          };
        }
      } catch (error) {
        console.error('Error with development update manager:', error);
      }
      return null;
    }

    let nativeUpdateInfo: NativeUpdateInfo | null = null;
    let customUpdateInfo: UpdateInfo | null = null;

    // Check native updates first if enabled and prioritized
    if (this.config.enableNativeUpdates && this.config.prioritizeNativeUpdates) {
      try {
        if (nativeInAppUpdateManager.isInitialized()) {
          nativeUpdateInfo = await nativeInAppUpdateManager.checkForUpdate();
        }
      } catch (error) {
        console.error('Error checking native updates:', error);
      }
    }

    // Check custom updates if native didn't find anything or if not prioritized
    if (this.config.enableCustomUpdates && (!nativeUpdateInfo?.updateAvailable || !this.config.prioritizeNativeUpdates)) {
      try {
        const result = await playStoreUpdateManager.checkForUpdates(forceCheck);
        if (result.updateAvailable && result.updateInfo) {
          customUpdateInfo = result.updateInfo;
        }
      } catch (error) {
        console.error('Error checking custom updates:', error);
      }
    }

    // Check native updates if custom didn't find anything and native wasn't checked yet
    if (this.config.enableNativeUpdates && !this.config.prioritizeNativeUpdates && !nativeUpdateInfo) {
      try {
        if (nativeInAppUpdateManager.isInitialized()) {
          nativeUpdateInfo = await nativeInAppUpdateManager.checkForUpdate();
        }
      } catch (error) {
        console.error('Error checking native updates (fallback):', error);
      }
    }

    // Determine which update to use
    if (nativeUpdateInfo?.updateAvailable) {
      return this.createComprehensiveUpdateInfo(nativeUpdateInfo, 'native');
    } else if (customUpdateInfo) {
      return this.createComprehensiveUpdateInfo(customUpdateInfo, 'custom');
    }

    return null;
  }

  /**
   * Create comprehensive update info from native or custom info
   */
  private createComprehensiveUpdateInfo(
    updateInfo: NativeUpdateInfo | UpdateInfo,
    source: 'native' | 'custom'
  ): ComprehensiveUpdateInfo {
    if (source === 'native') {
      const nativeInfo = updateInfo as NativeUpdateInfo;
      const updateType = this.determineUpdateType(nativeInfo);

      return {
        updateAvailable: true,
        versionCode: nativeInfo.availableVersionCode,
        updateType,
        source: 'native',
        nativeInfo,
        mandatory: updateType === 'immediate',
      };
    } else {
      const customInfo = updateInfo as UpdateInfo;
      return {
        updateAvailable: true,
        version: customInfo.version,
        versionCode: customInfo.versionCode,
        mandatory: customInfo.mandatory,
        releaseNotes: customInfo.releaseNotes,
        updateType: customInfo.mandatory ? 'immediate' : 'flexible',
        source: 'custom',
      };
    }
  }

  /**
   * Determine update type based on native update info
   */
  private determineUpdateType(nativeInfo: NativeUpdateInfo): 'immediate' | 'flexible' {
    // Force immediate if update priority is high
    if (nativeInfo.updatePriority && nativeInfo.updatePriority >= this.config.highPriorityThreshold) {
      return 'immediate';
    }

    // Force immediate if app is stale for too long
    if (nativeInfo.clientVersionStalenessDays && nativeInfo.clientVersionStalenessDays >= this.config.staleDaysForImmediate) {
      return 'immediate';
    }

    // Default to flexible if immediate is not allowed
    if (!nativeInfo.immediateUpdateAllowed) {
      return 'flexible';
    }

    // Default to flexible for better UX
    return 'flexible';
  }

  /**
   * Start update flow based on the update info
   */
  async startUpdateFlow(updateInfo: ComprehensiveUpdateInfo): Promise<boolean> {
    try {
      if (updateInfo.source === 'native' && nativeInAppUpdateManager.isInitialized()) {
        if (updateInfo.updateType === 'immediate') {
          return await nativeInAppUpdateManager.startImmediateUpdate();
        } else {
          return await nativeInAppUpdateManager.startFlexibleUpdate();
        }
      } else {
        // Use custom update flow (redirect to Play Store)
        await playStoreUpdateManager.startUpdateFlow(updateInfo.updateType);
        return true;
      }
    } catch (error) {
      console.error('Error starting update flow:', error);
      return false;
    }
  }

  /**
   * Show update prompt with appropriate UI
   */
  showUpdatePrompt(updateInfo: ComprehensiveUpdateInfo): void {
    const title = updateInfo.updateType === 'immediate' ? 'Update Required' : 'Update Available';
    const message = updateInfo.updateType === 'immediate'
      ? 'This update is required to continue using the app.'
      : 'A new version is available with improvements and bug fixes.';

    const buttons = updateInfo.updateType === 'immediate'
      ? [{ text: 'Update Now', onPress: () => this.startUpdateFlow(updateInfo) }]
      : [
          { text: 'Later', style: 'cancel' as const },
          { text: 'Update', onPress: () => this.startUpdateFlow(updateInfo) }
        ];

    Alert.alert(title, message, buttons, { cancelable: updateInfo.updateType !== 'immediate' });
  }

  /**
   * Check and prompt for updates automatically
   */
  async checkAndPromptForUpdates(): Promise<void> {
    try {
      // In development mode, use development update manager
      if (__DEV__) {
        try {
          const { developmentUpdateManager } = await import('./developmentUpdateManager');
          await developmentUpdateManager.checkAndPromptForUpdates();
        } catch (error) {
          console.error('Error with development update manager:', error);
        }
        return;
      }

      const updateInfo = await this.checkForUpdates();
      if (updateInfo) {
        this.showUpdatePrompt(updateInfo);
      }
    } catch (error) {
      console.error('Error in checkAndPromptForUpdates:', error);
    }
  }

  /**
   * Complete flexible update (for native updates)
   */
  async completeFlexibleUpdate(): Promise<boolean> {
    if (nativeInAppUpdateManager.isInitialized()) {
      try {
        return await nativeInAppUpdateManager.completeFlexibleUpdate();
      } catch (error) {
        console.error('Error completing flexible update:', error);
        return false;
      }
    }
    return false;
  }

  /**
   * Get current app version info
   */
  getCurrentVersion() {
    return {
      version: Application.nativeApplicationVersion || '1.0.0',
      versionCode: Application.nativeBuildVersion ? parseInt(Application.nativeBuildVersion) : 1,
    };
  }

  /**
   * Add event listeners for native update events
   */
  addUpdateEventListeners(): void {
    if (!nativeInAppUpdateManager.isInitialized()) return;

    nativeInAppUpdateManager.addEventListener('onUpdateDownloaded', () => {
      Alert.alert(
        'Update Downloaded',
        'The update has been downloaded. Restart the app to apply the update.',
        [
          { text: 'Later', style: 'cancel' },
          { text: 'Restart Now', onPress: () => this.completeFlexibleUpdate() }
        ]
      );
    });

    nativeInAppUpdateManager.addEventListener('onUpdateFailed', (data) => {
      console.error('Update failed:', data);
      Alert.alert('Update Failed', 'The update could not be installed. Please try again later.');
    });

    nativeInAppUpdateManager.addEventListener('onUpdateInstalled', () => {
      console.log('Update installed successfully');
    });
  }

  /**
   * Remove all event listeners
   */
  removeEventListeners(): void {
    nativeInAppUpdateManager.removeAllListeners();
  }
}

// Export singleton instance
export const comprehensiveUpdateManager = ComprehensiveUpdateManager.getInstance();
