-- Create sub_contractor_laborers table
CREATE TABLE IF NOT EXISTS sub_contractor_laborers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    full_name VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    sub_contractor_id UUID NOT NULL REFERENCES sub_contractors(id) ON DELETE CASCADE,
    site_id UUID NOT NULL REFERENCES sites(id) ON DELETE CASCADE,
    category VARCHAR(100),
    remarks TEXT,
    daily_wage DECIMAL(10,2),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_sub_contractor_laborers_contractor_id ON sub_contractor_laborers(sub_contractor_id);
CREATE INDEX IF NOT EXISTS idx_sub_contractor_laborers_site_id ON sub_contractor_laborers(site_id);
CREATE INDEX IF NOT EXISTS idx_sub_contractor_laborers_active ON sub_contractor_laborers(is_active);

-- Enable Row Level Security (RLS)
ALTER TABLE sub_contractor_laborers ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only access laborers for sites they have access to
CREATE POLICY "Users can view sub-contractor laborers for their sites" ON sub_contractor_laborers
    FOR SELECT USING (
        site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert sub-contractor laborers for their sites" ON sub_contractor_laborers
    FOR INSERT WITH CHECK (
        site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update sub-contractor laborers for their sites" ON sub_contractor_laborers
    FOR UPDATE USING (
        site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete sub-contractor laborers for their sites" ON sub_contractor_laborers
    FOR DELETE USING (
        site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    );

-- Create trigger for updated_at
CREATE TRIGGER update_sub_contractor_laborers_updated_at 
    BEFORE UPDATE ON sub_contractor_laborers 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create attendance table for sub-contractor laborers
CREATE TABLE IF NOT EXISTS sub_contractor_laborer_attendance (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    laborer_id UUID NOT NULL REFERENCES sub_contractor_laborers(id) ON DELETE CASCADE,
    sub_contractor_id UUID NOT NULL REFERENCES sub_contractors(id) ON DELETE CASCADE,
    site_id UUID NOT NULL REFERENCES sites(id) ON DELETE CASCADE,
    attendance_date DATE NOT NULL,
    status VARCHAR(20) NOT NULL CHECK (status IN ('present', 'absent', 'half_day', 'overtime')),
    overtime_hours DECIMAL(4,2) DEFAULT NULL,
    marked_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one attendance record per laborer per date
    UNIQUE(laborer_id, attendance_date)
);

-- Create indexes for sub-contractor laborer attendance
CREATE INDEX IF NOT EXISTS idx_sub_contractor_laborer_attendance_laborer_id ON sub_contractor_laborer_attendance(laborer_id);
CREATE INDEX IF NOT EXISTS idx_sub_contractor_laborer_attendance_contractor_id ON sub_contractor_laborer_attendance(sub_contractor_id);
CREATE INDEX IF NOT EXISTS idx_sub_contractor_laborer_attendance_site_id ON sub_contractor_laborer_attendance(site_id);
CREATE INDEX IF NOT EXISTS idx_sub_contractor_laborer_attendance_date ON sub_contractor_laborer_attendance(attendance_date);

-- Enable RLS for sub-contractor laborer attendance
ALTER TABLE sub_contractor_laborer_attendance ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for sub-contractor laborer attendance
CREATE POLICY "Users can view sub-contractor laborer attendance for their sites" ON sub_contractor_laborer_attendance
    FOR SELECT USING (
        site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert sub-contractor laborer attendance for their sites" ON sub_contractor_laborer_attendance
    FOR INSERT WITH CHECK (
        site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update sub-contractor laborer attendance for their sites" ON sub_contractor_laborer_attendance
    FOR UPDATE USING (
        site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete sub-contractor laborer attendance for their sites" ON sub_contractor_laborer_attendance
    FOR DELETE USING (
        site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    );

-- Create trigger for updated_at on laborer attendance
CREATE TRIGGER update_sub_contractor_laborer_attendance_updated_at 
    BEFORE UPDATE ON sub_contractor_laborer_attendance 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create view for easier querying of sub-contractor laborers with attendance
CREATE OR REPLACE VIEW sub_contractor_laborers_with_attendance AS
SELECT 
    scl.*,
    sc.name as contractor_name,
    sc.category as contractor_category
FROM sub_contractor_laborers scl
JOIN sub_contractors sc ON scl.sub_contractor_id = sc.id
WHERE scl.is_active = true;

-- Create view for sub-contractor attendance summary (derived from laborers)
CREATE OR REPLACE VIEW sub_contractor_attendance_summary AS
SELECT 
    sc.id as sub_contractor_id,
    sc.name as contractor_name,
    sc.site_id,
    scla.attendance_date,
    COUNT(CASE WHEN scla.status = 'present' THEN 1 END) as present_count,
    COUNT(CASE WHEN scla.status = 'absent' THEN 1 END) as absent_count,
    COUNT(CASE WHEN scla.status = 'half_day' THEN 1 END) as half_day_count,
    COUNT(CASE WHEN scla.status = 'overtime' THEN 1 END) as overtime_count,
    SUM(CASE WHEN scla.status = 'overtime' THEN scla.overtime_hours ELSE 0 END) as total_overtime_hours,
    COUNT(scl.id) as total_laborers,
    COUNT(scla.id) as marked_laborers,
    CASE 
        WHEN COUNT(scl.id) = 0 THEN 'no_laborers'
        WHEN COUNT(scla.id) = 0 THEN 'not_marked'
        WHEN COUNT(CASE WHEN scla.status = 'present' OR scla.status = 'overtime' THEN 1 END) = COUNT(scl.id) THEN 'present'
        WHEN COUNT(CASE WHEN scla.status = 'absent' THEN 1 END) = COUNT(scl.id) THEN 'absent'
        ELSE 'mixed'
    END as overall_status
FROM sub_contractors sc
LEFT JOIN sub_contractor_laborers scl ON sc.id = scl.sub_contractor_id AND scl.is_active = true
LEFT JOIN sub_contractor_laborer_attendance scla ON scl.id = scla.laborer_id
GROUP BY sc.id, sc.name, sc.site_id, scla.attendance_date; 