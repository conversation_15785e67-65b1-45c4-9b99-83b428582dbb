Construction Site Management App Development Prompt
Project Overview
Create a comprehensive construction site management application using React Native with the following capabilities:
•	Authentication and user profile management
•	Dashboard with site analytics
•	Site management and tracking
•	Task management with role-based permissions
•	Attendance tracking for team members
•	Inventory management
•	Transaction tracking between users
•	Profile management

Tech Stack
•	Frontend: React Native
•	Backend: Supabase for database and authentication
•	Theme: Support for both light and dark mode
•	Primary Color: #f97316 (Infratask primary color)

Authentication Flow
1.	Phone number entry screen
2.	OTP verification screen
3.	Profile creation with: 
o	Profile image upload
o	Full name
o	Email
o	City
o	Role selection

Database Structure
Create the following tables in Supabase:
1.	profiles:
o	id (UUID)
o	user_id (foreign key to auth.users)
o	full_name
o	email
o	city
o	role
o	phone_number
o	profile_image_url

2.	sites:
o	id (UUID)
o	name (required)
o	organization_name (required)
o	site_image_url (optional)
o	created_at (timestamp)
o	updated_at (timestamp)
o	status (active/completed)
o	owner_id (foreign key to profiles) - references the user who created the site

3.	site_members:
o	id (UUID)
o	site_id (foreign key to sites)
o	user_id (foreign key to profiles)
o	role (Super Admin, Admin, Member)
o	category (<PERSON>, Helper, etc.)
o	joined_at

4.	laborers:
o	id (UUID)
o	site_id (foreign key to sites)
o	full_name
o	phone_number
o	category (Mason, Helper, semi skilled helper, Plumber, Electrician, Interior, etc.)
o	remarks
o	created_by (foreign key to profiles)
o	created_at

5.	tasks:
o	id (UUID)
o	site_id (foreign key to sites)
o	name (required)
o	work_category (required)
o	status (pending, in progress, completed)
o	overall_progress (integer, percentage from 0-100)
o	due_date (date)
o	created_at (timestamp)
o	updated_at (timestamp)
o	created_by (foreign key to profiles)

6.	task_subcategories:
o	id (UUID)
o	task_id (foreign key to tasks)
o	name (required)
o	quantity (required)
o	completed_quantity (default 0)
o	unit_of_measure (required)
o	progress_percentage (integer, percentage from 0-100)
o	created_at (timestamp)
o	updated_at (timestamp)

7.	attendances:
o	id (UUID)
o	site_id (foreign key to sites)
o	laborer_id (foreign key to laborers)
o	date
o	status (P, A, H, OT)
o	marked_by (foreign key to profiles)
o	created_at
o	updated_at

8.	inventory:
o	id (UUID)
o	site_id (foreign key to sites)
o	material_name (required)
o	specifications (text)
o	stock (number, required)
o	uom (unit of measure, required)
o	price (decimal)
o	invoice_number (string)
o	invoice_image_url (string)
o	received_date (date, required)
o	created_at (timestamp)
o	updated_at (timestamp)
o	created_by (foreign key to profiles)

9.	transactions:
o	id (UUID)
o	transaction_type (income/expense)
o	amount
o	from_user_id (foreign key to profiles)
o	to_user_id (foreign key to profiles)
o	description
o	transaction_date
o	created_at

Screen Specifications
1. Authentication Screens
•	Phone Entry Screen
•	OTP Verification Screen
•	Profile Creation Screen

2. Dashboard Screen
•	Header: "Dashboard"
•	Analytics Section: 
o	Total Sites card
o	Total Workers card
o	Tasks in Progress percentage card
•	Middle Section: 
o	Task due dates highlights
o	Inventory stock alerts

3. My Sites Screen
•	Header: "My Sites"
•	List View: Container cards showing:
o	Site image
o	Site name
o	Created date
o	Last updated date
o	Status (active/completed)
•	Add Button: Floating action button to create new site
•	Create Site Screen:
o	Site name field (required)
o	Organization name field (required)
o	Site image upload (optional)
o	"Create Site" button

•	Special Functionality:
o	Any authenticated user can create a site
o	When a user creates a site, they are automatically: 
	Recorded as the site owner
	Assigned the "Super Admin" role in the site_members table
	Given full access to all site management features
o	After successful site creation, the app should: 
	Navigate back to My Sites page
	Fetch and display real-time data from Supabase
	Show the newly created site at the top of the list

4. Site Details Screen
•	Header: Site name with "Project Dashboard" subheading
•	Analytics Section: 
o	Total tasks percentage
o	Active tasks count
o	Workers count
o	Tasks in progress percentage
•	Tabs Section: 
o	Task Management
o	Attendance
o	Inventory
o	Team Members
o	Transactions
•	Report Section: 
o	Download options for: 
	Task reports (date range and category filters)
	Attendance reports (date range and category filters)
	Inventory reports (date range and category filters)

5. Task Management Screen
•	List view of tasks showing:
o	Task name
o	Work category
o	Status (with color indicators for pending, in progress, completed)
o	Overall progress bar showing percentage completion
o	Due date (with visual indicator for approaching/overdue dates)
o	Creation date
o	Edit and delete icons (based on role permissions)
•	Clicking on any task card navigates directly to the Update Task screen
•	Create Task Screen:
o	Task name field (required)
o	Work category dropdown (required) with options:
	Foundation Work
	Structural Work
	Masonry
	Roofing
	Electrical
	Plumbing
	HVAC
	Interior Finishing
	Exterior Finishing
	Landscaping
	Site Preparation
	Concrete Work
	Steel Work
	Carpentry
	Painting
	Flooring
	Windows and Doors
	Insulation
	Waterproofing
	Safety and Security
o	Status selection (pending, in progress, completed)
o	Due date field with calendar popup selector (required)
o	Subcategories Section:
	Header "Subcategories" with "Add Subcategory" button
	For each subcategory, display form fields: 
	Subcategory name field (required)
	Quantity field (required, numeric)
	Unit of measure dropdown (required) with options: 
	Square Meter
	Cubic Meter
	Linear Meter
	Kilogram
	Piece
	Lot
	Set
	Hour
	Day
	Week
	No.s
	Box
	Each
	Feet
	Running Feet
	Square Feet
	Tones
	Liter
	Cubic Feet
	Bag
	Other
	Remove button for each subcategory (except first one)
	User can add multiple subcategories dynamically
o	"Create Task" and "Cancel" buttons

•	Update Task Screen:
o	Same layout as Create Task screen but pre-populated with existing data
o	Overall task progress bar at the top showing percentage completion across all subcategories
o	Ability to edit task name, work category, and status
o	Due date field with calendar popup selector (pre-populated with existing due date)
o	Subcategories Section with Progress Tracking:
	Each subcategory shows: 
	Subcategory name (editable)
	Target quantity (editable)
	Completed quantity field (to enter how much work has been completed)
	Unit of measure (editable)
	Progress bar showing percentage completion for this subcategory
	Progress percentage label (calculated automatically)
	Ability to add new subcategories
	Ability to remove existing subcategories
o	"Save Changes" and "Cancel" buttons

o	Progress Calculation Logic:
	Each subcategory progress = (completed_quantity / quantity) * 100
	Overall task progress = average of all subcategory progress percentages
	Progress bars should update in real-time as user enters completed quantities
	Progress percentages should be rounded to nearest integer
	Task status should automatically update to "in progress" when overall progress > 0
	Task status should automatically update to "completed" when overall progress = 100

•	Role-based Permissions:
o	Member: View only, can see progress but cannot update
o	Admin: Create, update progress, no delete
o	Super Admin: Full control (create, update, delete)

•	Special Functionality:
o	Task progress updates are reflected in dashboard statistics
o	Tasks can be filtered by category, status, and due date
o	Tasks with approaching due dates appear in dashboard highlights
o	Visual indicators for overdue tasks

6. Attendance Screen
•	Calendar view at the top for date selection
•	"Add Labor" button to add new laborers to the site
•	List of laborers below calendar showing:
o	Full name
o	Attendance status buttons (P: Present, A: Absent, H: Half-day, OT: Overtime)
o	Delete icon on the far right

•	Add Labor Screen:
o	Full name field (required)
o	Phone number field (required)
o	Category dropdown (Mason, Helper, semi skilled helper, Plumber, Electrician, Interior, Site owner, etc.)
o	Remarks field (optional)
o	"Save" and "Cancel" buttons
•	When user clicks on a laborer name, display a bottom sheet with detailed information:
o	Full name (editable for Admin/Super Admin)
o	Phone number (editable for Admin/Super Admin)
o	Category (editable for Admin/Super Admin)
o	Remarks (editable for Admin/Super Admin)
o	"Save" and "Cancel" buttons for Admin/Super Admin

•	Role-based Permissions:
o	Member: View only, cannot modify attendance status or see the bottom sheet edit options
o	Admin: Can only modify current date's attendance, cannot edit past dates, can add/edit laborers
o	Super Admin: Full control over all dates and records

•	Special Functionality:
o	Laborers are specific to each site and not app users
o	When a laborer is deleted, they should still appear in past attendance records for reporting purposes
o	Attendance can be marked for each day using the status buttons
o	Attendance history should be viewable by date selection on the calendar

Sub Contractor Attendance Screen
•	Calendar view at the top for date selection
•	"Add Labor" button to add new laborers to the site
•	List of laborers below calendar showing:
o	Full name
o	Attendance status buttons (P: Present, A: Absent, H: Half-day, OT: Overtime)
o	Delete icon on the far right

•	Add Labor Screen:
o	Full name field (required)
o	Phone number field (required)
o	Category Field
o	Remarks field (optional)
o	"Save" and "Cancel" buttons

•	When user clicks on a laborer name, display a bottom sheet with detailed information:
o	Full name (editable for Admin/Super Admin)
o	Phone number (editable for Admin/Super Admin)
o	Category (editable for Admin/Super Admin)
o	Remarks (editable for Admin/Super Admin)
o	"Save" and "Cancel" buttons for Admin/Super Admin

•	Role-based Permissions:
o	Member: View only, cannot modify attendance status or see the bottom sheet edit options
o	Admin: Can only modify current date's attendance, cannot edit past dates, can add/edit laborers
o	Super Admin: Full control over all dates and records

•	Special Functionality:
o	Laborers are specific to each site and sub contractor and not app users
o	When a laborer is deleted, they should still appear in past attendance records for reporting purposes
o	Attendance can be marked for each day using the status buttons
o	Attendance history should be viewable by date selection on the calendar

7. Material Screen
•	List view of materials showing:
o	Material name
o	Specifications (truncated if too long)
o	Current stock level
o	UOM (Unit of Measure)
o	Price
o	Received date
o	Visual indicator for low stock items
o	Edit and delete icons (based on role permissions)
•	"Add Material" floating action button to add new materials

•	Add Material Screen:
o	Material name field (required)
o	Specifications field (optional, multi-line)
o	Stock quantity field (required, numeric)
o	UOM dropdown (required) with options: 
	No.s
	Box
	Each
	Meter
	Feet
	Running Feet
	Square Meter
	Square Feet
	Tones
	Liter
	Cubic Meter
	Cubic Feet
	Kilogram
	Bag
	Other
o	Price field (optional, decimal with currency symbol)
o	Invoice number field (optional)
o	Invoice image upload button (optional) 
	Supports image gallery selection or camera capture
	Shows thumbnail preview after upload
o	Received date field (required) 
	Default to current date
	Calendar popup for date selection
o	"Add Material" and "Cancel" buttons

Update Material Screen
•	Uses the same layout as the Add Material screen.
•	All input fields are pre-populated with existing material data.
•	All fields remain editable with the same validation rules applied as in the Add Material screen.
•	If an invoice image already exists, a thumbnail preview is displayed.
•	Includes an option to replace the existing invoice image with a new upload.
•	New field:
o	Update Stock — accepts a numeric input to increase or adjust material stock.
o	Validation: must be a non-negative number.
•	Stock Summary Card displayed below the form with:
o	Total Stock: current total stock before update
o	Updated Stock: value entered in the Update Stock field
o	Balance Stock: calculated in real-time as Total Stock + Updated Stock
•	Buttons:
o	Save Changes: saves all updates including stock and image changes
o	Cancel: discards changes and returns to the previous screen

•	Role-based Permissions:
o	Member: View only, can see material details
o	Admin: Create, update, delete materials
o	Super Admin: Full control
•	Special Functionality:
o	Low stock alerts appear on dashboard
o	Materials can be filtered by name, category, or date received
o	Search functionality for finding specific materials
o	Option to export inventory list as PDF or spreadsheet

8. Team Members Screen
•	List view of team members showing:
o	Profile image
o	Full name
o	Role (Super Admin, Admin, Member)
o	Category
o	Delete icon (not shown for site creator/Super Admin)
Add Team Member Screen
•	Search field to look up existing app users by name or phone number.
•	Search results display:
o	Profile image (if available)
o	Name
o	Phone number
If a matching user is found:
•	Tap to select the user.
•	Show:
o	Role selection dropdown with options: Admin, Member (Super Admin cannot be assigned)
o	Category selection dropdown with options: Mason, Helper, Semi-Skilled Helper, Plumber, Electrician, Interior, Site Owner
•	Buttons:
o	Add Member
o	Cancel
If no matching user is found:
•	Show message: "No user found. Invite them to join the app."
•	Display an Invite User section with:
o	Input field to enter phone number
o	Optional: enter name for personalized invite
o	Send Invite button — sends an SMS or WhatsApp message with a link to download the app and sign in
•	After sending, confirmation message: "Invitation sent successfully."

•	Role-based Permissions:
o	Member: View only, cannot add/edit/delete team members
o	Admin: Can add and manage other Members, cannot modify Super Admin or other Admins
o	Super Admin: Full control over team section including assigning Admin roles 
	Site creator is automatically the Super Admin and cannot be deleted from the site
	Only one Super Admin is allowed per site

•	Special Functionality:
o	When a user is added to a site, that site will appear in their My Sites page
o	Team members can view and interact with the site based on their assigned role permissions
o	When a team member is removed from a site, that site is unlinked from their My Sites page

9. Transaction Screen
•	List view of transactions
•	Add Transaction Screen: 
o	Transaction type (income/expense)
o	Amount
o	"Paid to" or "Received from" field with user search
o	Description
o	Date

10. Profile Screen
•	Display and edit profile details
•	Sign out button
•	Delete account button
•	Footer links: 
o	Privacy Policy
o	Legal
o	Terms and Conditions
o	Support

Special Requirements
Team Member Management
•	Site creator is automatically assigned Super Admin role

Transactions
•	When User A adds an expense paid to User B, it should appear as income for User B
•	When User A adds income received from User B, it should appear as expense for User B
•	Make sure user selection is optional, bypass the if user is not slected and the record should be created in transactions table

Interface
•	Implement both light and dark mode
•	Follow the design mockups provided
•	Use primary color #f97316 throughout the app

Development Approach
1.	Set up Supabase project and create database tables
2.	Implement authentication flow with phone number and OTP
3.	Create profile management system
4.	Build site management screens
5.	Implement task, attendance, inventory, and team member features
6.	Add transaction functionality
7.	Implement role-based permissions system
8.	Add report generation capabilities
9.	Implement UI theming for light/dark mode
10.	Test all features and user flows

Expected Deliverables
•	Complete React Native codebase
•	Supabase database setup
•	Functioning app with all specified features

Supabsae :
Here is Project url: https://vsnhscndlifvaptwdfsw.supabase.co
Here is anon - API Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.0MxRajYjYvdoZePb15nv32A7a6s5rX-pn7DRKzSTAAI

Subscription Model Requirements
Overview
Transform the existing freemium construction management application into a subscription-based SaaS platform with tiered pricing, user management, and free trial functionality.
Current State

Application is running in freemium mode
Full access provided to phone-authenticated users
Need to implement subscription-based access control

Subscription Plans
1. Standard Plan

Price: ₹199/month
Base Features:

1 user included
All core construction management features
Project management tools
Basic reporting and analytics


Add-on Options:

Additional users: ₹199 per user per month
No limit on additional users that can be added



2. Premium Plan

Price: ₹999/month
Features:

Unlimited users
All core construction management features
Advanced reporting and analytics
Priority customer support
Advanced integrations



3. Free Trial

Duration: 14 days
Access: Full Premium plan features
Auto-assignment: All new signups automatically get 14-day trial
Conversion: Trial users can choose any paid plan after expiry

Technical Requirements
Authentication & User Management

Maintain existing phone authentication system
Extend user model to include subscription information
Implement role-based access control for subscription tiers

Subscription Management System
Database Schema Extensions
sql-- Subscription Plans Table
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    billing_cycle VARCHAR(20) DEFAULT 'monthly',
    max_users INT NULL, -- NULL for unlimited
    features JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- User Subscriptions Table
CREATE TABLE user_subscriptions (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    plan_id UUID REFERENCES subscription_plans(id),
    status VARCHAR(20) DEFAULT 'active', -- active, cancelled, expired, trial
    current_period_start TIMESTAMP,
    current_period_end TIMESTAMP,
    trial_end TIMESTAMP,
    additional_users INT DEFAULT 0,
    payment_method_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Subscription Users (for team management)
CREATE TABLE subscription_users (
    id UUID PRIMARY KEY,
    subscription_id UUID REFERENCES user_subscriptions(id),
    user_id UUID REFERENCES users(id),
    role VARCHAR(50) DEFAULT 'member', -- owner, admin, member
    invited_by UUID REFERENCES users(id),
    invited_at TIMESTAMP,
    joined_at TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active', -- active, invited, removed
    created_at TIMESTAMP DEFAULT NOW()
);
API Endpoints
Subscription Management
GET /api/subscriptions/plans - Get all available plans
GET /api/subscriptions/current - Get current user's subscription
POST /api/subscriptions/subscribe - Subscribe to a plan
PUT /api/subscriptions/upgrade - Upgrade/downgrade plan
POST /api/subscriptions/cancel - Cancel subscription
POST /api/subscriptions/reactivate - Reactivate cancelled subscription
User Management
GET /api/subscriptions/users - Get all users in subscription
POST /api/subscriptions/users/invite - Invite new user to subscription
DELETE /api/subscriptions/users/{userId} - Remove user from subscription
PUT /api/subscriptions/users/{userId}/role - Update user role
Trial Management
POST /api/trial/start - Start free trial (auto-triggered on signup)
GET /api/trial/status - Get trial status
POST /api/trial/convert - Convert trial to paid subscription
Frontend Components
Subscription Management Page
Create a comprehensive subscription management interface with the following sections:
Current Plan Overview

Display current plan name and price
Show billing cycle and next billing date
Display plan features and usage limits
Show trial status if applicable

Plan Upgrade/Downgrade

Compare plans side-by-side
Allow seamless plan switching
Show prorated charges/credits
Confirmation dialogs for plan changes

User Management (Standard)

Display current user count and additional user cost
Add user functionality with email invitation
Remove user functionality with confirmation
Show pending invitations
Calculate and display monthly cost breakdown

Team Management (Premium)

List all team members with roles
Invite unlimited users via email
Manage user roles (owner, admin, member)
Remove team members
Bulk user operations

Billing & Payment

Payment method management
Billing history
Invoice downloads
Payment failure notifications
Auto-renewal settings

Trial Experience

Trial countdown banner in app header
Trial expiry notifications (7 days, 3 days, 1 day, expired)
Plan selection modal on trial expiry
Seamless trial-to-paid conversion

Access Control Implementation
Access Control Implementation - Screen Restrictions
Sites and Transactions Tab Protection
Frontend Route Guards
javascript// Route guard component for Sites and Transactions tabs
const SubscriptionGuard = ({ children, requiredFeature }) => {
    const { subscription, loading } = useSubscription();
    const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
    
    const handleAccessAttempt = () => {
        if (loading) return;
        
        if (!subscription || subscription.status === 'expired' || subscription.status === 'trial_expired') {
            setShowSubscriptionModal(true);
            return;
        }
        
        // Allow access for active subscriptions
        if (subscription.status === 'active' || subscription.status === 'trial') {
            return children;
        }
    };
    
    return (
        <>
            <div onClick={handleAccessAttempt}>
                {subscription?.status === 'active' || subscription?.status === 'trial' 
                    ? children 
                    : <div className="cursor-pointer">{children}</div>
                }
            </div>
            
            {showSubscriptionModal && (
                <SubscriptionModal 
                    isOpen={showSubscriptionModal}
                    onClose={() => setShowSubscriptionModal(false)}
                />
            )}
        </>
    );
};
Tab Click Interceptor
javascript// Intercept clicks on Sites and Transactions tabs
const TabNavigation = () => {
    const { subscription } = useSubscription();
    const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
    
    const handleTabClick = (tabName, event) => {
        if ((tabName === 'sites' || tabName === 'transactions')) {
            if (!subscription || !['active', 'trial'].includes(subscription.status)) {
                event.preventDefault();
                event.stopPropagation();
                setShowSubscriptionModal(true);
                return;
            }
        }
        // Allow normal navigation for subscribed users
    };
    
    return (
        <div className="tab-navigation">
            <Tab 
                name="dashboard" 
                onClick={(e) => handleTabClick('dashboard', e)}
            >
                Dashboard
            </Tab>
            
            <Tab 
                name="sites" 
                onClick={(e) => handleTabClick('sites', e)}
                className={!subscription?.status ? 'restricted-tab' : ''}
            >
                Sites {!subscription?.status && <LockIcon />}
            </Tab>
            
            <Tab 
                name="transactions" 
                onClick={(e) => handleTabClick('transactions', e)}
                className={!subscription?.status ? 'restricted-tab' : ''}
            >
                Transactions {!subscription?.status && <LockIcon />}
            </Tab>
            
            <SubscriptionModal 
                isOpen={showSubscriptionModal}
                onClose={() => setShowSubscriptionModal(false)}
            />
        </div>
    );
};
Subscription Popup Modal
javascript// Subscription popup modal component
const SubscriptionModal = ({ isOpen, onClose }) => {
    const navigate = useNavigate();
    
    const handlePlanSelect = (planType) => {
        navigate(`/subscribe/${planType}`);
        onClose();
    };
    
    if (!isOpen) return null;
    
    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="subscription-modal" onClick={(e) => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>Subscription Required</h2>
                    <button className="close-btn" onClick={onClose}>×</button>
                </div>
                
                <div className="modal-content">
                    <p>Access to Sites and Transactions requires an active subscription.</p>
                    
                    <div className="plans-grid">
                        <div className="plan-card">
                            <h3>Standard</h3>
                            <div className="price">₹249<span>/month</span></div>
                            <ul>
                                <li>1 user included</li>
                                <li>All features</li>
                                <li>Add users for ₹249 each</li>
                            </ul>
                            <button 
                                className="select-plan-btn"
                                onClick={() => handlePlanSelect('pay-as-you-go')}
                            >
                                Choose Plan
                            </button>
                        </div>
                        
                        <div className="plan-card premium">
                            <h3>Premium</h3>
                            <div className="price">₹999<span>/month</span></div>
                            <ul>
                                <li>Unlimited users</li>
                                <li>All features</li>
                                <li>Priority support</li>
                            </ul>
                            <button 
                                className="select-plan-btn"
                                onClick={() => handlePlanSelect('premium')}
                            >
                                Choose Plan
                            </button>
                        </div>
                    </div>
                    
                    <div className="trial-info">
                        <p>🎉 Start with a 14-day free trial</p>
                    </div>
                </div>
            </div>
        </div>
    );
};
Preserve Original Screen Functionality
Conditional Rendering Wrapper
javascript// Wrapper to preserve original functionality while adding restrictions
const ProtectedScreen = ({ children, screenName }) => {
    const { subscription, loading } = useSubscription();
    
    // Don't modify the original component, just wrap it
    if (loading) {
        return <LoadingSpinner />;
    }
    
    // Allow access for subscribed users - render original component unchanged
    if (subscription?.status === 'active' || subscription?.status === 'trial') {
        return children;
    }
    
    // For non-subscribed users, show the restriction overlay
    return (
        <div className="protected-screen-wrapper">
            {/* Original screen rendered but disabled */}
            <div className="screen-disabled-overlay">
                {children}
            </div>
            
            {/* Subscription prompt overlay */}
            <div className="subscription-prompt-overlay">
                <div className="subscription-prompt">
                    <LockIcon size={48} />
                    <h3>Subscription Required</h3>
                    <p>Access to {screenName} requires an active subscription</p>
                    <button 
                        className="subscribe-btn"
                        onClick={() => navigate('/subscribe')}
                    >
                        View Plans
                    </button>
                </div>
            </div>
        </div>
    );
};

// Usage in Sites screen
const SitesScreen = () => {
    return (
        <ProtectedScreen screenName="Sites">
            {/* Original Sites component - unchanged */}
            <OriginalSitesComponent />
        </ProtectedScreen>
    );
};

// Usage in Transactions screen
const TransactionsScreen = () => {
    return (
        <ProtectedScreen screenName="Transactions">
            {/* Original Transactions component - unchanged */}
            <OriginalTransactionsComponent />
        </ProtectedScreen>
    );
};
Manage Subscription Page
Complete Subscription Management Interface
Main Subscription Management Component
javascriptconst ManageSubscriptionPage = () => {
    const { subscription, loading, refetch } = useSubscription();
    const [activeTab, setActiveTab] = useState('overview');
    
    if (loading) return <LoadingSpinner />;
    
    return (
        <div className="manage-subscription-page">
            <div className="page-header">
                <h1>Manage Subscription</h1>
                <p>Control your plan, billing, and team members</p>
            </div>
            
            <div className="subscription-tabs">
                <button 
                    className={activeTab === 'overview' ? 'active' : ''}
                    onClick={() => setActiveTab('overview')}
                >
                    Overview
                </button>
                <button 
                    className={activeTab === 'billing' ? 'active' : ''}
                    onClick={() => setActiveTab('billing')}
                >
                    Billing
                </button>
                <button 
                    className={activeTab === 'team' ? 'active' : ''}
                    onClick={() => setActiveTab('team')}
                >
                    Team Members
                </button>
            </div>
            
            <div className="tab-content">
                {activeTab === 'overview' && <OverviewTab subscription={subscription} />}
                {activeTab === 'billing' && <BillingTab subscription={subscription} />}
                {activeTab === 'team' && <TeamMembersTab subscription={subscription} refetch={refetch} />}
            </div>
        </div>
    );
};
Overview Tab Component
javascriptconst OverviewTab = ({ subscription }) => {
    const [showCancelModal, setShowCancelModal] = useState(false);
    const [showUpgradeModal, setShowUpgradeModal] = useState(false);
    
    return (
        <div className="overview-tab">
            <div className="current-plan-card">
                <div className="plan-info">
                    <h3>{subscription.plan_name}</h3>
                    <div className="plan-price">
                        ₹{subscription.amount}/month
                    </div>
                    <div className="plan-status">
                        Status: <span className={`status ${subscription.status}`}>
                            {subscription.status}
                        </span>
                    </div>
                </div>
                
                <div className="plan-details">
                    <p><strong>Next billing:</strong> {formatDate(subscription.next_billing_date)}</p>
                    <p><strong>Users:</strong> {subscription.current_users}/{subscription.plan_type === 'premium' ? 'Unlimited' : subscription.max_users}</p>
                    {subscription.trial_end && (
                        <p><strong>Trial ends:</strong> {formatDate(subscription.trial_end)}</p>
                    )}
                </div>
                
                <div className="plan-actions">
                    {subscription.plan_type === 'pay_as_you_go' && (
                        <button 
                            className="upgrade-btn"
                            onClick={() => setShowUpgradeModal(true)}
                        >
                            Upgrade to Premium
                        </button>
                    )}
                    
                    <button 
                        className="cancel-btn"
                        onClick={() => setShowCancelModal(true)}
                    >
                        Cancel Subscription
                    </button>
                </div>
            </div>
            
            <div className="usage-summary">
                <h4>Current Usage</h4>
                <div className="usage-stats">
                    <div className="stat">
                        <span className="label">Active Projects:</span>
                        <span className="value">{subscription.usage?.active_projects || 0}</span>
                    </div>
                    <div className="stat">
                        <span className="label">Total Sites:</span>
                        <span className="value">{subscription.usage?.total_sites || 0}</span>
                    </div>
                    <div className="stat">
                        <span className="label">Monthly Transactions:</span>
                        <span className="value">{subscription.usage?.monthly_transactions || 0}</span>
                    </div>
                </div>
            </div>
            
            <CancelSubscriptionModal 
                isOpen={showCancelModal}
                onClose={() => setShowCancelModal(false)}
                subscription={subscription}
            />
            
            <UpgradeModal 
                isOpen={showUpgradeModal}
                onClose={() => setShowUpgradeModal(false)}
                currentPlan={subscription}
            />
        </div>
    );
};
Team Members Management
javascriptconst TeamMembersTab = ({ subscription, refetch }) => {
    const [teamMembers, setTeamMembers] = useState([]);
    const [showInviteModal, setShowInviteModal] = useState(false);
    const [loading, setLoading] = useState(true);
    
    useEffect(() => {
        fetchTeamMembers();
    }, []);
    
    const fetchTeamMembers = async () => {
        try {
            const response = await fetch('/api/subscriptions/users');
            const data = await response.json();
            setTeamMembers(data.users);
        } catch (error) {
            console.error('Failed to fetch team members:', error);
        } finally {
            setLoading(false);
        }
    };
    
    const removeUser = async (userId) => {
        if (!confirm('Are you sure you want to remove this user?')) return;
        
        try {
            await fetch(`/api/subscriptions/users/${userId}`, {
                method: 'DELETE'
            });
            
            fetchTeamMembers();
            refetch();
            toast.success('User removed successfully');
        } catch (error) {
            toast.error('Failed to remove user');
        }
    };
    
    const canAddUsers = () => {
        if (subscription.plan_type === 'premium') return true;
        return teamMembers.length < subscription.max_users;
    };
    
    return (
        <div className="team-members-tab">
            <div className="team-header">
                <h3>Team Members ({teamMembers.length})</h3>
                {canAddUsers() && (
                    <button 
                        className="invite-btn"
                        onClick={() => setShowInviteModal(true)}
                    >
                        + Invite User
                    </button>
                )}
            </div>
            
            {subscription.plan_type === 'pay_as_you_go' && (
                <div className="user-limit-info">
                    <p>Current users: {teamMembers.length}/{subscription.max_users}</p>
                    <p>Additional users: ₹249/month each</p>
                </div>
            )}
            
            <div className="team-members-list">
                {loading ? (
                    <LoadingSpinner />
                ) : teamMembers.length === 0 ? (
                    <div className="empty-state">
                        <p>No team members yet. Invite your first team member!</p>
                    </div>
                ) : (
                    teamMembers.map(member => (
                        <div key={member.id} className="member-card">
                            <div className="member-info">
                                <div className="member-avatar">
                                    {member.name?.charAt(0).toUpperCase()}
                                </div>
                                <div className="member-details">
                                    <h4>{member.name}</h4>
                                    <p>{member.email}</p>
                                    <span className={`role ${member.role}`}>
                                        {member.role}
                                    </span>
                                </div>
                            </div>
                            
                            <div className="member-actions">
                                <span className={`status ${member.status}`}>
                                    {member.status}
                                </span>
                                {member.role !== 'owner' && (
                                    <button 
                                        className="remove-btn"
                                        onClick={() => removeUser(member.id)}
                                    >
                                        Remove
                                    </button>
                                )}
                            </div>
                        </div>
                    ))
                )}
            </div>
            
            <InviteUserModal 
                isOpen={showInviteModal}
                onClose={() => setShowInviteModal(false)}
                onSuccess={() => {
                    fetchTeamMembers();
                    refetch();
                }}
                subscription={subscription}
            />
        </div>
    );
};
Invite User Modal
javascriptconst InviteUserModal = ({ isOpen, onClose, onSuccess, subscription }) => {
    const [email, setEmail] = useState('');
    const [role, setRole] = useState('member');
    const [loading, setLoading] = useState(false);
    
    const handleInvite = async (e) => {
        e.preventDefault();
        setLoading(true);
        
        try {
            const response = await fetch('/api/subscriptions/users/invite', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email, role })
            });
            
            if (response.ok) {
                toast.success('Invitation sent successfully');
                onSuccess();
                onClose();
                setEmail('');
                setRole('member');
            } else {
                const error = await response.json();
                toast.error(error.message || 'Failed to send invitation');
            }
        } catch (error) {
            toast.error('Failed to send invitation');
        } finally {
            setLoading(false);
        }
    };
    
    if (!isOpen) return null;
    
    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="invite-modal" onClick={(e) => e.stopPropagation()}>
                <div className="modal-header">
                    <h3>Invite Team Member</h3>
                    <button className="close-btn" onClick={onClose}>×</button>
                </div>
                
                <form onSubmit={handleInvite}>
                    <div className="form-group">
                        <label>Email Address</label>
                        <input 
                            type="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            required
                            placeholder="<EMAIL>"
                        />
                    </div>
                    
                    <div className="form-group">
                        <label>Role</label>
                        <select value={role} onChange={(e) => setRole(e.target.value)}>
                            <option value="member">Member</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    
                    {subscription.plan_type === 'pay_as_you_go' && (
                        <div className="cost-info">
                            <p>Adding this user will cost an additional ₹249/month</p>
                        </div>
                    )}
                    
                    <div className="modal-actions">
                        <button type="button" onClick={onClose}>Cancel</button>
                        <button type="submit" disabled={loading} className="primary">
                            {loading ? 'Sending...' : 'Send Invitation'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};
Cancel Subscription Modal
javascriptconst CancelSubscriptionModal = ({ isOpen, onClose, subscription }) => {
    const [reason, setReason] = useState('');
    const [feedback, setFeedback] = useState('');
    const [loading, setLoading] = useState(false);
    
    const handleCancel = async () => {
        setLoading(true);
        
        try {
            const response = await fetch('/api/subscriptions/cancel', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ reason, feedback })
            });
            
            if (response.ok) {
                toast.success('Subscription cancelled successfully');
                window.location.reload();
            } else {
                toast.error('Failed to cancel subscription');
            }
        } catch (error) {
            toast.error('Failed to cancel subscription');
        } finally {
            setLoading(false);
        }
    };
    
    if (!isOpen) return null;
    
    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="cancel-modal" onClick={(e) => e.stopPropagation()}>
                <div className="modal-header">
                    <h3>Cancel Subscription</h3>
                    <button className="close-btn" onClick={onClose}>×</button>
                </div>
                
                <div className="cancel-content">
                    <p>We're sorry to see you go! Your subscription will remain active until {formatDate(subscription.next_billing_date)}.</p>
                    
                    <div className="form-group">
                        <label>Reason for cancelling (optional)</label>
                        <select value={reason} onChange={(e) => setReason(e.target.value)}>
                            <option value="">Select a reason</option>
                            <option value="too_expensive">Too expensive</option>
                            <option value="not_using">Not using enough</option>
                            <option value="missing_features">Missing features</option>
                            <option value="switching_service">Switching to another service</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    
                    <div className="form-group">
                        <label>Additional feedback (optional)</label>
                        <textarea 
                            value={feedback}
                            onChange={(e) => setFeedback(e.target.value)}
                            placeholder="Help us improve..."
                            rows={3}
                        />
                    </div>
                    
                    <div className="modal-actions">
                        <button onClick={onClose}>Keep Subscription</button>
                        <button 
                            onClick={handleCancel}
                            disabled={loading}
                            className="danger"
                        >
                            {loading ? 'Cancelling...' : 'Cancel Subscription'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

Payment Integration - Razorpay
Razorpay Setup & Configuration

API Keys: Set up Razorpay API keys (test and live environments)
Webhook Configuration: Configure webhooks for subscription events
INR Currency: All transactions in Indian Rupees (₹)
Payment Methods: UPI, Credit/Debit Cards, Net Banking, Wallets (Paytm, PhonePe, etc.)
KYC Compliance: Ensure proper business verification for live payments

Razorpay Subscription Plans Setup
javascript// Create subscription plans in Razorpay
const plans = [
    {
        id: "pay_as_you_go",
        period: "monthly",
        interval: 1,
        item: {
            name: "Standard",
            amount: 24900, // ₹249 in paise
            currency: "INR"
        }
    },
    {
        id: "premium_plan",
        period: "monthly", 
        interval: 1,
        item: {
            name: "Premium Plan",
            amount: 99900, // ₹999 in paise
            currency: "INR"
        }
    }
];
Database Schema for Payment Tracking
sql-- Razorpay Payment Records
CREATE TABLE razorpay_payments (
    id UUID PRIMARY KEY,
    subscription_id UUID REFERENCES user_subscriptions(id),
    razorpay_payment_id VARCHAR(100) UNIQUE,
    razorpay_order_id VARCHAR(100),
    razorpay_subscription_id VARCHAR(100),
    amount DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'INR',
    status VARCHAR(50), -- created, authorized, captured, refunded, failed
    method VARCHAR(50), -- card, netbanking, upi, wallet
    payment_date TIMESTAMP,
    failure_reason TEXT,
    webhook_data JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Razorpay Subscription Records
CREATE TABLE razorpay_subscriptions (
    id UUID PRIMARY KEY,
    user_subscription_id UUID REFERENCES user_subscriptions(id),
    razorpay_subscription_id VARCHAR(100) UNIQUE,
    razorpay_plan_id VARCHAR(100),
    status VARCHAR(50), -- created, authenticated, active, paused, cancelled, completed
    current_start TIMESTAMP,
    current_end TIMESTAMP,
    next_charge_at TIMESTAMP,
    charge_at TIMESTAMP,
    start_at TIMESTAMP,
    end_at TIMESTAMP,
    auth_attempts INTEGER DEFAULT 0,
    paid_count INTEGER DEFAULT 0,
    customer_notify BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
Razorpay API Integration
Backend Service Layer
javascript// Razorpay Service Implementation
class RazorpayService {
    constructor() {
        this.razorpay = new Razorpay({
            key_id: process.env.RAZORPAY_KEY_ID,
            key_secret: process.env.RAZORPAY_KEY_SECRET
        });
    }

    // Create subscription
    async createSubscription(planId, customerId, addons = []) {
        try {
            const subscription = await this.razorpay.subscriptions.create({
                plan_id: planId,
                customer_id: customerId,
                quantity: 1,
                total_count: 12, // Annual billing cycles
                addons: addons,
                notes: {
                    construction_app: true,
                    plan_type: planId
                }
            });
            return subscription;
        } catch (error) {
            throw new Error(`Razorpay subscription creation failed: ${error.message}`);
        }
    }

    // Create customer
    async createCustomer(userDetails) {
        try {
            const customer = await this.razorpay.customers.create({
                name: userDetails.name,
                email: userDetails.email,
                contact: userDetails.phone,
                notes: {
                    user_id: userDetails.id,
                    signup_date: new Date().toISOString()
                }
            });
            return customer;
        } catch (error) {
            throw new Error(`Razorpay customer creation failed: ${error.message}`);
        }
    }

    // Handle additional users (Standard)
    async addUserToSubscription(subscriptionId, additionalUsers) {
        try {
            const addon = {
                item: {
                    name: "Additional User",
                    amount: 24900, // ₹249 in paise
                    currency: "INR"
                },
                quantity: additionalUsers
            };
            
            const updatedSubscription = await this.razorpay.subscriptions.update(subscriptionId, {
                addons: [addon]
            });
            return updatedSubscription;
        } catch (error) {
            throw new Error(`Failed to add users to subscription: ${error.message}`);
        }
    }

    // Cancel subscription
    async cancelSubscription(subscriptionId, cancelAtCycleEnd = true) {
        try {
            const cancelledSubscription = await this.razorpay.subscriptions.cancel(
                subscriptionId, 
                cancelAtCycleEnd
            );
            return cancelledSubscription;
        } catch (error) {
            throw new Error(`Subscription cancellation failed: ${error.message}`);
        }
    }
}
API Endpoints for Razorpay Integration
javascript// Subscription creation endpoint
POST /api/razorpay/create-subscription
{
    "plan_type": "pay_as_you_go", // or "premium"
    "additional_users": 2, // Only for pay_as_you_go
    "payment_method": "card" // or "upi", "netbanking"
}

// Payment verification endpoint
POST /api/razorpay/verify-payment
{
    "razorpay_payment_id": "pay_xxxxx",
    "razorpay_order_id": "order_xxxxx", 
    "razorpay_signature": "signature_xxxxx"
}

// Webhook endpoint
POST /api/webhooks/razorpay
// Handles all Razorpay webhook events
Webhook Event Handlers
javascript// Webhook event processing
const handleRazorpayWebhook = async (event) => {
    const { event: eventType, payload } = event;
    
    switch (eventType) {
        case 'subscription.activated':
            await activateUserSubscription(payload.subscription.entity);
            break;
            
        case 'subscription.charged':
            await recordSuccessfulPayment(payload.payment.entity);
            break;
            
        case 'subscription.halted':
            await handlePaymentFailure(payload.subscription.entity);
            break;
            
        case 'subscription.cancelled':
            await deactivateSubscription(payload.subscription.entity);
            break;
            
        case 'subscription.completed':
            await handleSubscriptionCompletion(payload.subscription.entity);
            break;
            
        case 'payment.failed':
            await handlePaymentFailure(payload.payment.entity);
            break;
    }
};
Frontend Razorpay Integration
Payment Component
javascript// React component for Razorpay checkout
const RazorpayCheckout = ({ planDetails, onSuccess, onFailure }) => {
    const handlePayment = async () => {
        const options = {
            key: process.env.REACT_APP_RAZORPAY_KEY_ID,
            subscription_id: planDetails.subscriptionId,
            name: "Construction Management Pro",
            description: `${planDetails.planName} - Monthly Subscription`,
            image: "/logo192.png",
            handler: function (response) {
                // Verify payment on backend
                verifyPayment(response).then(onSuccess).catch(onFailure);
            },
            prefill: {
                name: planDetails.customerName,
                email: planDetails.customerEmail,
                contact: planDetails.customerPhone
            },
            notes: {
                address: "Construction Management App"
            },
            theme: {
                color: "#3399cc"
            },
            modal: {
                ondismiss: function() {
                    onFailure("Payment cancelled by user");
                }
            }
        };
        
        const razorpay = new window.Razorpay(options);
        razorpay.open();
    };
    
    return (
        <button onClick={handlePayment} className="razorpay-payment-btn">
            Pay ₹{planDetails.amount}
        </button>
    );
};
Payment Flows with Razorpay

New Subscription Flow:

Trial → Plan Selection → Create Razorpay Customer → Create Subscription → Payment → Webhook Activation


Plan Upgrade Flow:

Current Plan → New Plan Selection → Cancel Current Subscription → Create New Subscription → Prorated Payment → Activation


Additional User Flow (Standard):

Current Subscription → Add Users → Update Subscription with Addons → Immediate Payment → User Addition


Subscription Renewal:

Automatic charge via Razorpay → Webhook notification → Subscription extension → Email confirmation



Error Handling & Retry Logic
javascript// Payment retry mechanism
const retryFailedPayment = async (subscriptionId, maxRetries = 3) => {
    let attempts = 0;
    
    while (attempts < maxRetries) {
        try {
            const result = await razorpay.subscriptions.fetch(subscriptionId);
            if (result.status === 'active') {
                return { success: true, subscription: result };
            }
            attempts++;
            await new Promise(resolve => setTimeout(resolve, 2000 * attempts));
        } catch (error) {
            attempts++;
            if (attempts >= maxRetries) {
                throw new Error(`Payment retry failed after ${maxRetries} attempts`);
            }
        }
    }
};
Security Implementation

Webhook Signature Verification: Verify all webhook requests using Razorpay signatures
Payment Verification: Server-side verification of payment responses
Secure Key Management: Environment-based API key storage
Rate Limiting: Implement rate limits on payment endpoints
Audit Logging: Log all payment transactions and webhook events

Email Notifications Subscription Events:

Welcome email with trial details
Trial expiry warnings (7, 3, 1 days)
Payment successful confirmations
Payment failure notifications
Plan upgrade/downgrade confirmations
User invitation emails
Monthly usage summaries

Email Templates

Professional, construction industry-themed design
Clear call-to-action buttons
Mobile-responsive layouts
Unsubscribe options

Migration Strategy
Phase 1: Database Setup

Create subscription tables
Migrate existing users to trial status
Set up payment gateway

Phase 2: Backend Implementation

Implement subscription APIs
Add access control middleware
Set up payment webhooks
Create email notification system

Phase 3: Frontend Development

Build subscription management pages
Implement trial experience
Add billing interface
Create user management tools

Phase 4: Testing & Deployment

Test all payment flows
Verify access control
Test email notifications
Load test with concurrent users

Phase 5: User Migration

Notify existing users about changes
Provide grace period for plan selection
Migrate free users to trial status
Monitor and support during transition

Analytics & Reporting Subscription Metrics:

Monthly Recurring Revenue (MRR)
Customer Acquisition Cost (CAC)
Churn rate by plan
Trial conversion rates
Average Revenue Per User (ARPU)
User growth metrics

Subscription Management page Requirements:

Real-time subscription statistics
Revenue trends and forecasting
User engagement by plan type
Payment success/failure rates
Support ticket correlation with plans

Security Considerations Data Protection:

Encrypt payment information
Secure webhook endpoints
Implement rate limiting on payment APIs
Audit logs for subscription changes

Access Control

Role-based permissions
Subscription-based feature access
Secure user invitation process
Session management for multi-user accounts

Success Criteria
Technical Metrics

99.9% payment processing uptime
< 2 second page load times for subscription pages
Zero data loss during plan transitions
100% webhook delivery success rate

Business Metrics



25% trial-to-paid conversion rate


< 5% monthly churn rate


90% payment success rate


30% month-over-month revenue growth

User Experience

Intuitive subscription management interface
Seamless plan upgrades/downgrades
Clear pricing and feature communication
Responsive customer support for billing issues

Support & Documentation

Create comprehensive billing FAQ
Set up dedicated billing support channel
Document all subscription APIs
Provide user guides for plan management
Create troubleshooting guides for common issues