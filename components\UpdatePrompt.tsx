import { useColorScheme } from '@/hooks/useColorScheme';
import { AppVersion } from '@/lib/inAppUpdate';
import { MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

interface UpdatePromptProps {
  updateInfo: AppVersion;
  onUpdate: () => void;
  onDismiss?: () => void;
  style?: any;
}

export default function UpdatePrompt({ updateInfo, onUpdate, onDismiss, style }: UpdatePromptProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const { version, mandatory, releaseNotes } = updateInfo;

  return (
    <View style={[
      styles.container,
      {
        backgroundColor: isDark ? '#1a1a1a' : '#fff',
        borderColor: isDark ? '#333' : '#e0e0e0',
      },
      style
    ]}>
      <View style={styles.iconContainer}>
        <MaterialIcons 
          name="system-update" 
          size={24} 
          color={mandatory ? '#ff4444' : '#f97316'} 
        />
      </View>
      
      <View style={styles.content}>
        <Text style={[
          styles.title,
          { color: isDark ? '#fff' : '#000' }
        ]}>
          {mandatory ? 'Update Required' : 'Update Available'}
        </Text>
        
        <Text style={[
          styles.version,
          { color: isDark ? '#ccc' : '#666' }
        ]}>
          Version {version}
        </Text>
        
        {releaseNotes && (
          <Text style={[
            styles.releaseNotes,
            { color: isDark ? '#aaa' : '#888' }
          ]} numberOfLines={2}>
            {releaseNotes}
          </Text>
        )}
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[
              styles.updateButton,
              { backgroundColor: mandatory ? '#ff4444' : '#f97316' }
            ]}
            onPress={onUpdate}
          >
            <Text style={styles.updateButtonText}>
              Update Now
            </Text>
          </TouchableOpacity>
          
          {!mandatory && onDismiss && (
            <TouchableOpacity
              style={[
                styles.dismissButton,
                { borderColor: isDark ? '#555' : '#ddd' }
              ]}
              onPress={onDismiss}
            >
              <Text style={[
                styles.dismissButtonText,
                { color: isDark ? '#ccc' : '#666' }
              ]}>
                Later
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 16,
    margin: 16,
    borderRadius: 12,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    marginRight: 12,
    alignSelf: 'flex-start',
    marginTop: 2,
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  version: {
    fontSize: 14,
    marginBottom: 8,
  },
  releaseNotes: {
    fontSize: 13,
    lineHeight: 18,
    marginBottom: 12,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  updateButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    flex: 1,
  },
  updateButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  dismissButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    flex: 1,
  },
  dismissButtonText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
}); 