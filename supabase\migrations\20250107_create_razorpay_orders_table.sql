-- Migration: Create razorpay_orders table for logging Razorpay order creation
-- This table stores order details created via Razorpay API for tracking and reconciliation

-- <PERSON>reate razorpay_orders table
CREATE TABLE IF NOT EXISTS razorpay_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id VARCHAR(255) UNIQUE NOT NULL, -- Razorpay order ID (e.g., order_ABC123)
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    amount INTEGER NOT NULL, -- Amount in paise
    currency VARCHAR(3) DEFAULT 'INR',
    receipt VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'created', -- created, paid, attempted, cancelled
    plan_id VARCHAR(50), -- Reference to subscription plan
    notes JSONB DEFAULT '{}', -- Additional notes from <PERSON><PERSON>pay
    razorpay_created_at TIMESTAMP, -- Timestamp from Razor<PERSON>y
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE razorpay_orders ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only see their own orders
CREATE POLICY "Users can view their own orders" ON razorpay_orders
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own orders (via edge function)
CREATE POLICY "Users can create their own orders" ON razorpay_orders
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Service role can manage all orders (for edge function)
CREATE POLICY "Service role can manage all orders" ON razorpay_orders
    FOR ALL USING (auth.role() = 'service_role');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_razorpay_orders_order_id ON razorpay_orders(order_id);
CREATE INDEX IF NOT EXISTS idx_razorpay_orders_user_id ON razorpay_orders(user_id);
CREATE INDEX IF NOT EXISTS idx_razorpay_orders_status ON razorpay_orders(status);
CREATE INDEX IF NOT EXISTS idx_razorpay_orders_created_at ON razorpay_orders(created_at);

-- Create function to update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at trigger for razorpay_orders table
CREATE TRIGGER update_razorpay_orders_updated_at 
    BEFORE UPDATE ON razorpay_orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON razorpay_orders TO authenticated;
GRANT ALL ON razorpay_orders TO service_role;

-- Add comments for documentation
COMMENT ON TABLE razorpay_orders IS 'Stores Razorpay order details for tracking and reconciliation';
COMMENT ON COLUMN razorpay_orders.order_id IS 'Unique Razorpay order identifier';
COMMENT ON COLUMN razorpay_orders.amount IS 'Order amount in paise (smallest currency unit)';
COMMENT ON COLUMN razorpay_orders.receipt IS 'Receipt identifier for the order';
COMMENT ON COLUMN razorpay_orders.status IS 'Order status: created, paid, attempted, cancelled';
COMMENT ON COLUMN razorpay_orders.notes IS 'Additional metadata and notes from Razorpay';
