import { canViewReports, UserRole } from '@/lib/permissions';
import { supabase } from '@/lib/supabase';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert } from 'react-native';

/**
 * Hook to check if user has access to reports for a specific site.
 * Redirects to site detail page if not authorized.
 */
export function useReportAccess(siteId: string) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasAccess, setHasAccess] = useState(false);
  const [userRole, setUserRole] = useState<UserRole | null>(null);

  useEffect(() => {
    async function checkAccess() {
      try {
        // Get current user
        const { data: userData } = await supabase.auth.getUser();
        if (!userData?.user) {
          Alert.alert('Access Denied', 'You must be logged in to view reports.');
          router.replace('/');
          return;
        }
        
        // Check site membership and role
        const { data: memberData, error } = await supabase
          .from('site_members')
          .select('role')
          .eq('site_id', siteId)
          .eq('user_id', userData.user.id)
          .single();
          
        if (error || !memberData) {
          Alert.alert('Access Denied', 'You do not have permission to access reports for this site.');
          router.replace(`/site/${siteId}`);
          return;
        }
        
        const role = memberData.role as UserRole;
        setUserRole(role);
        
        // Check if role has report access
        const canAccess = canViewReports(role);
        setHasAccess(canAccess);
        
        if (!canAccess) {
          Alert.alert('Access Denied', 'You do not have permission to view reports for this site.');
          router.replace(`/site/${siteId}`);
          return;
        }
        
        setIsLoading(false);
      } catch (error) {
        console.error('Error checking report access:', error);
        Alert.alert('Error', 'An error occurred while checking permissions.');
        router.replace(`/site/${siteId}`);
      }
    }
    
    checkAccess();
  }, [siteId]);
  
  return { isLoading, hasAccess, userRole };
} 