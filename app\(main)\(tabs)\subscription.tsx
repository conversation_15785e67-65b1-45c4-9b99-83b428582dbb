import { Colors } from '@/constants/Colors';
import { useAuth } from '@/context/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useSubscriptionAccess } from '@/hooks/useSubscriptionAccess';
import { createSubscriptionOrder, updateSubscriptionPayment } from '@/lib/razorpay-orders';
import { supabase } from '@/lib/supabase';
import { FontAwesome5, Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import {
    Alert,
    Dimensions,
    Platform,
    ScrollView,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import RazorpayCheckout from 'react-native-razorpay';
import { SafeAreaView } from 'react-native-safe-area-context';

const { width } = Dimensions.get('window');
const isWeb = Platform.OS === 'web';
const isTablet = width > 768;

// Razorpay configuration - Use live keys for production builds only
// Check if we're in production environment (only when ENVIRONMENT is explicitly set to 'production')
// This ensures development builds use test keys even when __DEV__ is false
const isProduction = process.env.ENVIRONMENT === 'production';

const RAZORPAY_CONFIG = {
  key_id: isProduction
    ? process.env.EXPO_PUBLIC_RAZORPAY_KEY_ID_LIVE
    : process.env.EXPO_PUBLIC_RAZORPAY_KEY_ID,
  key_secret: isProduction
    ? process.env.RAZORPAY_KEY_SECRET_LIVE
    : process.env.RAZORPAY_KEY_SECRET,
  currency: 'INR',
  company_name: 'Infratask',
  company_logo: 'https://cdn.razorpay.com/logos/QpmNzEJUBnXmVL_medium.png',
  theme_color: '#f97316'
};

// Log which keys are being used (for debugging)
console.log('Environment Detection:');
console.log('- __DEV__:', __DEV__);
console.log('- process.env.ENVIRONMENT:', process.env.ENVIRONMENT);
console.log('- isProduction:', isProduction);
console.log('Razorpay Environment:', isProduction ? 'Production (Live Keys)' : 'Development (Test Keys)');
console.log('Using Key ID:', RAZORPAY_CONFIG.key_id ? RAZORPAY_CONFIG.key_id.substring(0, 12) + '...' : 'Missing');

// Payment interfaces imported from lib/razorpay.ts

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  billing: string;
  description: string;
  features: string[];
  recommended?: boolean;
  additionalUserPrice?: number;
  maxUsers?: number | null;
}

interface UserSubscription {
  id: string;
  status: 'trial' | 'active' | 'cancelled' | 'expired';
  plan_id: string;
  plan_name?: string; // Optional until schema cache refreshes
  current_period_end: string;
  trial_end?: string;
  additional_users: number;
}

// Payment functions now imported from lib/razorpay.ts

// Payment verification function now imported from lib/razorpay.ts

const SubscriptionScreen = () => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { user } = useAuth();
  const [currentSubscription, setCurrentSubscription] = useState<UserSubscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [subscribing, setSubscribing] = useState<string | null>(null);
  const [processingPayment, setProcessingPayment] = useState(false);
  const [refreshCount, setRefreshCount] = useState(0); // Track refresh attempts

  // WebView payment state (keeping for future use)
  // const [showWebView, setShowWebView] = useState(false);
  // const [webViewPaymentData, setWebViewPaymentData] = useState<any>(null);
  // const [webViewOrder, setWebViewOrder] = useState<any>(null);

  // Use the new subscription access hook for automatic expiry checking
  const {
    subscription: accessSubscription,
    isExpired,
    isSubscribed,
    refreshSubscription
  } = useSubscriptionAccess();

  const subscriptionPlans: SubscriptionPlan[] = [
    {
      id: 'standard',
      name: 'Standard',
      price: 249,
      billing: 'per month',
      description: 'Perfect for small teams and startups',
      additionalUserPrice: 249,
      maxUsers: 1,
      features: [
        '1 user included',
        'create and manage unlimited sites',
        'All core construction management features',
        'Project management tools',
        'Basic reporting and analytics',
        'Email support',
      ]
    },
    {
      id: 'premium',
      name: 'Premium',
      price: 999,
      billing: 'per month',
      description: 'Best for growing construction businesses',
      recommended: true,
      maxUsers: null,
      features: [
        '5 users Included + ₹249 for each extra user',
        'create and manage unlimited sites',
        'All core construction management features',
        'Advanced reporting and analytics',
        'Priority customer support',
        'Advanced integrations',
        'Custom workflows',
        'Data export & backup',
      ]
    }
  ];

  useEffect(() => {
    fetchCurrentSubscription();
    // Also trigger the automatic expiry check
    if (refreshSubscription) {
      refreshSubscription();
    }
  }, [user, refreshSubscription, refreshCount]);

  // Single auto-refresh after successful payment (triggered by refreshCount change)
  useEffect(() => {
    if (refreshCount > 0 && user) {
      console.log('🔄 Auto-refreshing subscription status after payment...');
      const refreshAfterPayment = async () => {
        await fetchCurrentSubscription();
        if (refreshSubscription) {
          await refreshSubscription();
        }
      };
      refreshAfterPayment();
    }
  }, [refreshCount, user, refreshSubscription]);

  // WebView callback function (commented out for now)
  // const openWebView = (paymentData: any, order: any) => {
  //   console.log('🌐 Opening WebView with payment data:', paymentData);
  //   setWebViewPaymentData(paymentData);
  //   setWebViewOrder(order);
  //   setShowWebView(true);
  // };

  // Check if subscription has expired and show alert
  useEffect(() => {
    if (isExpired()) {
      Alert.alert(
        'Subscription Expired',
        'Your subscription has expired. Please renew to continue accessing premium features.',
        [
          {
            text: 'Renew Now',
            onPress: () => {
              // User can choose to subscribe again
            },
            style: 'default'
          },
          {
            text: 'OK',
            style: 'cancel'
          }
        ]
      );
    }
  }, [isExpired]);

  const handlePayment = async (planId: 'standard' | 'premium' = 'standard') => {
    if (!user) {
      Alert.alert('Error', 'Please login to continue with payment');
      return;
    }

    try {
      setProcessingPayment(true);

      // Create order using the edge function
      console.log('Creating Razorpay order for plan:', planId);
      const orderResponse = await createSubscriptionOrder(planId, user.id);

      console.log('Order created successfully:', orderResponse.order.id);

      // Prepare Razorpay checkout options
      const options = {
        description: `${planId === 'standard' ? 'Standard' : 'Premium'} Plan Subscription`,
        image: RAZORPAY_CONFIG.company_logo,
        currency: orderResponse.order.currency,
        key: RAZORPAY_CONFIG.key_id,
        amount: orderResponse.order.amount,
        name: RAZORPAY_CONFIG.company_name,
        order_id: orderResponse.order.id, // Use the real order ID from Razorpay
        prefill: {
          email: user.email || '',
          contact: user.phone || '',
          name: user.user_metadata?.full_name || user.email || ''
        },
        theme: { color: RAZORPAY_CONFIG.theme_color }
      };

      // Open Razorpay checkout
      RazorpayCheckout.open(options).then(async (paymentData: any) => {
        // Payment successful
        console.log('Payment successful:', paymentData);

        try {
          console.log('🔄 Calling edge function to update subscription...');

          // Use edge function to update subscription and payment status
          const updateResult = await updateSubscriptionPayment({
            razorpay_order_id: paymentData.razorpay_order_id,
            razorpay_payment_id: paymentData.razorpay_payment_id,
            razorpay_signature: paymentData.razorpay_signature,
            plan_id: planId,
            payment_status: 'success'
          });

          console.log('✅ Edge function response:', updateResult);

          if (updateResult.success) {
            // Immediately update local state
            if (updateResult.subscription) {
              setCurrentSubscription(updateResult.subscription);
            }

            // Trigger single auto-refresh
            console.log('🔄 Triggering auto-refresh after successful payment...');
            setRefreshCount(prev => prev + 1);

            // Immediate refresh
            await fetchCurrentSubscription();
            if (refreshSubscription) {
              await refreshSubscription();
            }

            // Show beautiful success message
            Alert.alert(
              '🎉 Payment Successful!',
              `Congratulations! Your ${planId === 'standard' ? 'Standard' : 'Premium'} subscription is now active!\n\n✅ Full access unlocked\n✅ All premium features enabled\n✅ 30-day billing cycle started\n\nEnjoy your enhanced construction management experience!`,
              [{
                text: 'Start Using Premium Features',
                onPress: async () => {
                  // Final refresh when user clicks the button
                  await fetchCurrentSubscription();
                  if (refreshSubscription) {
                    await refreshSubscription();
                  }
                },
                style: 'default'
              }]
            );
          } else {
            console.error('❌ Edge function failed:', updateResult);

            // Fallback: Try direct database update
            console.log('🔄 Attempting fallback database update...');
            await fallbackCreateSubscription(paymentData, planId);
          }
        } catch (dbError: any) {
          console.error('❌ Database error after payment:', dbError);

          // Check if it's a network error or edge function error
          if (dbError.message && dbError.message.includes('Edge Function returned a non-2xx status code')) {
            console.log('🔄 Edge function error detected, attempting fallback...');
            await fallbackCreateSubscription(paymentData, planId);
          } else {
            console.log('🔄 Unknown error, attempting fallback...');
            await fallbackCreateSubscription(paymentData, planId);
          }
        }

      }).catch(async (error: any) => {
        // Payment failed or cancelled
        console.log('Payment error:', error);

        // Check if payment was cancelled by user
        const isPaymentCancelled = error.code === 0 ||
          (error.error && error.error.reason === 'payment_error') ||
          (error.error && error.error.step === 'payment_authentication');

        try {
          // Update order status in database for cancelled/failed payments
          await updateSubscriptionPayment({
            razorpay_order_id: orderResponse.order.id,
            plan_id: planId,
            payment_status: isPaymentCancelled ? 'cancelled' : 'failed'
          });
        } catch (updateError) {
          console.error('Error updating order status:', updateError);
        }

        if (isPaymentCancelled) {
          Alert.alert(
            'Payment Cancelled',
            'Your payment was cancelled. Would you like to try again?',
            [
              {
                text: 'Try Again',
                onPress: () => handlePayment(planId),
                style: 'default'
              },
              {
                text: 'Cancel',
                style: 'cancel'
              }
            ]
          );
        } else {
          Alert.alert('Payment Failed', 'Something went wrong with your payment. Please try again.');
        }
      }).finally(() => {
        setProcessingPayment(false);
      });

    } catch (error: any) {
      console.error('Error creating order:', error);
      Alert.alert('Error', 'Failed to create payment order. Please try again.');
      setProcessingPayment(false);
    }
  };

  const fetchCurrentSubscription = async () => {
    if (!user) return;

    try {
      console.log('🔍 Fetching subscription for user:', user.id);
      // Check if user has an active subscription or trial
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching subscription:', error);
      } else if (data) {
        console.log('✅ Subscription found:', data);
        setCurrentSubscription(data);
      } else {
        console.log('ℹ️ No subscription found, starting trial');
        // New user - automatically start 14-day trial
        await startFreeTrial();
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fallback function to create subscription directly if edge function fails
  const fallbackCreateSubscription = async (paymentData: any, planId: string) => {
    try {
      console.log('🔄 Creating subscription via fallback method...');

      const currentDate = new Date();
      const endDate = new Date(currentDate);
      endDate.setDate(endDate.getDate() + 30); // 30 days subscription

      const subscriptionData = {
        user_id: user!.id,
        plan_id: planId,
        plan_name: planId === 'standard' ? 'Standard Plan' : 'Premium Plan',
        status: 'active',
        current_period_start: currentDate.toISOString(),
        current_period_end: endDate.toISOString(),
        order_id: paymentData.razorpay_order_id,
        payment_id: paymentData.razorpay_payment_id,
        razorpay_signature: paymentData.razorpay_signature,
        additional_users: 0,
        auto_renew: true
      };

      // First check if subscription already exists
      const { data: existingSubscription } = await supabase
        .from('user_subscriptions')
        .select('id')
        .eq('user_id', user!.id)
        .single();

      let subscriptionResult;
      let subscriptionError;

      if (existingSubscription) {
        // Update existing subscription
        const { data, error } = await supabase
          .from('user_subscriptions')
          .update(subscriptionData)
          .eq('user_id', user!.id)
          .select()
          .single();
        subscriptionResult = data;
        subscriptionError = error;
      } else {
        // Insert new subscription
        const { data, error } = await supabase
          .from('user_subscriptions')
          .insert([subscriptionData])
          .select()
          .single();
        subscriptionResult = data;
        subscriptionError = error;
      }

      if (subscriptionError) {
        console.error('❌ Fallback subscription creation failed:', subscriptionError);
        Alert.alert('Error', 'Payment successful but failed to activate subscription. Please contact support with your payment details.');
        return;
      }

      console.log('✅ Fallback subscription created successfully:', subscriptionResult);

      // Update local state
      setCurrentSubscription(subscriptionResult);

      // Trigger single auto-refresh
      setRefreshCount(prev => prev + 1);

      // Immediate refresh
      await fetchCurrentSubscription();
      if (refreshSubscription) {
        await refreshSubscription();
      }

      // Show success message
      Alert.alert(
        '🎉 Payment Successful!',
        `Congratulations! Your ${planId === 'standard' ? 'Standard' : 'Premium'} subscription is now active!\n\n✅ Full access unlocked\n✅ All premium features enabled\n✅ 30-day billing cycle started\n\nEnjoy your enhanced construction management experience!`,
        [{
          text: 'Start Using Premium Features',
          onPress: async () => {
            await fetchCurrentSubscription();
            if (refreshSubscription) {
              await refreshSubscription();
            }
          },
          style: 'default'
        }]
      );

    } catch (error) {
      console.error('❌ Fallback method failed:', error);
      Alert.alert('Error', 'Payment successful but failed to activate subscription. Please contact support with your payment details.');
    }
  };

  const startFreeTrial = async () => {
    if (!user) return;

    // Show options for trial
    Alert.alert(
      '🎉 Start Your Free Trial',
      'Start your 14-day Premium trial with full access to all features:',
      [
        {
          text: 'Start Free Trial',
          onPress: () => createFreeTrial(),
          style: 'default'
        },
        {
          text: 'Cancel',
          style: 'cancel'
        }
      ]
    );
  };

  const createFreeTrial = async () => {
    if (!user) return;

    try {
      const trialEnd = new Date();
      trialEnd.setDate(trialEnd.getDate() + 14);

      const { data, error } = await supabase
        .from('user_subscriptions')
        .insert([
          {
            user_id: user.id,
            plan_id: 'premium', // Start with Premium features during trial
            status: 'trial',
            trial_end: trialEnd.toISOString(),
            current_period_start: new Date().toISOString(),
            current_period_end: trialEnd.toISOString(),
            additional_users: 0,
          }
        ])
        .select()
        .single();

      if (error) {
        console.error('Error starting trial:', error);
        Alert.alert('Error', 'Failed to start trial. Please try again.');
      } else {
        setCurrentSubscription(data);

        // Immediately refresh subscription status for instant access
        await fetchCurrentSubscription();
        if (refreshSubscription) {
          await refreshSubscription();
        }

        Alert.alert(
          '🎉 Welcome to your Free Trial!',
          'You now have 14 days of Premium access. Enjoy all features with unlimited users!',
          [{
            text: 'Get Started',
            onPress: async () => {
              // Final refresh when user clicks the button
              await fetchCurrentSubscription();
              if (refreshSubscription) {
                await refreshSubscription();
              }
            },
            style: 'default'
          }]
        );
      }
    } catch (error) {
      console.error('Error starting trial:', error);
      Alert.alert('Error', 'Failed to start trial. Please try again.');
    }
  };



  const handleSubscribe = async (planId: string) => {
    if (!user) return;

    setSubscribing(planId);

    // Call the new handlePayment function
    await handlePayment(planId as 'standard' | 'premium');

    // Reset subscribing state
    setSubscribing(null);
  };

  const getTrialDaysLeft = () => {
    if (!currentSubscription?.trial_end) return 0;
    const trialEnd = new Date(currentSubscription.trial_end);
    const now = new Date();
    const diffTime = trialEnd.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const renderTrialBanner = () => {
    if (!currentSubscription || currentSubscription.status !== 'trial') return null;

    const daysLeft = getTrialDaysLeft();

    return (
      <LinearGradient
        colors={['#f97316', '#ea580c']}
        style={styles.trialBanner}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <View style={styles.trialContent}>
          <MaterialCommunityIcons name="crown" size={24} color="white" />
          <View style={styles.trialText}>
            <Text style={styles.trialTitle}>Free Trial Active</Text>
            <Text style={styles.trialSubtitle}>
              {daysLeft} days left • Premium features unlocked
            </Text>
          </View>
          {daysLeft <= 3 && (
            <TouchableOpacity
              style={styles.upgradeButton}
              onPress={() => handleSubscribe('premium')}
              disabled={processingPayment}
            >
              <Text style={styles.upgradeButtonText}>Upgrade Now</Text>
            </TouchableOpacity>
          )}
        </View>
      </LinearGradient>
    );
  };

  const handleManageSubscription = () => {
    router.push('/manage-subscription');
  };

  const handleCancelSubscription = () => {
    Alert.alert(
      'Cancel Subscription',
      'Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your current billing period.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Confirm Cancel',
          style: 'destructive',
          onPress: async () => {
            // TODO: Implement subscription cancellation logic
            Alert.alert('Feature Coming Soon', 'Subscription cancellation will be available soon. Please contact support for assistance.');
          },
        },
      ]
    );
  };

  const handleUpdatePaymentMethod = () => {
    // TODO: Implement payment method update logic
    Alert.alert('Feature Coming Soon', 'Payment method update will be available soon. Please contact support for assistance.');
  };

  const handleChangePlan = () => {
    // TODO: Implement plan change logic
    Alert.alert('Feature Coming Soon', 'Plan changes will be available soon. Please contact support for assistance.');
  };

  const renderCurrentSubscription = () => {
    if (!currentSubscription) return null;

    const isTrialUser = currentSubscription.status === 'trial';
    const isExpired = currentSubscription.status === 'expired';
    const isActive = currentSubscription.status === 'active';

    let statusText = 'Unknown';
    let statusColor = colors.primary;
    let iconName: any = 'check-circle';

    if (isTrialUser) {
      statusText = 'Trial';
      statusColor = '#ff9500';
      iconName = 'clock-outline';
    } else if (isExpired) {
      statusText = 'Expired';
      statusColor = '#ef4444'; // Red color for expired
      iconName = 'close-circle';
    } else if (isActive) {
      statusText = 'Active';
      statusColor = '#10b981'; // Green color for active
      iconName = 'check-circle';
    }

    return (
      <View style={[styles.currentSubscriptionCard, { backgroundColor: colors.background, borderColor: statusColor }]}>
        <View style={styles.currentSubscriptionHeader}>
          <View style={styles.currentSubscriptionTitle}>
            <MaterialCommunityIcons
              name={iconName}
              size={24}
              color={statusColor}
            />
            <Text style={[styles.currentSubscriptionText, { color: colors.text }]}>
              Current Plan: {currentSubscription.plan_name || currentSubscription.plan_id} {isTrialUser ? '(Trial)' : isExpired ? '(Expired)' : ''}
            </Text>
          </View>
          <Text style={[styles.subscriptionStatus, { color: statusColor }]}>
            {statusText}
          </Text>
        </View>

        {isTrialUser ? (
          <Text style={[styles.subscriptionDetails, { color: colors.icon }]}>
            Trial ends: {new Date(currentSubscription.trial_end || currentSubscription.current_period_end).toLocaleDateString()}
          </Text>
        ) : isExpired ? (
          <Text style={[styles.subscriptionDetails, { color: '#ef4444' }]}>
            Expired on: {new Date(currentSubscription.current_period_end).toLocaleDateString()}
          </Text>
        ) : (
          <Text style={[styles.subscriptionDetails, { color: colors.icon }]}>
            Next billing: {new Date(currentSubscription.current_period_end).toLocaleDateString()}
          </Text>
        )}

        {currentSubscription.additional_users > 0 && (
          <Text style={[styles.subscriptionDetails, { color: colors.icon }]}>
            Additional users: {currentSubscription.additional_users}
          </Text>
        )}

        <TouchableOpacity
          style={[styles.manageSubscriptionButton, { backgroundColor: colors.background, borderColor: statusColor }]}
          onPress={isExpired ? () => {/* User can choose to renew by selecting a plan below */ } : handleManageSubscription}
        >
          <MaterialCommunityIcons
            name={isExpired ? "refresh" : "cog"}
            size={20}
            color={statusColor}
          />
          <Text style={[styles.manageSubscriptionButtonText, { color: statusColor }]}>
            {isExpired ? "Subscription Expired - Choose Plan Below" : "Manage Subscription"}
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderPlanCard = (plan: SubscriptionPlan) => {
    const isCurrentPlan = currentSubscription?.plan_id === plan.id && currentSubscription?.status === 'active';
    const isProcessingThisPlan = subscribing === plan.id || processingPayment;

    return (
      <View
        key={plan.id}
        style={[
          styles.planCard,
          { backgroundColor: colors.background },
          plan.recommended && styles.recommendedCard,
          plan.recommended && { borderColor: colors.primary }
        ]}
      >
        {plan.recommended && (
          <View style={[styles.recommendedBadge, { backgroundColor: colors.primary }]}>
            <Text style={styles.recommendedText}>MOST POPULAR</Text>
          </View>
        )}

        <View style={styles.planHeader}>
          <Text style={[styles.planName, { color: colors.text }]}>{plan.name}</Text>
          <View style={styles.priceContainer}>
            <Text style={[styles.priceSymbol, { color: colors.text }]}>₹</Text>
            <Text style={[styles.price, { color: colors.text }]}>{plan.price}</Text>
            <Text style={[styles.billing, { color: colors.icon }]}>/{plan.billing}</Text>
          </View>
          <Text style={[styles.planDescription, { color: colors.icon }]}>{plan.description}</Text>
        </View>

        <View style={styles.featuresContainer}>
          {plan.features.map((feature, index) => (
            <View key={index} style={styles.featureRow}>
              <Ionicons name="checkmark" size={20} color={colors.primary} />
              <Text style={[styles.featureText, { color: colors.text }]}>{feature}</Text>
            </View>
          ))}
        </View>

        <TouchableOpacity
          style={[
            styles.subscribeButton,
            { backgroundColor: isCurrentPlan ? colors.icon : colors.primary },
            plan.recommended && !isCurrentPlan && styles.primaryButton,
            isProcessingThisPlan && styles.processingButton
          ]}
          onPress={() => !isCurrentPlan && !isProcessingThisPlan && handleSubscribe(plan.id)}
          disabled={isCurrentPlan || isProcessingThisPlan || processingPayment}
        >
          {isProcessingThisPlan && (
            <MaterialCommunityIcons
              name="loading"
              size={20}
              color="white"
              style={styles.loadingIcon}
            />
          )}
          <Text style={[
            styles.subscribeButtonText,
            { color: isCurrentPlan ? colors.text : 'white' },
            isProcessingThisPlan && styles.processingText
          ]}>
            {isCurrentPlan
              ? 'Current Plan'
              : isProcessingThisPlan
                ? 'Processing...'
                : 'Choose Plan'
            }
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <MaterialCommunityIcons name="loading" size={32} color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>Loading subscription...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>Subscription Plans</Text>
          <Text style={[styles.headerSubtitle, { color: colors.icon }]}>
            Choose the perfect plan for your construction business
          </Text>
        </View>



        {/* Trial Banner */}
        {renderTrialBanner()}

        {/* Current Subscription */}
        {renderCurrentSubscription()}

        {/* Free Trial Info */}
        {!currentSubscription && (
          <View style={[styles.trialInfoCard, { backgroundColor: colors.background, borderColor: colors.primary }]}>
            <MaterialCommunityIcons name="gift" size={32} color={colors.primary} />
            <Text style={[styles.trialInfoTitle, { color: colors.text }]}>14-Day Free Trial</Text>
            <Text style={[styles.trialInfoText, { color: colors.icon }]}>
              Start with full Premium access for 14 days. No payment required!
            </Text>
            <TouchableOpacity
              style={[styles.trialButton, { backgroundColor: colors.primary }]}
              onPress={startFreeTrial}
              disabled={processingPayment}
            >
              {processingPayment && (
                <MaterialCommunityIcons
                  name="loading"
                  size={20}
                  color="white"
                  style={styles.loadingIcon}
                />
              )}
              <Text style={[styles.trialButtonText, processingPayment && styles.processingText]}>
                {processingPayment ? 'Processing...' : 'Get Started - 14 Days Trial'}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Subscription Plans */}
        <View style={[styles.plansContainer, isTablet && styles.plansContainerTablet]}>
          {subscriptionPlans.map(renderPlanCard)}
        </View>

        {/* Features Comparison */}
        <View style={styles.comparisonSection}>
          <Text style={[styles.comparisonTitle, { color: colors.text }]}>
            Why Choose Premium?
          </Text>
          <View style={styles.comparisonGrid}>
            <View style={styles.comparisonItem}>
              <FontAwesome5 name="users" size={24} color={colors.primary} />
              <Text style={[styles.comparisonItemTitle, { color: colors.text }]}>Unlimited Projects</Text>
              <Text style={[styles.comparisonItemText, { color: colors.icon }]}>
                Add unlimited projects without extra cost
              </Text>
            </View>
            <View style={styles.comparisonItem}>
              <MaterialCommunityIcons name="chart-line" size={24} color={colors.primary} />
              <Text style={[styles.comparisonItemTitle, { color: colors.text }]}>Advanced Analytics</Text>
              <Text style={[styles.comparisonItemText, { color: colors.icon }]}>
                Detailed insights and comprehensive reporting
              </Text>
            </View>
            <View style={styles.comparisonItem}>
              <MaterialCommunityIcons name="headset" size={24} color={colors.primary} />
              <Text style={[styles.comparisonItemTitle, { color: colors.text }]}>Priority Support</Text>
              <Text style={[styles.comparisonItemText, { color: colors.icon }]}>
                Get help when you need it with priority assistance
              </Text>
            </View>
          </View>
        </View>

        {/* Payment Security Notice */}
        <View style={styles.securityNotice}>
          <View style={styles.securityHeader}>
            <MaterialCommunityIcons name="shield-check" size={24} color={colors.primary} />
            <Text style={[styles.securityTitle, { color: colors.text }]}>Secure Payments</Text>
          </View>
          <Text style={[styles.securityText, { color: colors.icon }]}>
            All payments are processed securely through Razorpay with bank-level encryption.
            Your payment information is never stored on our servers.
          </Text>
        </View>


      </ScrollView>
    </SafeAreaView>
  );
};

const styles = {
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 32,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  header: {
    padding: 24,
    alignItems: 'center' as const,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold' as const,
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    textAlign: 'center' as const,
    lineHeight: 24,
  },

  trialBanner: {
    margin: 16,
    borderRadius: 16,
    padding: 20,
  },
  trialContent: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
  },
  trialText: {
    marginLeft: 12,
    flex: 1,
  },
  trialTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold' as const,
  },
  trialSubtitle: {
    color: 'white',
    fontSize: 14,
    opacity: 0.9,
    marginTop: 2,
  },
  currentSubscriptionCard: {
    margin: 16,
    padding: 20,
    borderRadius: 16,
    borderWidth: 2,
  },
  currentSubscriptionHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 8,
  },
  currentSubscriptionTitle: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    flex: 1,
  },
  currentSubscriptionText: {
    fontSize: 16,
    fontWeight: '600' as const,
    marginLeft: 8,
  },
  subscriptionStatus: {
    fontSize: 14,
    fontWeight: '600' as const,
    textTransform: 'uppercase' as const,
  },
  subscriptionDetails: {
    fontSize: 14,
    marginBottom: 4,
  },
  trialInfoCard: {
    margin: 16,
    padding: 24,
    borderRadius: 16,
    borderWidth: 1,
    alignItems: 'center' as const,
  },
  trialInfoTitle: {
    fontSize: 20,
    fontWeight: 'bold' as const,
    marginTop: 12,
    marginBottom: 8,
  },
  trialInfoText: {
    fontSize: 14,
    textAlign: 'center' as const,
    lineHeight: 20,
  },
  plansContainer: {
    padding: 16,
  },
  plansContainerTablet: {
    flexDirection: 'row' as const,
    justifyContent: 'center' as const,
    alignItems: 'flex-start' as const,
  },
  planCard: {
    borderRadius: 20,
    padding: 24,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    position: 'relative' as const,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 12,
      },
      android: {
        elevation: 4,
      },
      web: {
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
        maxWidth: isTablet ? 400 : '100%',
        margin: isTablet ? '0 12px 20px' : '0 0 20px',
      },
    }),
  },
  recommendedCard: {
    borderWidth: 2,
    transform: [{ scale: isTablet ? 1.05 : 1 }],
  },
  recommendedBadge: {
    position: 'absolute' as const,
    top: -12,
    alignSelf: 'center' as const,
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 20,
  },
  recommendedText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold' as const,
  },
  planHeader: {
    marginBottom: 24,
    alignItems: 'center' as const,
  },
  planName: {
    fontSize: 24,
    fontWeight: 'bold' as const,
    marginBottom: 12,
  },
  priceContainer: {
    flexDirection: 'row' as const,
    alignItems: 'baseline' as const,
    marginBottom: 12,
  },
  priceSymbol: {
    fontSize: 20,
    fontWeight: '600' as const,
  },
  price: {
    fontSize: 48,
    fontWeight: 'bold' as const,
  },
  billing: {
    fontSize: 16,
    marginLeft: 4,
  },
  planDescription: {
    fontSize: 16,
    textAlign: 'center' as const,
    lineHeight: 24,
  },
  featuresContainer: {
    marginBottom: 32,
  },
  featureRow: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginBottom: 12,
  },
  featureText: {
    fontSize: 15,
    marginLeft: 12,
    flex: 1,
    lineHeight: 22,
  },
  subscribeButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center' as const,
    flexDirection: 'row' as const,
    justifyContent: 'center' as const,
  },
  primaryButton: {
    transform: [{ scale: 1.02 }],
  },
  subscribeButtonText: {
    fontSize: 16,
    fontWeight: 'bold' as const,
  },
  comparisonSection: {
    margin: 16,
    padding: 24,
  },
  comparisonTitle: {
    fontSize: 24,
    fontWeight: 'bold' as const,
    textAlign: 'center' as const,
    marginBottom: 32,
  },
  comparisonGrid: {
    flexDirection: isTablet ? 'row' as const : 'column' as const,
    justifyContent: 'space-between' as const,
  },
  comparisonItem: {
    alignItems: 'center' as const,
    marginBottom: 24,
    flex: isTablet ? 1 : undefined,
    paddingHorizontal: isTablet ? 16 : 0,
  },
  comparisonItemTitle: {
    fontSize: 18,
    fontWeight: '600' as const,
    marginTop: 12,
    marginBottom: 8,
    textAlign: 'center' as const,
  },
  comparisonItemText: {
    fontSize: 14,
    textAlign: 'center' as const,
    lineHeight: 20,
  },
  upgradeButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  upgradeButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600' as const,
  },
  trialButton: {
    marginTop: 16,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  trialButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold' as const,
  },
  processingButton: {
    opacity: 0.7,
  },
  loadingIcon: {
    marginRight: 8,
  },
  processingText: {
    marginLeft: 8,
  },
  securityNotice: {
    margin: 16,
    padding: 20,
    backgroundColor: 'rgba(59, 130, 246, 0.05)',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(59, 130, 246, 0.1)',
  },
  securityHeader: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginBottom: 8,
  },
  securityTitle: {
    fontSize: 16,
    fontWeight: '600' as const,
    marginLeft: 8,
  },
  securityText: {
    fontSize: 14,
    lineHeight: 20,
  },
  manageSubscriptionButton: {
    marginTop: 16,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  manageSubscriptionButtonText: {
    fontSize: 14,
    fontWeight: '600' as const,
    marginLeft: 8,
  },

};

export default SubscriptionScreen;
