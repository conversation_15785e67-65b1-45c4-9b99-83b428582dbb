import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface CreateStandardSubscriptionRequest {
  added_user_id: string;
  premium_user_id: string;
  subscription_id: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client with service role key
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')!
    const token = authHeader.replace('Bearer ', '')

    // Get user from token
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser(token)
    
    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Parse request body
    const body: CreateStandardSubscriptionRequest = await req.json()
    const { added_user_id, premium_user_id, subscription_id } = body

    console.log('🔄 Creating standard subscription for added user:', {
      added_user_id,
      premium_user_id,
      subscription_id,
      requesting_user: user.id
    })

    // Verify that the requesting user is the premium user or has permission
    if (user.id !== premium_user_id) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized: Only the premium user can create subscriptions for added users' }),
        { 
          status: 403, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Verify that the premium user has an active premium subscription
    const { data: premiumSubscription, error: premiumSubError } = await supabaseClient
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', premium_user_id)
      .eq('id', subscription_id)
      .eq('plan_id', 'premium')
      .eq('status', 'active')
      .single()

    if (premiumSubError || !premiumSubscription) {
      console.error('❌ Premium subscription not found or not active:', premiumSubError)
      return new Response(
        JSON.stringify({ 
          error: 'Premium subscription not found or not active',
          details: premiumSubError?.message 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Check if the added user already has an active subscription
    const { data: existingSubscription, error: existingSubError } = await supabaseClient
      .from('user_subscriptions')
      .select('id, plan_id, status')
      .eq('user_id', added_user_id)
      .eq('status', 'active')
      .single()

    if (existingSubscription) {
      console.log('ℹ️ User already has an active subscription:', existingSubscription)
      return new Response(
        JSON.stringify({ 
          success: true,
          message: 'User already has an active subscription',
          existing_subscription: existingSubscription
        }),
        { 
          status: 200, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get the standard plan details
    const { data: standardPlan, error: planError } = await supabaseClient
      .from('subscription_plans')
      .select('*')
      .eq('name', 'standard')
      .eq('is_active', true)
      .single()

    if (planError || !standardPlan) {
      console.error('❌ Standard plan not found:', planError)
      return new Response(
        JSON.stringify({ 
          error: 'Standard plan not found',
          details: planError?.message 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Create subscription dates matching the premium user's subscription period
    const currentDate = new Date(premiumSubscription.current_period_start)
    const endDate = new Date(premiumSubscription.current_period_end)

    const subscriptionData = {
      user_id: added_user_id,
      plan_id: 'standard',
      status: 'active',
      current_period_start: currentDate.toISOString(),
      current_period_end: endDate.toISOString(),
      additional_users: 0,
      auto_renew: false, // Standard plans created this way don't auto-renew
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    // Create the standard subscription
    const { data: newSubscription, error: subscriptionError } = await supabaseClient
      .from('user_subscriptions')
      .insert([subscriptionData])
      .select()
      .single()

    if (subscriptionError) {
      console.error('❌ Error creating standard subscription:', subscriptionError)
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Failed to create standard subscription',
          details: subscriptionError.message
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

    console.log('✅ Standard subscription created successfully:', {
      subscription_id: newSubscription.id,
      user_id: newSubscription.user_id,
      plan_id: newSubscription.plan_id,
      status: newSubscription.status,
      period_start: newSubscription.current_period_start,
      period_end: newSubscription.current_period_end
    })

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Standard subscription created successfully for added user',
        subscription: newSubscription
      }),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Error in create-standard-subscription function:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        details: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
