import {
    processAdditionalUserPayment,
    type AdditionalUserPaymentRequest
} from '@/app/api/additional-user-payment';
import {
    addUserToSubscription as addUserToSubscriptionAPI,
    fetchSubscriptionUsers,
    removeUserFromSubscription as removeUserFromSubscriptionAPI,
    type SubscriptionUser
} from '@/app/api/subscription-users';
import AddUserModal from '@/components/subscription/AddUserModal';
import UserCard from '@/components/subscription/UserCard';
import UserLimitsBanner from '@/components/subscription/UserLimitsBanner';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/context/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { supabase } from '@/lib/supabase';
import {
    checkUserAdditionLimit,
    showUserAdditionAlert
} from '@/utils/subscription-limits';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import {
    Alert,
    Dimensions,
    ScrollView,
    Text,
    TouchableOpacity,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

const { width } = Dimensions.get('window');
const isTablet = width > 768;

interface UserSubscription {
  id: string;
  status: 'trial' | 'active' | 'cancelled' | 'expired';
  plan_id: string;
  plan_name: string;
  current_period_start: string;
  current_period_end: string;
  trial_end?: string;
  additional_users: number;
  auto_renew: boolean;
}

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  billing: string;
  description: string;
  features: string[];
  additionalUserPrice?: number;
  maxUsers?: number | null;
}



const ManageSubscriptionScreen = () => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { user } = useAuth();
  const [currentSubscription, setCurrentSubscription] = useState<UserSubscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [subscriptionUsers, setSubscriptionUsers] = useState<SubscriptionUser[]>([]);
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [newUserPhone, setNewUserPhone] = useState('');
  const [addingUser, setAddingUser] = useState(false);

  // Simple user limits calculation
  const getUserLimits = () => {
    if (!currentSubscription || currentSubscription.plan_id !== 'premium') return null;

    const activeUsers = subscriptionUsers.filter(u => u.status === 'active').length;
    const totalUsers = activeUsers + 1; // +1 for owner
    const includedUsers = 5; // Premium plan includes 5 users
    const additionalUsers = Math.max(0, totalUsers - includedUsers);
    const additionalCost = additionalUsers * 249;
    const remainingIncludedSlots = Math.max(0, includedUsers - totalUsers);

    return {
      includedUsers,
      currentActiveUsers: activeUsers,
      totalUsers,
      additionalUsers,
      additionalCost,
      canAddMore: true, // Premium can always add more with payment
      remainingIncludedSlots
    };
  };

  const availablePlans: SubscriptionPlan[] = [
    {
      id: 'standard',
      name: 'Standard',
      price: 249,
      billing: 'per month',
      description: 'Perfect for small teams and startups',
      additionalUserPrice: 249,
      maxUsers: 1,
      features: [
        '1 user included',
        'Unlimited sites',
        'Core features',
        'Basic reporting',
        'Email support'
      ]
    },
    {
      id: 'premium',
      name: 'Premium',
      price: 999,
      billing: 'per month',
      description: 'Best for growing construction businesses',
      maxUsers: null,
      features: [
        '5 users included + ₹249 for each extra user',
        'Unlimited sites',
        'All features',
        'Advanced reporting',
        'Priority support',
        'Custom workflows'
      ]
    }
  ];

  useEffect(() => {
    fetchCurrentSubscription();
  }, [user]);

  const fetchCurrentSubscription = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching subscription:', error);
      } else if (data) {
        setCurrentSubscription(data);

        // Fetch subscription users if it's a premium plan
        if (data.plan_id === 'premium') {
          await fetchSubscriptionUsersData(data.id);
        }
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchSubscriptionUsersData = async (subscriptionId: string) => {
    try {
      const users = await fetchSubscriptionUsers(subscriptionId);
      setSubscriptionUsers(users);
    } catch (error) {
      console.error('Error fetching subscription users:', error);
      setSubscriptionUsers([]);
    }
  };

  const handleChangePlan = (newPlanId: string) => {
    const newPlan = availablePlans.find(plan => plan.id === newPlanId);
    const currentPlan = availablePlans.find(plan => plan.id === currentSubscription?.plan_id);

    if (!newPlan || !currentPlan) return;

    const isUpgrade = newPlan.price > currentPlan.price;
    const priceChange = Math.abs(newPlan.price - currentPlan.price);

    Alert.alert(
      `${isUpgrade ? 'Upgrade' : 'Downgrade'} Plan`,
      `Are you sure you want to change from ${currentPlan.name} to ${newPlan.name}?\n\n${
        isUpgrade
          ? `Your next bill will increase by ₹${priceChange}/month.`
          : `Your next bill will decrease by ₹${priceChange}/month.`
      }`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: async () => {
            setActionLoading('change-plan');
            try {
              // TODO: Implement plan change logic with proper billing
              await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call

              // Update subscription in database
              const { error } = await supabase
                .from('user_subscriptions')
                .update({
                  plan_id: newPlanId,
                  plan_name: newPlan.name
                })
                .eq('id', currentSubscription?.id);

              if (error) throw error;

              Alert.alert('Success', `Your plan has been changed to ${newPlan.name}.`);
              fetchCurrentSubscription();
            } catch (error) {
              console.error('Error changing plan:', error);
              Alert.alert('Error', 'Failed to change plan. Please try again or contact support.');
            } finally {
              setActionLoading(null);
            }
          }
        }
      ]
    );
  };

  const handleCancelSubscription = () => {
    Alert.alert(
      'Cancel Subscription',
      'Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your current billing period.',
      [
        { text: 'Keep Subscription', style: 'cancel' },
        {
          text: 'Cancel Subscription',
          style: 'destructive',
          onPress: async () => {
            setActionLoading('cancel');
            try {
              // TODO: Implement proper cancellation logic
              await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call

              const { error } = await supabase
                .from('user_subscriptions')
                .update({
                  status: 'cancelled',
                  auto_renew: false
                })
                .eq('id', currentSubscription?.id);

              if (error) throw error;

              Alert.alert(
                'Subscription Cancelled',
                'Your subscription has been cancelled. You will retain access to premium features until the end of your current billing period.',
                [{ text: 'OK', onPress: () => router.back() }]
              );

              fetchCurrentSubscription();
            } catch (error) {
              console.error('Error cancelling subscription:', error);
              Alert.alert('Error', 'Failed to cancel subscription. Please contact support.');
            } finally {
              setActionLoading(null);
            }
          }
        }
      ]
    );
  };

  const handleUpdatePaymentMethod = () => {
    Alert.alert(
      'Update Payment Method',
      'This feature will be available soon. You can contact support for assistance with payment method updates.',
      [{ text: 'OK' }]
    );
  };

  const handleReactivateSubscription = async () => {
    if (currentSubscription?.status !== 'cancelled') return;

    setActionLoading('reactivate');
    try {
      // TODO: Implement proper reactivation logic
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call

      const { error } = await supabase
        .from('user_subscriptions')
        .update({
          status: 'active',
          auto_renew: true
        })
        .eq('id', currentSubscription?.id);

      if (error) throw error;

      Alert.alert('Success', 'Your subscription has been reactivated!');
      fetchCurrentSubscription();
    } catch (error) {
      console.error('Error reactivating subscription:', error);
      Alert.alert('Error', 'Failed to reactivate subscription. Please try again.');
    } finally {
      setActionLoading(null);
    }
  };

  const handleAddUserFromModal = async (phone: string, requiresPayment: boolean, paymentAmount: number) => {
    setNewUserPhone(phone);
    setAddingUser(true);

    try {
      if (requiresPayment) {
        // Handle payment first
        const userLimits = getUserLimits();
        if (!userLimits) {
          throw new Error('Unable to check subscription limits');
        }

        const paymentRequest: AdditionalUserPaymentRequest = {
          subscriptionId: currentSubscription!.id,
          userId: user!.id,
          userEmail: phone, // Using phone as identifier for now
          additionalUserCount: userLimits.additionalUsers + 1,
          totalAmount: paymentAmount
        };

        const paymentResult = await processAdditionalUserPayment(paymentRequest);

        if (!paymentResult.success) {
          throw new Error(paymentResult.error || 'Payment failed');
        }
      }

      // Add the user with the phone number directly
      await addUserToSubscriptionWithPhone(phone);
      setShowAddUserModal(false);

    } catch (error) {
      console.error('Error adding user:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to add user. Please try again.';
      Alert.alert('Error', errorMessage);
    } finally {
      setAddingUser(false);
      setNewUserPhone('');
    }
  };

  const handleAddUser = async () => {
    if (!currentSubscription || !newUserEmail.trim()) return;

    const userLimits = getUserLimits();
    if (!userLimits) {
      Alert.alert('Error', 'Unable to check subscription limits. Please try again.');
      return;
    }

    // Check user addition limits
    const limitCheck = checkUserAdditionLimit(userLimits, 1);

    if (!limitCheck.canProceed) {
      Alert.alert(limitCheck.title, limitCheck.message);
      return;
    }

    if (limitCheck.requiresPayment) {
      // Show payment prompt
      showUserAdditionAlert(
        limitCheck,
        () => addUserToSubscription(), // Proceed without payment (shouldn't happen)
        () => handleAdditionalUserPayment(limitCheck), // Handle payment
        () => {} // Cancel
      );
    } else {
      // Add user without payment (within included limit)
      await addUserToSubscription();
    }
  };

  const handleAdditionalUserPayment = async (limitCheck: any) => {
    if (!currentSubscription || !user) return;

    setAddingUser(true);
    try {
      const userLimits = getUserLimits();
      if (!userLimits) {
        Alert.alert('Error', 'Unable to process payment. Please try again.');
        return;
      }

      const paymentRequest: AdditionalUserPaymentRequest = {
        subscriptionId: currentSubscription.id,
        userId: user.id,
        userEmail: newUserEmail.trim(),
        additionalUserCount: userLimits.additionalUsers + 1, // Current + 1 new user
        totalAmount: limitCheck.paymentAmount
      };

      const paymentResult = await processAdditionalUserPayment(paymentRequest);

      if (paymentResult.success) {
        Alert.alert('Payment Successful', 'Payment completed successfully. You can now add the user.');
        // Now add the user after successful payment
        await addUserToSubscription();
      } else {
        Alert.alert('Payment Failed', paymentResult.error || 'Payment was not completed. Please try again.');
      }
    } catch (error) {
      console.error('Error processing payment:', error);
      Alert.alert('Error', 'Failed to process payment. Please try again.');
    } finally {
      setAddingUser(false);
    }
  };

  const addUserToSubscriptionWithPhone = async (phoneNumber: string) => {
    if (!currentSubscription || !phoneNumber.trim()) {
      console.log('addUserToSubscriptionWithPhone: Missing data', {
        currentSubscription: !!currentSubscription,
        phone: phoneNumber.trim()
      });
      Alert.alert('Error', 'Missing subscription or phone number data');
      return;
    }

    console.log('addUserToSubscriptionWithPhone: Starting with phone:', phoneNumber.trim());

    try {
      // Format phone number - ensure it has 91 prefix for Indian numbers (database format)
      let formattedPhone = phoneNumber.trim();

      // Remove + if present and ensure 91 prefix
      formattedPhone = formattedPhone.replace(/^\+/, '');

      // If it's a 10-digit number, add 91 prefix
      if (/^[6-9]\d{9}$/.test(formattedPhone)) {
        formattedPhone = `91${formattedPhone}`;
      }

      // If it starts with +91, convert to 91
      if (formattedPhone.startsWith('+91')) {
        formattedPhone = formattedPhone.replace('+91', '91');
      }

      console.log('addUserToSubscriptionWithPhone: Formatted phone:', formattedPhone);

      const requestData = {
        subscriptionId: currentSubscription.id,
        phone: formattedPhone,
        invitedBy: user!.id,
        role: 'member'
      };

      console.log('addUserToSubscriptionWithPhone: Request data:', requestData);

      const newUser = await addUserToSubscriptionAPI(requestData);

      console.log('addUserToSubscriptionWithPhone: Success, new user:', newUser);

      Alert.alert('Success', `${newUser.full_name || newUser.email} has been added to your subscription.`);
      setNewUserPhone('');

      // Refresh subscription users
      await fetchSubscriptionUsersData(currentSubscription.id);

    } catch (error) {
      console.error('Error adding user:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to add user. Please try again.';
      Alert.alert('Error', errorMessage);
    }
  };

  const addUserToSubscription = async () => {
    if (!currentSubscription || !newUserPhone.trim()) return;

    await addUserToSubscriptionWithPhone(newUserPhone.trim());
  };

  const handleRemoveUser = (userId: string, userName: string) => {
    Alert.alert(
      'Remove User',
      `Are you sure you want to remove ${userName} from your subscription?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => removeUserFromSubscriptionHandler(userId)
        }
      ]
    );
  };

  const removeUserFromSubscriptionHandler = async (userId: string) => {
    if (!currentSubscription) return;

    try {
      await removeUserFromSubscriptionAPI(currentSubscription.id, userId);
      Alert.alert('Success', 'User has been removed from your subscription.');

      // Refresh subscription users
      await fetchSubscriptionUsersData(currentSubscription.id);

    } catch (error) {
      console.error('Error removing user:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to remove user. Please try again.';
      Alert.alert('Error', errorMessage);
    }
  };

  const renderSubscriptionDetails = () => {
    if (!currentSubscription) return null;

    const isTrialUser = currentSubscription.status === 'trial';
    const isCancelled = currentSubscription.status === 'cancelled';
    const statusColor = isTrialUser ? '#ff9500' : isCancelled ? '#ff3b30' : colors.primary;
    const currentPlan = availablePlans.find(plan => plan.id === currentSubscription.plan_id);

    return (
      <View style={[styles.detailsCard, { backgroundColor: colors.background, borderColor: statusColor }]}>
        <View style={styles.cardHeader}>
          <MaterialCommunityIcons
            name={isTrialUser ? "clock-outline" : isCancelled ? "close-circle" : "check-circle"}
            size={24}
            color={statusColor}
          />
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            Subscription Details
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: colors.icon }]}>Plan:</Text>
          <Text style={[styles.detailValue, { color: colors.text }]}>
            {currentSubscription.plan_name} {isTrialUser ? '(Trial)' : ''}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: colors.icon }]}>Status:</Text>
          <Text style={[styles.detailValue, { color: statusColor }]}>
            {currentSubscription.status.charAt(0).toUpperCase() + currentSubscription.status.slice(1)}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: colors.icon }]}>
            {isTrialUser ? 'Trial ends:' : isCancelled ? 'Access until:' : 'Next billing:'}
          </Text>
          <Text style={[styles.detailValue, { color: colors.text }]}>
            {isTrialUser
              ? (currentSubscription.trial_end
                  ? new Date(currentSubscription.trial_end).toLocaleDateString()
                  : '-')
              : (currentSubscription.current_period_end
                  ? new Date(currentSubscription.current_period_end).toLocaleDateString()
                  : '-')}
          </Text>
        </View>

        {currentPlan && (
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: colors.icon }]}>Price:</Text>
            <Text style={[styles.detailValue, { color: colors.text }]}>
              ₹{currentPlan.price}/{currentPlan.billing}
            </Text>
          </View>
        )}

        {currentSubscription.additional_users > 0 && (
          <View style={styles.detailRow}>
            <Text style={[styles.detailLabel, { color: colors.icon }]}>Additional users:</Text>
            <Text style={[styles.detailValue, { color: colors.text }]}>
              {currentSubscription.additional_users}
            </Text>
          </View>
        )}

        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: colors.icon }]}>Auto-renewal:</Text>
          <Text style={[styles.detailValue, { color: colors.text }]}>
            {currentSubscription.auto_renew ? 'Enabled' : 'Disabled'}
          </Text>
        </View>
      </View>
    );
  };

  const renderAvailablePlans = () => {
    if (!currentSubscription || currentSubscription.status === 'trial') return null;

    return (
      <View style={styles.sectionContainer}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Change Plan</Text>
        {availablePlans.map(plan => {
          const isCurrentPlan = plan.id === currentSubscription.plan_id;
          const isChangingPlan = actionLoading === 'change-plan';

          return (
            <TouchableOpacity
              key={plan.id}
              style={[
                styles.planOption,
                { backgroundColor: colors.background, borderColor: isCurrentPlan ? colors.primary : colors.icon },
                isCurrentPlan && { borderWidth: 2 }
              ]}
              onPress={() => !isCurrentPlan && !isChangingPlan && handleChangePlan(plan.id)}
              disabled={isCurrentPlan || isChangingPlan}
            >
              <View style={styles.planOptionContent}>
                <View style={styles.planOptionHeader}>
                  <Text style={[styles.planOptionName, { color: colors.text }]}>{plan.name}</Text>
                  <Text style={[styles.planOptionPrice, { color: colors.text }]}>
                    ₹{plan.price}/{plan.billing}
                  </Text>
                </View>
                <Text style={[styles.planOptionDescription, { color: colors.icon }]}>
                  {plan.description}
                </Text>
                {isCurrentPlan && (
                  <Text style={[styles.currentPlanLabel, { color: colors.primary }]}>
                    Current Plan
                  </Text>
                )}
              </View>
              {!isCurrentPlan && (
                <MaterialCommunityIcons name="chevron-right" size={24} color={colors.icon} />
              )}
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  const renderUserManagement = () => {
    if (!currentSubscription || currentSubscription.plan_id !== 'premium') return null;

    const userLimits = getUserLimits();
    if (!userLimits) return null;

    const activeUsers = subscriptionUsers.filter(u => u.status === 'active');

    return (
      <View style={styles.sectionContainer}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Users</Text>

        {/* User Limits Banner */}
        <UserLimitsBanner
          userLimits={userLimits}
          planName={currentSubscription.plan_name || 'Premium'}
        />

        {/* Owner Card */}
        <UserCard
          user={{
            id: 'owner',
            user_id: user?.id || '',
            email: user?.email || '',
            phone: user?.phone || '',
            full_name: 'You',
            role: 'owner',
            status: 'active'
          }}
          isOwner={true}
          showRemoveButton={false}
        />

        {/* Active Users */}
        {activeUsers.map((subscriptionUser) => (
          <UserCard
            key={subscriptionUser.id}
            user={subscriptionUser}
            onRemove={handleRemoveUser}
            showRemoveButton={true}
          />
        ))}

        {/* Add User Button */}
        <TouchableOpacity
          style={[styles.addUserButton, { backgroundColor: colors.background, borderColor: colors.primary }]}
          onPress={() => setShowAddUserModal(true)}
          disabled={!!actionLoading}
        >
          <MaterialCommunityIcons name="plus" size={20} color={colors.primary} />
          <Text style={[styles.addUserButtonText, { color: colors.primary }]}>
            Add User
          </Text>
        </TouchableOpacity>
      </View>
    );
  };

  const renderActions = () => {
    if (!currentSubscription) return null;

    const isTrialUser = currentSubscription.status === 'trial';
    const isCancelled = currentSubscription.status === 'cancelled';

    return (
      <View style={styles.sectionContainer}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Actions</Text>

        {!isTrialUser && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.background, borderColor: colors.primary }]}
            onPress={handleUpdatePaymentMethod}
            disabled={!!actionLoading}
          >
            <MaterialCommunityIcons name="credit-card" size={20} color={colors.primary} />
            <Text style={[styles.actionButtonText, { color: colors.primary }]}>
              Update Payment Method
            </Text>
            <MaterialCommunityIcons name="chevron-right" size={20} color={colors.primary} />
          </TouchableOpacity>
        )}

        {isCancelled ? (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.primary }]}
            onPress={handleReactivateSubscription}
            disabled={actionLoading === 'reactivate'}
          >
            {actionLoading === 'reactivate' && (
              <MaterialCommunityIcons name="loading" size={20} color="white" />
            )}
            <Text style={[styles.actionButtonText, { color: 'white' }]}>
              {actionLoading === 'reactivate' ? 'Reactivating...' : 'Reactivate Subscription'}
            </Text>
          </TouchableOpacity>
        ) : !isTrialUser && (
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.background, borderColor: '#ff3b30' }]}
            onPress={handleCancelSubscription}
            disabled={actionLoading === 'cancel'}
          >
            {actionLoading === 'cancel' && (
              <MaterialCommunityIcons name="loading" size={20} color="#ff3b30" />
            )}
            <MaterialCommunityIcons name="close-circle" size={20} color="#ff3b30" />
            <Text style={[styles.actionButtonText, { color: '#ff3b30' }]}>
              {actionLoading === 'cancel' ? 'Cancelling...' : 'Cancel Subscription'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.loadingContainer}>
          <MaterialCommunityIcons name="loading" size={32} color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.text }]}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!currentSubscription) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.emptyContainer}>
          <MaterialCommunityIcons name="information" size={48} color={colors.icon} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>No Subscription Found</Text>
          <Text style={[styles.emptyText, { color: colors.icon }]}>
            You don't have an active subscription. Go back to the subscription page to get started.
          </Text>
          <TouchableOpacity
            style={[styles.emptyButton, { backgroundColor: colors.primary }]}
            onPress={() => router.back()}
          >
            <Text style={styles.emptyButtonText}>View Plans</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderSubscriptionDetails()}
        {renderUserManagement()}
        {renderAvailablePlans()}
        {renderActions()}

        {/* Help Section */}
        <View style={[styles.helpSection, { backgroundColor: 'rgba(59, 130, 246, 0.05)', borderColor: 'rgba(59, 130, 246, 0.1)' }]}>
          <View style={styles.helpHeader}>
            <MaterialCommunityIcons name="help-circle" size={24} color={colors.primary} />
            <Text style={[styles.helpTitle, { color: colors.text }]}>Need Help?</Text>
          </View>
          <Text style={[styles.helpText, { color: colors.icon }]}>
            If you have any questions about your subscription or need assistance, please contact our support team.
          </Text>
          <TouchableOpacity style={[styles.helpButton, { backgroundColor: colors.primary }]}>
            <MaterialCommunityIcons name="email" size={16} color="white" />
            <Text style={styles.helpButtonText}>Contact Support</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Add User Modal */}
      <AddUserModal
        visible={showAddUserModal}
        onClose={() => setShowAddUserModal(false)}
        onAddUser={handleAddUserFromModal}
        userLimits={getUserLimits()}
        loading={addingUser}
      />
    </SafeAreaView>
  );
};

const styles = {
  container: {
    flex: 1,
  },

  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    padding: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold' as const,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    textAlign: 'center' as const,
    lineHeight: 20,
    marginBottom: 24,
  },
  emptyButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  emptyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold' as const,
  },
  detailsCard: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 24,
    borderWidth: 2,
  },
  cardHeader: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold' as const,
    marginLeft: 8,
  },
  detailRow: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    flex: 1,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: '600' as const,
    flex: 1,
    textAlign: 'right' as const,
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold' as const,
    marginBottom: 16,
  },
  planOption: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
  },
  planOptionContent: {
    flex: 1,
  },
  planOptionHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 4,
  },
  planOptionName: {
    fontSize: 16,
    fontWeight: 'bold' as const,
  },
  planOptionPrice: {
    fontSize: 16,
    fontWeight: '600' as const,
  },
  planOptionDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  currentPlanLabel: {
    fontSize: 12,
    fontWeight: '600' as const,
    marginTop: 4,
  },
  actionButton: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600' as const,
    marginHorizontal: 8,
    flex: 1,
    textAlign: 'center' as const,
  },
  helpSection: {
    marginTop: 16,
    padding: 20,
    borderRadius: 12,
    borderWidth: 1,
  },
  helpHeader: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginBottom: 8,
  },
  helpTitle: {
    fontSize: 16,
    fontWeight: '600' as const,
    marginLeft: 8,
  },
  helpText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  helpButton: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    padding: 12,
    borderRadius: 8,
  },
  helpButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600' as const,
    marginLeft: 8,
  },

  // User Management Styles
  userManagementHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 16,
  },
  userCountContainer: {
    alignItems: 'flex-end' as const,
  },
  userCount: {
    fontSize: 14,
    fontWeight: '500' as const,
  },
  additionalUserCount: {
    fontSize: 12,
    fontWeight: '600' as const,
    marginTop: 2,
  },
  userCard: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    flex: 1,
  },
  userDetails: {
    marginLeft: 12,
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600' as const,
    marginBottom: 2,
  },
  userEmail: {
    fontSize: 14,
  },
  userRole: {
    fontSize: 12,
    fontWeight: '600' as const,
  },
  removeButton: {
    padding: 8,
  },
  addUserButton: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderStyle: 'dashed' as const,
    marginBottom: 16,
  },
  addUserButtonText: {
    fontSize: 16,
    fontWeight: '600' as const,
    marginLeft: 8,
  },
  additionalUsersInfo: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  additionalUsersText: {
    fontSize: 14,
    fontWeight: '500' as const,
    marginLeft: 8,
    flex: 1,
  },

  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 16,
    padding: 24,
  },
  modalHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold' as const,
  },
  modalCloseButton: {
    padding: 4,
  },
  modalDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 24,
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600' as const,
    marginBottom: 8,
  },
  textInputContainer: {
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  textInput: {
    fontSize: 16,
    minHeight: 20,
  },
  modalActions: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  modalCancelButton: {
    borderWidth: 1,
  },
  modalAddButton: {
    // backgroundColor will be set dynamically
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600' as const,
  },
};

export default ManageSubscriptionScreen;