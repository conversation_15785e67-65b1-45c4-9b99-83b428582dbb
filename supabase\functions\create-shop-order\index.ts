// @deno-types="https://deno.land/x/xhr@0.3.0/mod.d.ts"
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ShopOrderRequest {
  cart_items: Array<{
    product_id: number;
    quantity: number;
    unit_price: number;
    total_price: number;
  }>;
  total_amount: number;
  delivery_address: string;
  delivery_time?: string;
  notes?: Record<string, string>;
}

interface RazorpayOrderResponse {
  id: string;
  entity: string;
  amount: number;
  amount_paid: number;
  amount_due: number;
  currency: string;
  receipt: string;
  offer_id: string | null;
  status: string;
  attempts: number;
  notes: Record<string, string>;
  created_at: number;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('Create shop order function called')

    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      console.error('No authorization header provided')
      throw new Error('No authorization header')
    }

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: authHeader },
        },
      }
    )

    // Get the current user
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser()
    if (userError || !user) {
      throw new Error('User not authenticated')
    }

    // Parse request body
    console.log('Parsing request body...')
    const { cart_items, total_amount, delivery_address, delivery_time, notes }: ShopOrderRequest = await req.json()
    console.log('Request data:', {
      cart_items_count: cart_items?.length,
      total_amount,
      delivery_address: delivery_address?.substring(0, 50) + '...',
      user_id: user.id
    })

    // Validate required fields
    if (!cart_items || cart_items.length === 0) {
      throw new Error('Cart items are required')
    }
    if (!total_amount || total_amount <= 0) {
      throw new Error('Valid total amount is required')
    }
    if (!delivery_address) {
      throw new Error('Delivery address is required')
    }

    // Generate order number
    const orderNumber = `ORD${Date.now()}${Math.random().toString(36).substring(2, 5).toUpperCase()}`
    
    // Generate receipt for Razorpay
    const receipt = `shop_${orderNumber}_${Date.now()}`

    // Get Razorpay configuration
    const isProduction = Deno.env.get('DENO_DEPLOYMENT_ID') !== undefined
    const razorpayKeyId = isProduction
      ? Deno.env.get('EXPO_PUBLIC_RAZORPAY_KEY_ID_LIVE')
      : Deno.env.get('EXPO_PUBLIC_RAZORPAY_KEY_ID')
    const razorpayKeySecret = isProduction
      ? Deno.env.get('RAZORPAY_KEY_SECRET_LIVE')
      : Deno.env.get('RAZORPAY_KEY_SECRET')

    if (!razorpayKeyId || !razorpayKeySecret) {
      console.error('Razorpay configuration missing:', {
        isProduction,
        hasKeyId: !!razorpayKeyId,
        hasKeySecret: !!razorpayKeySecret,
        keyIdLength: razorpayKeyId?.length,
        keySecretLength: razorpayKeySecret?.length
      })
      throw new Error(`Razorpay configuration not found. Production: ${isProduction}, KeyId: ${!!razorpayKeyId}, KeySecret: ${!!razorpayKeySecret}`)
    }

    // Create Razorpay order
    const razorpayOrderData = {
      amount: Math.round(total_amount * 100), // Convert to paise
      currency: 'INR',
      receipt: receipt,
      notes: {
        ...notes,
        user_id: user.id,
        order_type: 'shop',
        order_number: orderNumber,
        delivery_address: delivery_address,
        created_via: 'supabase_edge_function'
      }
    }

    console.log('Creating Razorpay order:', razorpayOrderData)

    const razorpayResponse = await fetch('https://api.razorpay.com/v1/orders', {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${btoa(`${razorpayKeyId}:${razorpayKeySecret}`)}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(razorpayOrderData),
    })

    if (!razorpayResponse.ok) {
      const errorText = await razorpayResponse.text()
      console.error('Razorpay API error:', errorText)
      throw new Error(`Razorpay API error: ${razorpayResponse.status} ${errorText}`)
    }

    const razorpayOrder: RazorpayOrderResponse = await razorpayResponse.json()
    console.log('Razorpay order created:', razorpayOrder.id)

    // Create shop order in database
    const { data: shopOrder, error: orderError } = await supabaseClient
      .from('shop_orders')
      .insert({
        user_id: user.id,
        order_number: orderNumber,
        status: 'pending',
        total_amount: total_amount,
        razorpay_order_id: razorpayOrder.id,
        payment_status: 'pending',
        delivery_address: delivery_address,
        delivery_time: delivery_time || 'Standard delivery (30-45 mins)',
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    if (orderError) {
      console.error('Error creating shop order:', orderError)
      throw new Error('Failed to create shop order')
    }

    // Create order items
    const orderItems = cart_items.map(item => ({
      order_id: shopOrder.id,
      product_id: item.product_id,
      quantity: item.quantity,
      unit_price: item.unit_price,
      total_price: item.total_price
    }))

    const { error: itemsError } = await supabaseClient
      .from('shop_order_items')
      .insert(orderItems)

    if (itemsError) {
      console.error('Error creating order items:', itemsError)
      throw new Error('Failed to create order items')
    }

    // Log the Razorpay order for tracking
    await supabaseClient
      .from('razorpay_orders')
      .insert({
        order_id: razorpayOrder.id,
        user_id: user.id,
        amount: razorpayOrder.amount,
        currency: razorpayOrder.currency,
        receipt: razorpayOrder.receipt,
        status: 'created',
        plan_id: 'shop_order',
        notes: razorpayOrder.notes,
        razorpay_created_at: new Date(razorpayOrder.created_at * 1000).toISOString()
      })

    return new Response(
      JSON.stringify({
        success: true,
        order: razorpayOrder,
        shop_order: shopOrder,
        message: 'Shop order created successfully'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error in create-shop-order function:', error)
    return new Response(
      JSON.stringify({
        error: error.message,
        message: 'Failed to create shop order'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})
