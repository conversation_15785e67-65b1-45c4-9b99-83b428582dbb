import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export interface UserCardProps {
  user: {
    id: string;
    user_id: string;
    email: string;
    phone?: string;
    full_name?: string;
    role: string;
    status: 'active' | 'invited' | 'removed';
  };
  isOwner?: boolean;
  onRemove?: (userId: string, userName: string) => void;
  showRemoveButton?: boolean;
}

const UserCard: React.FC<UserCardProps> = ({
  user,
  isOwner = false,
  onRemove,
  showRemoveButton = true
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const handleRemove = () => {
    if (onRemove && !isOwner) {
      onRemove(user.user_id, user.full_name || user.email);
    }
  };

  const getUserIcon = () => {
    if (isOwner) {
      return <MaterialCommunityIcons name="crown" size={24} color={colors.primary} />;
    }
    return <MaterialCommunityIcons name="account" size={24} color={colors.icon} />;
  };

  const getUserRole = () => {
    if (isOwner) return 'Owner';
    return user.role.charAt(0).toUpperCase() + user.role.slice(1);
  };

  const getStatusColor = () => {
    switch (user.status) {
      case 'active':
        return colors.primary;
      case 'invited':
        return '#f59e0b';
      case 'removed':
        return '#ef4444';
      default:
        return colors.icon;
    }
  };

  return (
    <View style={[styles.userCard, { backgroundColor: colors.background, borderColor: colors.icon }]}>
      <View style={styles.userInfo}>
        {getUserIcon()}
        <View style={styles.userDetails}>
          <Text style={[styles.userName, { color: colors.text }]}>
            {user.full_name || 'Unknown User'}
            {isOwner && ' (You)'}
          </Text>
          <Text style={[styles.userEmail, { color: colors.icon }]}>
            {isOwner ? user.email : (user.phone || user.email)}
          </Text>
          {user.status !== 'active' && (
            <Text style={[styles.userStatus, { color: getStatusColor() }]}>
              {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
            </Text>
          )}
        </View>
      </View>
      
      <View style={styles.userActions}>
        <Text style={[styles.userRole, { color: isOwner ? colors.primary : colors.icon }]}>
          {getUserRole()}
        </Text>
        
        {showRemoveButton && !isOwner && user.status === 'active' && (
          <TouchableOpacity
            onPress={handleRemove}
            style={styles.removeButton}
          >
            <MaterialCommunityIcons name="close" size={20} color="#ef4444" />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  userCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  userDetails: {
    marginLeft: 12,
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  userEmail: {
    fontSize: 14,
    marginBottom: 2,
  },
  userStatus: {
    fontSize: 12,
    fontWeight: '500',
  },
  userActions: {
    alignItems: 'flex-end',
  },
  userRole: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 8,
  },
  removeButton: {
    padding: 8,
  },
});

export default UserCard;
