import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { supabase } from '@/lib/supabase';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useIsFocused } from '@react-navigation/native';
import * as FileSystem from 'expo-file-system';
import * as Print from 'expo-print';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import * as Sharing from 'expo-sharing';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Alert, FlatList, Modal, Platform, RefreshControl, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Define Transaction interface
interface Transaction {
  id: string;
  type: 'income' | 'expense';
  amount: number;
  description: string;
  transaction_date: string;
  profile_id?: string;
  paid_to_received_from?: string;
  linked_transaction_id?: string;
  counterparty_user_id?: string;
  is_linked?: boolean;
  created_at?: string;
  updated_at?: string;
  effective_linked_id?: string;
  site_id?: string;
  is_original?: boolean;
}

// Define filter type
type FilterType = 'all' | 'income' | 'expense';
type SortOption = 'newest' | 'oldest' | 'highest' | 'lowest';

export default function SiteTransactionsScreen() {
  const insets = useSafeAreaInsets();
  const colorScheme = useColorScheme();
  const primaryColor = Colors[colorScheme ?? 'light'].primary;
  const isDark = colorScheme === 'dark';
  const isFocused = useIsFocused();
  const { id: siteId } = useLocalSearchParams();
  
  const [site, setSite] = useState<any>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [filter, setFilter] = useState<FilterType>('all');
  const [sortBy, setSortBy] = useState<SortOption>('newest');
  const [summary, setSummary] = useState({
    totalIncome: 0,
    totalExpense: 0,
    balance: 0
  });
  
  // Report generation states
  const [showReportModal, setShowReportModal] = useState(false);
  const [generatingReport, setGeneratingReport] = useState(false);
  const [reportDateFrom, setReportDateFrom] = useState(new Date(new Date().getFullYear(), new Date().getMonth(), 1));
  const [reportDateTo, setReportDateTo] = useState(new Date());
  const [showFromDatePicker, setShowFromDatePicker] = useState(false);
  const [showToDatePicker, setShowToDatePicker] = useState(false);
  const [reportType, setReportType] = useState<FilterType>('all');
  
  const subscriptionRef = useRef<any>(null);

  // Professional color scheme
  const colors = {
    background: isDark ? '#0f0f0f' : '#fafafa',
    cardBackground: isDark ? '#1a1a1a' : '#ffffff',
    border: isDark ? '#2a2a2a' : '#e5e7eb',
    text: isDark ? '#ffffff' : '#111827',
    textSecondary: isDark ? '#a1a1a1' : '#6b7280',
    textTertiary: isDark ? '#737373' : '#9ca3af',
    success: '#10b981',
    successLight: '#d1fae5',
    danger: '#ef4444',
    dangerLight: '#fee2e2',
    primary: primaryColor,
    primaryLight: isDark ? primaryColor + '20' : primaryColor + '10',
  };

  // Fetch site details
  const fetchSiteDetails = async () => {
    try {
      const { data, error } = await supabase
        .from('sites')
        .select('*')
        .eq('id', siteId)
        .single();

      if (error) {
        console.error('Error fetching site details:', error);
        return;
      }

      setSite(data);
    } catch (error) {
      console.error('Error in fetchSiteDetails:', error);
    }
  };

  // Setup realtime subscription for transactions
  const setupRealtimeSubscription = async () => {
    try {
      // Clean up any existing subscription first
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
        subscriptionRef.current = null;
      }

      // Create a unique channel name to prevent conflicts
      const channelName = `site-transactions-${siteId}-${Date.now()}`;

      const subscription = supabase
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: 'transactions',
            filter: `site_id=eq.${siteId}`
          },
          () => {
            fetchTransactions();
          }
        )
        .subscribe();

      subscriptionRef.current = subscription;
    } catch (error) {
      console.error('Error setting up realtime subscription:', error);
    }
  };

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('No authenticated user found');
        setLoading(false);
        return;
      }

      // Fetch transactions for this site
      // We'll filter out linked transactions in the next step
      const { data, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('site_id', siteId)
        .order('transaction_date', { ascending: false });

      if (error) {
        console.error('Error fetching site transactions:', error);
        return;
      }

      // Filter out linked transactions - only show original transactions
      // We now mark transactions with is_original field during creation
      // Original transactions have is_original: true
      // Linked transactions have is_original: false

      const filteredTransactions = (data || []).filter(transaction => {
        // Only show original transactions
        // For backward compatibility, if is_original is undefined, assume it's original
        return transaction.is_original !== false;
      });

      console.log(`Fetched ${data?.length || 0} site transactions, filtered to ${filteredTransactions.length} original transactions`);
      setTransactions(filteredTransactions);
      
    } catch (error) {
      console.error('Error in fetchTransactions:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Calculate summary from transactions
  const calculateSummary = () => {
    const income = transactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + parseFloat(t.amount.toString()), 0);
    
    const expense = transactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + parseFloat(t.amount.toString()), 0);
    
    setSummary({
      totalIncome: income,
      totalExpense: expense,
      balance: income - expense
    });
  };

  // Filter and sort transactions
  const getFilteredAndSortedTransactions = () => {
    let result = [...transactions];
    
    // Apply filter
    if (filter !== 'all') {
      result = result.filter(t => t.type === filter);
    }
    
    // Apply sorting
    result.sort((a, b) => {
      const amountA = parseFloat(a.amount.toString());
      const amountB = parseFloat(b.amount.toString());
      const dateA = new Date(a.transaction_date).getTime();
      const dateB = new Date(b.transaction_date).getTime();
      
      switch (sortBy) {
        case 'newest':
          if (dateA !== dateB) {
            return dateB - dateA;
          }
          const createdA = new Date(a.created_at || 0).getTime();
          const createdB = new Date(b.created_at || 0).getTime();
          return createdB - createdA;
        case 'oldest':
          if (dateA !== dateB) {
            return dateA - dateB;
          }
          const createdAOld = new Date(a.created_at || 0).getTime();
          const createdBOld = new Date(b.created_at || 0).getTime();
          return createdAOld - createdBOld;
        case 'highest':
          return amountB - amountA;
        case 'lowest':
          return amountA - amountB;
        default:
          return dateB - dateA;
      }
    });
    
    return result;
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);
      
      if (date.toDateString() === today.toDateString()) {
        return 'Today';
      } else if (date.toDateString() === yesterday.toDateString()) {
        return 'Yesterday';
      } else {
        return date.toLocaleDateString(undefined, {
          month: 'short',
          day: 'numeric'
        });
      }
    } catch (error) {
      return dateString;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Get filtered transactions for report
  const getReportTransactions = () => {
    let result = [...transactions];
    
    // Filter by date range
    result = result.filter(t => {
      const transactionDate = new Date(t.transaction_date);
      return transactionDate >= reportDateFrom && transactionDate <= reportDateTo;
    });
    
    // Filter by type
    if (reportType !== 'all') {
      result = result.filter(t => t.type === reportType);
    }
    
    // Sort by date (newest first)
    result.sort((a, b) => new Date(b.transaction_date).getTime() - new Date(a.transaction_date).getTime());
    
    return result;
  };

  // Calculate report summary
  const getReportSummary = (reportTransactions: Transaction[]) => {
    const income = reportTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + parseFloat(t.amount.toString()), 0);
    
    const expense = reportTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + parseFloat(t.amount.toString()), 0);
    
    return {
      totalIncome: income,
      totalExpense: expense,
      balance: income - expense,
      transactionCount: reportTransactions.length
    };
  };

  // Generate HTML for PDF
  const generatePDFHTML = (reportTransactions: Transaction[], reportSummary: any) => {
    const formatDate = (date: Date) => {
      return date.toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    };

    const formatDateTime = (dateString: string) => {
      const date = new Date(dateString);
      return date.toLocaleString('en-IN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Transaction Report</title>
      <style>
        body {
          font-family: 'Helvetica Neue', Arial, sans-serif;
          margin: 0;
          padding: 20px;
          color: #333;
          line-height: 1.6;
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
          border-bottom: 2px solid #2563eb;
          padding-bottom: 20px;
        }
        .header h1 {
          color: #2563eb;
          margin: 0;
          font-size: 28px;
        }
        .header p {
          margin: 5px 0;
          color: #666;
        }
        .summary {
          display: flex;
          justify-content: space-around;
          margin: 30px 0;
          background: #f8fafc;
          padding: 20px;
          border-radius: 8px;
        }
        .summary-item {
          text-align: center;
        }
        .summary-item h3 {
          margin: 0;
          font-size: 18px;
          color: #374151;
        }
        .summary-item .amount {
          font-size: 24px;
          font-weight: bold;
          margin: 5px 0;
        }
        .income { color: #10b981; }
        .expense { color: #ef4444; }
        .balance { color: ${reportSummary.balance >= 0 ? '#10b981' : '#ef4444'}; }
        .transactions {
          margin-top: 30px;
        }
        .transactions h2 {
          color: #374151;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 10px;
        }
        .transaction {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 15px;
          border: 1px solid #e5e7eb;
          margin-bottom: 10px;
          border-radius: 6px;
          background: white;
        }
        .transaction-left {
          flex: 1;
        }
        .transaction-title {
          font-weight: 600;
          font-size: 16px;
          margin-bottom: 4px;
        }
        .transaction-details {
          font-size: 14px;
          color: #6b7280;
        }
        .transaction-right {
          text-align: right;
        }
        .transaction-amount {
          font-weight: bold;
          font-size: 16px;
          margin-bottom: 4px;
        }
        .transaction-date {
          font-size: 12px;
          color: #9ca3af;
        }
        .footer {
          margin-top: 50px;
          text-align: center;
          font-size: 12px;
          color: #9ca3af;
          border-top: 1px solid #e5e7eb;
          padding-top: 20px;
        }
        @media print {
          body { margin: 0; }
          .summary { break-inside: avoid; }
          .transaction { break-inside: avoid; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Transaction Report</h1>
        <p><strong>Site:</strong> ${site?.name || 'Site Report'}</p>
        <p><strong>Period:</strong> ${formatDate(reportDateFrom)} - ${formatDate(reportDateTo)}</p>
        <p><strong>Report Type:</strong> ${reportType === 'all' ? 'All Transactions' : reportType === 'income' ? 'Income Only' : 'Expenses Only'}</p>
        <p><strong>Generated:</strong> ${formatDateTime(new Date().toISOString())}</p>
      </div>

      <div class="summary">
        <div class="summary-item">
          <h3>Total Income</h3>
          <div class="amount income">${formatCurrency(reportSummary.totalIncome)}</div>
        </div>
        <div class="summary-item">
          <h3>Total Expenses</h3>
          <div class="amount expense">${formatCurrency(reportSummary.totalExpense)}</div>
        </div>
        <div class="summary-item">
          <h3>Net Balance</h3>
          <div class="amount balance">${formatCurrency(Math.abs(reportSummary.balance))}</div>
        </div>
        <div class="summary-item">
          <h3>Transactions</h3>
          <div class="amount">${reportSummary.transactionCount}</div>
        </div>
      </div>

      <div class="transactions">
        <h2>Transaction Details</h2>
        ${reportTransactions.length === 0 ? 
          '<p style="text-align: center; color: #9ca3af; padding: 40px;">No transactions found for the selected period.</p>' :
          reportTransactions.map(transaction => `
            <div class="transaction">
              <div class="transaction-left">
                <div class="transaction-title">${transaction.description}</div>
                <div class="transaction-details">
                  ${transaction.type === 'income' ? 'From' : 'To'}: ${transaction.paid_to_received_from || 'N/A'}
                  ${transaction.is_linked || transaction.linked_transaction_id ? ' • Linked Transaction' : ''}
                </div>
              </div>
              <div class="transaction-right">
                <div class="transaction-amount ${transaction.type}">
                  ${transaction.type === 'income' ? '+' : '-'}${formatCurrency(parseFloat(transaction.amount.toString()))}
                </div>
                <div class="transaction-date">${formatDateTime(transaction.transaction_date)}</div>
              </div>
            </div>
          `).join('')
        }
      </div>

      <div class="footer">
        <p>This report was generated automatically on ${formatDateTime(new Date().toISOString())}</p>
        <p>© ${new Date().getFullYear()} Construction Management System</p>
      </div>
    </body>
    </html>
    `;
  };

  // Generate and share PDF report
  const generateReport = async () => {
    try {
      setGeneratingReport(true);
      
      const reportTransactions = getReportTransactions();
      const reportSummary = getReportSummary(reportTransactions);
      
      if (reportTransactions.length === 0) {
        Alert.alert('No Data', 'No transactions found for the selected date range and filters.');
        return;
      }

      const htmlContent = generatePDFHTML(reportTransactions, reportSummary);
      
      // Generate PDF
      const { uri } = await Print.printToFileAsync({
        html: htmlContent,
        base64: false,
      });

      const reportFileName = `${site?.name || 'Site'}_Transactions_${reportDateFrom.toISOString().split('T')[0]}_to_${reportDateTo.toISOString().split('T')[0]}.pdf`;
      const newPath = `${FileSystem.documentDirectory}${reportFileName}`;
      
      // Move the file to a permanent location
      await FileSystem.moveAsync({
        from: uri,
        to: newPath,
      });

      // Check if sharing is available
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(newPath, {
          mimeType: 'application/pdf',
          dialogTitle: 'Share Transaction Report',
        });
      } else {
        Alert.alert('Success', `Report generated successfully and saved to: ${newPath}`);
      }

      setShowReportModal(false);
      
    } catch (error) {
      console.error('Error generating report:', error);
      Alert.alert('Error', 'Failed to generate report. Please try again.');
    } finally {
      setGeneratingReport(false);
    }
  };

  const renderTransaction = ({ item }: { item: Transaction }) => {
    const isLinked = item.is_linked || item.linked_transaction_id || item.counterparty_user_id;
    const isIncome = item.type === 'income';

    return (
      <TouchableOpacity
        style={[styles.transactionCard, {
          backgroundColor: colors.cardBackground,
          borderColor: colors.border,
        }]}
        activeOpacity={0.7}
        onPress={() => {
          console.log('Transaction clicked:', item.id);
          console.log('siteId:', siteId);

          // Navigate to the simpler route
          router.push(`/transaction-detail?siteId=${siteId}&transactionId=${item.id}`);
        }}
      >
        <View style={styles.transactionHeader}>
          <View style={styles.transactionLeft}>
            <View style={[
              styles.transactionIcon,
              { backgroundColor: isIncome ? colors.successLight : colors.dangerLight }
            ]}>
              <MaterialCommunityIcons 
                name={isIncome ? 'trending-up' : 'trending-down'} 
                size={18} 
                color={isIncome ? colors.success : colors.danger} 
              />
            </View>
            
            <View style={styles.transactionDetails}>
              <Text style={[styles.transactionTitle, { color: colors.text }]} numberOfLines={1}>
                {item.description}
              </Text>
              <Text style={[styles.transactionSubtitle, { color: colors.textSecondary }]} numberOfLines={1}>
                {isIncome ? `From: ${item.paid_to_received_from}` : `To: ${item.paid_to_received_from}`}
              </Text>
            </View>
          </View>
          
          <View style={styles.transactionRight}>
            <Text style={[
              styles.transactionAmount, 
              { color: isIncome ? colors.success : colors.danger }
            ]}>
              {isIncome ? '+' : '-'}{formatCurrency(parseFloat(item.amount.toString()))}
            </Text>
            <Text style={[styles.transactionDate, { color: colors.textTertiary }]}>
              {formatDate(item.transaction_date)}
            </Text>
            {isLinked && (
              <View style={[styles.linkedBadge, { backgroundColor: colors.primaryLight }]}>
                <MaterialCommunityIcons name="link" size={12} color={colors.primary} />
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  // Effects
  useEffect(() => {
    fetchSiteDetails();
  }, [siteId]);

  useEffect(() => {
    fetchTransactions();
  }, [siteId]);

  useEffect(() => {
    calculateSummary();
  }, [transactions]);

  useEffect(() => {
    if (isFocused) {
      setupRealtimeSubscription();
    }

    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
      }
    };
  }, [isFocused]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchTransactions();
  }, []);

  // Get filtered transactions
  const filteredTransactions = getFilteredAndSortedTransactions();

  return (
    <ThemedView style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen
        options={{
          title: 'Transactions',
          headerLargeTitle: false,
          headerStyle: {
            backgroundColor: colors.cardBackground,
          },
          headerTintColor: colors.text,
          headerShadowVisible: false,
        }}
      />

      {/* Professional Summary Section */}
      <View style={[styles.summarySection, { backgroundColor: colors.cardBackground, borderBottomColor: colors.border }]}>
        <View style={styles.summaryHeader}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Financial Overview</Text>
          <TouchableOpacity
            style={[styles.addTransactionBtn, { backgroundColor: colors.primary }]}
            onPress={() => router.push(`/transaction-form?siteId=${siteId}`)}
          >
            <MaterialCommunityIcons name="plus" size={16} color="white" />
            <Text style={styles.addBtnText}>Add</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.summaryGrid}>
          <View style={[styles.summaryCard, { backgroundColor: colors.successLight, borderColor: colors.success + '20' }]}>
            <View style={styles.summaryCardHeader}>
              <MaterialCommunityIcons name="trending-up" size={20} color={colors.success} />
              <Text style={[styles.summaryCardTitle, { color: colors.success }]}>Income</Text>
            </View>
            <Text style={[styles.summaryCardAmount, { color: colors.success }]}>
              {formatCurrency(summary.totalIncome)}
            </Text>
          </View>

          <View style={[styles.summaryCard, { backgroundColor: colors.dangerLight, borderColor: colors.danger + '20' }]}>
            <View style={styles.summaryCardHeader}>
              <MaterialCommunityIcons name="trending-down" size={20} color={colors.danger} />
              <Text style={[styles.summaryCardTitle, { color: colors.danger }]}>Expenses</Text>
            </View>
            <Text style={[styles.summaryCardAmount, { color: colors.danger }]}>
              {formatCurrency(summary.totalExpense)}
            </Text>
          </View>

          <View style={[styles.summaryCard, { 
            backgroundColor: summary.balance >= 0 ? colors.successLight : colors.dangerLight,
            borderColor: (summary.balance >= 0 ? colors.success : colors.danger) + '20'
          }]}>
            <View style={styles.summaryCardHeader}>
              <MaterialCommunityIcons 
                name={summary.balance >= 0 ? "wallet" : "wallet-outline"} 
                size={20} 
                color={summary.balance >= 0 ? colors.success : colors.danger} 
              />
              <Text style={[styles.summaryCardTitle, { 
                color: summary.balance >= 0 ? colors.success : colors.danger 
              }]}>Net Balance</Text>
            </View>
            <Text style={[styles.summaryCardAmount, { 
              color: summary.balance >= 0 ? colors.success : colors.danger 
            }]}>
              {formatCurrency(Math.abs(summary.balance))}
            </Text>
          </View>
        </View>
      </View>

      {/* Professional Filter Section */}
      <View style={[styles.filterSection, { backgroundColor: colors.cardBackground, borderBottomColor: colors.border }]}>
        <View style={styles.filterControls}>
          <View style={[styles.segmentedControl, { backgroundColor: colors.background, borderColor: colors.border }]}>
            {(['all', 'income', 'expense'] as FilterType[]).map((type) => (
              <TouchableOpacity
                key={type}
                style={[
                  styles.segmentButton,
                  filter === type && { backgroundColor: colors.cardBackground, shadowColor: colors.border }
                ]}
                onPress={() => setFilter(type)}
              >
                <Text style={[
                  styles.segmentText, 
                  { color: filter === type ? colors.text : colors.textSecondary },
                  filter === type && styles.segmentTextActive
                ]}>
                  {type === 'all' ? 'All' : type === 'income' ? 'Income' : 'Expenses'}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <TouchableOpacity
            style={[styles.sortButton, { backgroundColor: colors.background, borderColor: colors.border }]}
            onPress={() => {
              const options: SortOption[] = ['newest', 'oldest', 'highest', 'lowest'];
              const currentIndex = options.indexOf(sortBy);
              const nextIndex = (currentIndex + 1) % options.length;
              setSortBy(options[nextIndex]);
            }}
          >
            <MaterialCommunityIcons name="sort" size={16} color={colors.primary} />
            <Text style={[styles.sortText, { color: colors.primary }]}>
              {sortBy === 'newest' ? 'Newest' : 
               sortBy === 'oldest' ? 'Oldest' : 
               sortBy === 'highest' ? 'Highest' : 'Lowest'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Professional Transaction List */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading transactions...</Text>
        </View>
      ) : filteredTransactions.length === 0 ? (
        <View style={styles.emptyContainer}>
          <View style={[styles.emptyIcon, { backgroundColor: colors.border + '40' }]}>
            <MaterialCommunityIcons name="script-text-outline" size={32} color={colors.textTertiary} />
          </View>
          <Text style={[styles.emptyTitle, { color: colors.text }]}>No transactions found</Text>
          <Text style={[styles.emptySubtitle, { color: colors.textSecondary }]}>
            {filter === 'all' 
              ? 'Start by adding your first transaction' 
              : `No ${filter} transactions found for this period`}
          </Text>
          <TouchableOpacity
            style={[styles.emptyAction, { backgroundColor: colors.primary }]}
            onPress={() => router.push(`/transaction-form?siteId=${siteId}`)}
          >
            <MaterialCommunityIcons name="plus" size={16} color="white" />
            <Text style={styles.emptyActionText}>Add Transaction</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <FlatList
          data={filteredTransactions}
          renderItem={renderTransaction}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              tintColor={colors.primary}
              colors={[colors.primary]}
            />
          }
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.transactionsList}
          ItemSeparatorComponent={() => <View style={styles.listSeparator} />}
        />
      )}

      {/* Floating Action Button for Report */}
      <TouchableOpacity
        style={[styles.fab, { backgroundColor: colors.primary }]}
        onPress={() => setShowReportModal(true)}
        activeOpacity={0.8}
      >
        <MaterialCommunityIcons name="file-chart" size={24} color="white" />
      </TouchableOpacity>

      {/* Report Generation Modal */}
      <Modal
        visible={showReportModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowReportModal(false)}
      >
        <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
            <TouchableOpacity
              onPress={() => setShowReportModal(false)}
              style={styles.modalCloseButton}
            >
              <MaterialCommunityIcons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
            <Text style={[styles.modalTitle, { color: colors.text }]}>Generate Report</Text>
            <View style={styles.modalCloseButton} />
          </View>

          <View style={styles.modalContent}>
            {/* Report Type Selection */}
            <View style={styles.modalSection}>
              <Text style={[styles.modalSectionTitle, { color: colors.text }]}>Report Type</Text>
              <View style={[styles.segmentedControl, { backgroundColor: colors.cardBackground, borderColor: colors.border }]}>
                {(['all', 'income', 'expense'] as FilterType[]).map((type) => (
                  <TouchableOpacity
                    key={type}
                    style={[
                      styles.segmentButton,
                      reportType === type && { backgroundColor: colors.primary }
                    ]}
                    onPress={() => setReportType(type)}
                  >
                    <Text style={[
                      styles.segmentText, 
                      { color: reportType === type ? 'white' : colors.textSecondary }
                    ]}>
                      {type === 'all' ? 'All' : type === 'income' ? 'Income' : 'Expenses'}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Date Range Selection */}
            <View style={styles.modalSection}>
              <Text style={[styles.modalSectionTitle, { color: colors.text }]}>Date Range</Text>
              
              <View style={styles.datePickerContainer}>
                <TouchableOpacity
                  style={[styles.datePickerButton, { backgroundColor: colors.cardBackground, borderColor: colors.border }]}
                  onPress={() => setShowFromDatePicker(true)}
                >
                  <MaterialCommunityIcons name="calendar" size={20} color={colors.primary} />
                  <View style={styles.datePickerTextContainer}>
                    <Text style={[styles.datePickerLabel, { color: colors.textSecondary }]}>From</Text>
                    <Text style={[styles.datePickerValue, { color: colors.text }]}>
                      {reportDateFrom.toLocaleDateString('en-IN')}
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.datePickerButton, { backgroundColor: colors.cardBackground, borderColor: colors.border }]}
                  onPress={() => setShowToDatePicker(true)}
                >
                  <MaterialCommunityIcons name="calendar" size={20} color={colors.primary} />
                  <View style={styles.datePickerTextContainer}>
                    <Text style={[styles.datePickerLabel, { color: colors.textSecondary }]}>To</Text>
                    <Text style={[styles.datePickerValue, { color: colors.text }]}>
                      {reportDateTo.toLocaleDateString('en-IN')}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>

            {/* Report Preview */}
            <View style={styles.modalSection}>
              <Text style={[styles.modalSectionTitle, { color: colors.text }]}>Preview</Text>
              <View style={[styles.reportPreview, { backgroundColor: colors.cardBackground, borderColor: colors.border }]}>
                <Text style={[styles.reportPreviewText, { color: colors.textSecondary }]}>
                  {(() => {
                    const previewTransactions = getReportTransactions();
                    const previewSummary = getReportSummary(previewTransactions);
                    return `${previewSummary.transactionCount} transactions • ${formatCurrency(previewSummary.totalIncome)} income • ${formatCurrency(previewSummary.totalExpense)} expenses`;
                  })()}
                </Text>
              </View>
            </View>
          </View>

          <View style={[styles.modalFooter, { borderTopColor: colors.border }]}>
            <TouchableOpacity
              style={[styles.modalButton, styles.modalCancelButton, { borderColor: colors.border }]}
              onPress={() => setShowReportModal(false)}
            >
              <Text style={[styles.modalButtonText, { color: colors.textSecondary }]}>Cancel</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.modalButton, styles.modalGenerateButton, { backgroundColor: colors.primary }]}
              onPress={generateReport}
              disabled={generatingReport}
            >
              {generatingReport ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <React.Fragment>
                  <MaterialCommunityIcons name="download" size={16} color="white" />
                  <Text style={[styles.modalButtonText, { color: 'white', marginLeft: 6 }]}>
                    Generate & Share
                  </Text>
                </React.Fragment>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Date Pickers */}
      {showFromDatePicker && (
        <DateTimePicker
          value={reportDateFrom}
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={(event, selectedDate) => {
            setShowFromDatePicker(false);
            if (selectedDate) {
              setReportDateFrom(selectedDate);
            }
          }}
        />
      )}

      {showToDatePicker && (
        <DateTimePicker
          value={reportDateTo}
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={(event, selectedDate) => {
            setShowToDatePicker(false);
            if (selectedDate) {
              setReportDateTo(selectedDate);
            }
          }}
        />
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  summarySection: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  addTransactionBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  addBtnText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  summaryGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  summaryCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryCardTitle: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 6,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  summaryCardAmount: {
    fontSize: 16,
    fontWeight: '700',
    letterSpacing: -0.5,
  },
  filterSection: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  filterControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  segmentedControl: {
    flexDirection: 'row',
    borderRadius: 10,
    padding: 4,
    borderWidth: 1,
  },
  segmentButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  segmentText: {
    fontSize: 14,
    fontWeight: '500',
  },
  segmentTextActive: {
    fontWeight: '600',
  },
  sortButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  sortText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  emptyIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  emptyAction: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 10,
  },
  emptyActionText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 6,
  },
  transactionsList: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 40,
  },
  transactionCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    letterSpacing: -0.2,
  },
  transactionSubtitle: {
    fontSize: 14,
    lineHeight: 20,
  },
  transactionRight: {
    alignItems: 'flex-end',
    position: 'relative',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 4,
    letterSpacing: -0.3,
  },
  transactionDate: {
    fontSize: 12,
    fontWeight: '500',
  },
  linkedBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  listSeparator: {
    height: 12,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
  },
  modalCloseButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  modalSection: {
    marginBottom: 24,
  },
  modalSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: -0.2,
    marginBottom: 12,
  },
  datePickerContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  datePickerButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  datePickerTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  datePickerLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 2,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  datePickerValue: {
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: -0.2,
  },
  reportPreview: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  reportPreviewText: {
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },
  modalFooter: {
    flexDirection: 'row',
    gap: 12,
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderTopWidth: 1,
  },
  modalButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 1,
  },
  modalCancelButton: {
    backgroundColor: 'transparent',
  },
  modalGenerateButton: {
    borderWidth: 0,
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    letterSpacing: -0.2,
  },
}); 