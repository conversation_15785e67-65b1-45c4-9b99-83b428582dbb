import { Platform, Alert } from 'react-native';
import * as Application from 'expo-application';

export interface MockUpdateInfo {
  updateAvailable: boolean;
  version?: string;
  versionCode?: number;
  mandatory?: boolean;
  releaseNotes?: string;
  updateType: 'immediate' | 'flexible';
  source: 'mock';
}

/**
 * Development-only update manager for testing update flows
 * This provides mock functionality when native updates aren't available
 */
class DevelopmentUpdateManager {
  private static instance: DevelopmentUpdateManager;
  private mockUpdateAvailable = false;
  private mockUpdateInfo: MockUpdateInfo | null = null;

  private constructor() {}

  static getInstance(): DevelopmentUpdateManager {
    if (!DevelopmentUpdateManager.instance) {
      DevelopmentUpdateManager.instance = new DevelopmentUpdateManager();
    }
    return DevelopmentUpdateManager.instance;
  }

  /**
   * Initialize the development update manager
   */
  async initialize(): Promise<boolean> {
    console.log('🔧 Development Update Manager initialized');
    return true;
  }

  /**
   * Set mock update for testing
   */
  setMockUpdate(updateInfo: Partial<MockUpdateInfo> | null): void {
    if (updateInfo) {
      this.mockUpdateInfo = {
        updateAvailable: true,
        version: updateInfo.version || '1.0.1',
        versionCode: updateInfo.versionCode || 15,
        mandatory: updateInfo.mandatory || false,
        releaseNotes: updateInfo.releaseNotes || 'Mock update for development testing',
        updateType: updateInfo.updateType || 'flexible',
        source: 'mock'
      };
      this.mockUpdateAvailable = true;
    } else {
      this.mockUpdateInfo = null;
      this.mockUpdateAvailable = false;
    }
  }

  /**
   * Check for mock updates
   */
  async checkForUpdates(forceCheck: boolean = false): Promise<MockUpdateInfo | null> {
    console.log('🔧 Development: Checking for mock updates...');
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    if (this.mockUpdateAvailable && this.mockUpdateInfo) {
      console.log('🔧 Development: Mock update found!', this.mockUpdateInfo);
      return this.mockUpdateInfo;
    }

    console.log('🔧 Development: No mock updates available');
    return null;
  }

  /**
   * Start mock update flow
   */
  async startUpdateFlow(updateInfo: MockUpdateInfo): Promise<boolean> {
    console.log('🔧 Development: Starting mock update flow...', updateInfo);

    return new Promise((resolve) => {
      Alert.alert(
        '🔧 Development Mode',
        `Mock ${updateInfo.updateType} update flow started!\n\nVersion: ${updateInfo.version}\nType: ${updateInfo.updateType}\nMandatory: ${updateInfo.mandatory}`,
        [
          {
            text: 'Simulate Success',
            onPress: () => {
              console.log('🔧 Development: Mock update completed successfully');
              this.setMockUpdate(null); // Clear mock update
              resolve(true);
            }
          },
          {
            text: 'Simulate Failure',
            style: 'destructive',
            onPress: () => {
              console.log('🔧 Development: Mock update failed');
              resolve(false);
            }
          }
        ]
      );
    });
  }

  /**
   * Show mock update prompt
   */
  showUpdatePrompt(updateInfo: MockUpdateInfo): void {
    const title = updateInfo.updateType === 'immediate' ? '🔧 Mock Update Required' : '🔧 Mock Update Available';
    const message = `Development Mode\n\n${updateInfo.releaseNotes}\n\nThis is a mock update for testing purposes.`;

    const buttons = updateInfo.updateType === 'immediate' 
      ? [{ text: 'Mock Update', onPress: () => this.startUpdateFlow(updateInfo) }]
      : [
          { text: 'Later', style: 'cancel' as const },
          { text: 'Mock Update', onPress: () => this.startUpdateFlow(updateInfo) }
        ];

    Alert.alert(title, message, buttons, { cancelable: updateInfo.updateType !== 'immediate' });
  }

  /**
   * Check and prompt for mock updates
   */
  async checkAndPromptForUpdates(): Promise<void> {
    try {
      const updateInfo = await this.checkForUpdates();
      if (updateInfo) {
        this.showUpdatePrompt(updateInfo);
      }
    } catch (error) {
      console.error('🔧 Development: Error in checkAndPromptForUpdates:', error);
    }
  }

  /**
   * Get current app version info
   */
  getCurrentVersion() {
    return {
      version: Application.nativeApplicationVersion || '1.0.0',
      versionCode: Application.nativeBuildVersion ? parseInt(Application.nativeBuildVersion) : 14,
    };
  }

  /**
   * Enable mock update for testing
   */
  enableMockUpdate(type: 'immediate' | 'flexible' = 'flexible'): void {
    const currentVersion = this.getCurrentVersion();
    this.setMockUpdate({
      version: '1.0.1',
      versionCode: currentVersion.versionCode + 1,
      mandatory: type === 'immediate',
      updateType: type,
      releaseNotes: `Mock ${type} update for development testing.\n\nCurrent: ${currentVersion.version} (${currentVersion.versionCode})\nNew: 1.0.1 (${currentVersion.versionCode + 1})`
    });
    console.log(`🔧 Development: Mock ${type} update enabled`);
  }

  /**
   * Disable mock update
   */
  disableMockUpdate(): void {
    this.setMockUpdate(null);
    console.log('🔧 Development: Mock update disabled');
  }

  /**
   * Check if mock update is available
   */
  hasMockUpdate(): boolean {
    return this.mockUpdateAvailable;
  }

  /**
   * Get mock update info
   */
  getMockUpdateInfo(): MockUpdateInfo | null {
    return this.mockUpdateInfo;
  }
}

// Export singleton instance
export const developmentUpdateManager = DevelopmentUpdateManager.getInstance();

// Global helper functions for easy testing
if (__DEV__) {
  // @ts-ignore - Add to global for easy access in development
  global.enableMockUpdate = (type: 'immediate' | 'flexible' = 'flexible') => {
    developmentUpdateManager.enableMockUpdate(type);
  };

  // @ts-ignore
  global.disableMockUpdate = () => {
    developmentUpdateManager.disableMockUpdate();
  };

  // @ts-ignore
  global.checkMockUpdates = () => {
    developmentUpdateManager.checkAndPromptForUpdates();
  };

  console.log('🔧 Development Update Manager loaded!');
  console.log('🔧 Use these commands in console:');
  console.log('   enableMockUpdate("flexible") - Enable flexible mock update');
  console.log('   enableMockUpdate("immediate") - Enable immediate mock update');
  console.log('   disableMockUpdate() - Disable mock update');
  console.log('   checkMockUpdates() - Check for mock updates');
}
