import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { supabase } from '@/lib/supabase';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import { format, parseISO } from 'date-fns';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  useColorScheme,
  View
} from 'react-native';

// Work categories array
const WORK_CATEGORIES = [
  'Foundation Work',
  'Structural Work',
  'Masonry',
  'Roofing',
  'Electrical',
  'Plumbing',
  'HVAC',
  'Interior Finishing',
  'Exterior Finishing',
  'Landscaping',
  'Site Preparation',
  'Concrete Work',
  'Steel Work',
  'Carpentry',
  'Painting',
  'Flooring',
  'Windows and Doors',
  'Insulation',
  'Waterproofing',
  'Safety and Security',
];

// Units of measure array
const UNITS_OF_MEASURE = [
  'Square Meter',
  'Cubic Meter',
  'Linear Meter',
  'Kilogram',
  'Piece',
  'Lot',
  'Set',
  'Hour',
  'Day',
  'Week',
  'No.s',
  'Box',
  'Each',
  'Feet',
  'Running Feet',
  'Square Feet',
  'Tones',
  'Liter',
  'Cubic Feet',
  'Bag',
  'Other',
];

// Status options
const STATUS_OPTIONS = [
  { label: 'Pending', value: 'pending' as const, color: '#f59e0b' },
  { label: 'In Progress', value: 'in progress' as const, color: '#3b82f6' },
  { label: 'Completed', value: 'completed' as const, color: '#10b981' },
] as const;

// Type for task
type Task = {
  id: string;
  site_id: string;
  name: string;
  work_category: string;
  status: 'pending' | 'in progress' | 'completed';
  overall_progress: number;
  due_date: string;
  created_at: string;
  updated_at: string;
  created_by: string;
};

// Type for subcategory
type Subcategory = {
  id: string;
  task_id: string;
  name: string;
  quantity: number;
  completed_quantity: number;
  unit_of_measure: string;
  progress_percentage: number;
  created_at?: string;
  updated_at?: string;
};

// Type for UI subcategory state
type SubcategoryState = {
  id: string;
  name: string;
  quantity: string;
  completed_quantity: string;
  unit_of_measure: string;
  progress_percentage: number;
  isNew?: boolean;
  deleted?: boolean;
};

// Generate a simple unique ID for subcategories (replacement for uuid)
const generateId = () => {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
};

export default function EditTaskScreen() {
  const { id: siteId, taskId } = useLocalSearchParams();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  // Task state
  const [task, setTask] = useState<Task | null>(null);
  const [name, setName] = useState('');
  const [category, setCategory] = useState('');
  const [status, setStatus] = useState<'pending' | 'in progress' | 'completed'>('pending');
  const [dueDate, setDueDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [overallProgress, setOverallProgress] = useState(0);
  
  // Subcategories state
  const [subcategories, setSubcategories] = useState<SubcategoryState[]>([]);
  
  // Validation state
  const [nameError, setNameError] = useState('');
  const [categoryError, setCategoryError] = useState('');
  const [subcategoryErrors, setSubcategoryErrors] = useState<{[key: string]: {name: string; quantity: string; completedQuantity: string; unit: string}}>({}); 
  
  // Loading state
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [userRole, setUserRole] = useState<string | null>(null);

  // Fetch task data
  useEffect(() => {
    const fetchTaskData = async () => {
      if (!siteId || !taskId) return;
      
      try {
        setLoading(true);
        
        // Get current user
        const { data: userData } = await supabase.auth.getUser();
        if (!userData.user) return;
        
        // Get user role for the site
        const { data: userMemberData } = await supabase
          .from('site_members')
          .select('role')
          .eq('site_id', siteId)
          .eq('user_id', userData.user.id)
          .single();
        
        if (userMemberData) {
          setUserRole(userMemberData.role);
          
          if (userMemberData.role === 'Member') {
            Alert.alert('Permission Denied', 'You do not have permission to edit tasks');
            router.back();
            return;
          }
        }
        
        // Fetch task data
        const { data: taskData, error: taskError } = await supabase
          .from('tasks')
          .select('*')
          .eq('id', taskId)
          .single();
        
        if (taskError || !taskData) {
          console.error('Error fetching task:', taskError);
          Alert.alert('Error', 'Failed to load task details');
          router.back();
          return;
        }
        
        // Set task data
        setTask(taskData as Task);
        setName(taskData.name);
        setCategory(taskData.work_category);
        setStatus(taskData.status);
        setDueDate(parseISO(taskData.due_date));
        setOverallProgress(taskData.overall_progress);
        
        // Fetch subcategories
        const { data: subcategoriesData, error: subcategoriesError } = await supabase
          .from('task_subcategories')
          .select('*')
          .eq('task_id', taskId)
          .order('created_at', { ascending: true });
        
        if (!subcategoriesError && subcategoriesData) {
          // Convert subcategory data to state format
          const subcategoryState = subcategoriesData.map((subcat) => ({
            id: subcat.id,
            name: subcat.name,
            quantity: subcat.quantity.toString(),
            completed_quantity: subcat.completed_quantity.toString(),
            unit_of_measure: subcat.unit_of_measure,
            progress_percentage: subcat.progress_percentage
          }));
          
          setSubcategories(subcategoryState);
        }
        
      } catch (error) {
        console.error('Error:', error);
        Alert.alert('Error', 'An unexpected error occurred');
        router.back();
      } finally {
        setLoading(false);
      }
    };
    
    fetchTaskData();
  }, [siteId, taskId]);

  // Handle date picker
  const onDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setDueDate(selectedDate);
    }
  };
  
  // Add subcategory
  const addSubcategory = () => {
    setSubcategories([
      ...subcategories,
      { 
        id: generateId(),
        name: '', 
        quantity: '', 
        completed_quantity: '0',
        unit_of_measure: '',
        progress_percentage: 0,
        isNew: true
      }
    ]);
  };
  
  // Remove subcategory
  const removeSubcategory = (id: string) => {
    if (subcategories.length === 1) {
      Alert.alert('Cannot Remove', 'At least one subcategory is required');
      return;
    }
    
    // If it's an existing subcategory (from database), mark it for deletion
    // If it's a new subcategory (added during editing), remove it completely
    const updatedSubcategories = subcategories.map(item => 
      item.id === id && !item.isNew ? {...item, deleted: true} : item
    ).filter(item => !(item.id === id && item.isNew));
    
    setSubcategories(updatedSubcategories);
    
    // Clear errors for removed subcategory
    const updatedErrors = {...subcategoryErrors};
    delete updatedErrors[id];
    setSubcategoryErrors(updatedErrors);
    
    // Update overall progress
    calculateOverallProgress(updatedSubcategories.filter(item => !item.deleted));
  };
  
  // Update subcategory field
  const updateSubcategory = (id: string, field: keyof SubcategoryState, value: string) => {
    const updatedSubcategories = subcategories.map(item => {
      if (item.id === id) {
        const updatedItem = {...item, [field]: value};
        
        // If completed_quantity or quantity changed, update progress percentage
        if (field === 'completed_quantity' || field === 'quantity') {
          const quantity = parseFloat(updatedItem.quantity) || 0;
          const completedQuantity = parseFloat(updatedItem.completed_quantity) || 0;
          
          if (quantity > 0) {
            updatedItem.progress_percentage = Math.min(Math.round((completedQuantity / quantity) * 100), 100);
          } else {
            updatedItem.progress_percentage = 0;
          }
        }
        
        return updatedItem;
      }
      return item;
    });
    
    setSubcategories(updatedSubcategories);
    
    // Update overall progress
    calculateOverallProgress(updatedSubcategories.filter(item => !item.deleted));
    
    // Clear error when field is updated
    if (subcategoryErrors[id]) {
      const fieldMap: {[key: string]: keyof typeof subcategoryErrors[string]} = {
        name: 'name',
        quantity: 'quantity',
        completed_quantity: 'completedQuantity',
        unit_of_measure: 'unit'
      };
      
      const updatedErrors = {...subcategoryErrors};
      if (updatedErrors[id] && fieldMap[field]) {
        updatedErrors[id] = {
          ...updatedErrors[id],
          [fieldMap[field]]: ''
        };
      }
      setSubcategoryErrors(updatedErrors);
    }
  };
  
  // Calculate overall progress from subcategories
  const calculateOverallProgress = (activeSubcategories: SubcategoryState[]) => {
    if (activeSubcategories.length === 0) {
      setOverallProgress(0);
      return;
    }
    
    const totalProgress = activeSubcategories.reduce(
      (sum, subcategory) => sum + subcategory.progress_percentage, 
      0
    );
    
    const newOverallProgress = Math.round(totalProgress / activeSubcategories.length);
    setOverallProgress(newOverallProgress);
    
    // Update status based on progress
    if (newOverallProgress === 100) {
      setStatus('completed');
    } else if (newOverallProgress > 0 && status === 'pending') {
      setStatus('in progress');
    }
  };
  
  // Validate form
  const validateForm = () => {
    let isValid = true;
    
    // Validate task name
    if (!name.trim()) {
      setNameError('Task name is required');
      isValid = false;
    } else {
      setNameError('');
    }
    
    // Validate category
    if (!category) {
      setCategoryError('Work category is required');
      isValid = false;
    } else {
      setCategoryError('');
    }
    
    // Validate subcategories
    const errors: typeof subcategoryErrors = {};
    
    subcategories.filter(subcategory => !subcategory.deleted).forEach(subcategory => {
      const subcategoryError = {
        name: '',
        quantity: '',
        completedQuantity: '',
        unit: ''
      };
      
      if (!subcategory.name.trim()) {
        subcategoryError.name = 'Name is required';
        isValid = false;
      }
      
      if (!subcategory.quantity.trim()) {
        subcategoryError.quantity = 'Quantity is required';
        isValid = false;
      } else if (isNaN(Number(subcategory.quantity)) || Number(subcategory.quantity) <= 0) {
        subcategoryError.quantity = 'Must be a positive number';
        isValid = false;
      }
      
      if (!subcategory.completed_quantity.trim()) {
        subcategoryError.completedQuantity = 'Required';
        isValid = false;
      } else if (isNaN(Number(subcategory.completed_quantity)) || Number(subcategory.completed_quantity) < 0) {
        subcategoryError.completedQuantity = 'Must be a positive number';
        isValid = false;
      } else if (Number(subcategory.completed_quantity) > Number(subcategory.quantity)) {
        subcategoryError.completedQuantity = 'Cannot exceed target';
        isValid = false;
      }
      
      if (!subcategory.unit_of_measure) {
        subcategoryError.unit = 'Unit is required';
        isValid = false;
      }
      
      if (subcategoryError.name || subcategoryError.quantity || subcategoryError.completedQuantity || subcategoryError.unit) {
        errors[subcategory.id] = subcategoryError;
      }
    });
    
    setSubcategoryErrors(errors);
    return isValid;
  };
  
  // Save changes
  const handleSaveChanges = async () => {
    if (!validateForm()) {
      // Scroll to the top to show validation errors
      return;
    }
    
    try {
      setSaving(true);
      
      // Update task
      const { error: taskError } = await supabase
        .from('tasks')
        .update({
          name,
          work_category: category,
          status,
          overall_progress: overallProgress,
          due_date: format(dueDate, 'yyyy-MM-dd')
        })
        .eq('id', taskId);
      
      if (taskError) {
        console.error('Error updating task:', taskError);
        Alert.alert('Error', 'Failed to update task');
        return;
      }
      
      // Process subcategories: update existing, add new, delete marked
      const activeSubcategories = subcategories.filter(subcategory => !subcategory.deleted);
      
      // Update or insert subcategories
      for (const subcategory of activeSubcategories) {
        const subcategoryData = {
          task_id: taskId,
          name: subcategory.name,
          quantity: parseFloat(subcategory.quantity),
          completed_quantity: parseFloat(subcategory.completed_quantity),
          unit_of_measure: subcategory.unit_of_measure,
          progress_percentage: subcategory.progress_percentage
        };
        
        if (subcategory.isNew) {
          // Insert new subcategory
          const { error } = await supabase
            .from('task_subcategories')
            .insert(subcategoryData);
          
          if (error) {
            console.error('Error creating subcategory:', error);
            Alert.alert('Warning', 'Some new subcategories failed to save');
          }
        } else {
          // Update existing subcategory
          const { error } = await supabase
            .from('task_subcategories')
            .update(subcategoryData)
            .eq('id', subcategory.id);
          
          if (error) {
            console.error('Error updating subcategory:', error);
            Alert.alert('Warning', 'Some subcategory updates failed');
          }
        }
      }
      
      // Delete subcategories marked for deletion
      const subcategoriesToDelete = subcategories
        .filter(subcategory => subcategory.deleted && !subcategory.isNew)
        .map(subcategory => subcategory.id);
      
      if (subcategoriesToDelete.length > 0) {
        const { error } = await supabase
          .from('task_subcategories')
          .delete()
          .in('id', subcategoriesToDelete);
        
        if (error) {
          console.error('Error deleting subcategories:', error);
          Alert.alert('Warning', 'Some subcategories could not be deleted');
        }
      }
      
      Alert.alert(
        'Success',
        'Task updated successfully',
        [{ text: 'OK', onPress: () => router.back() }]
      );
      
    } catch (error) {
      console.error('Error updating task:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setSaving(false);
    }
  };
  
  // Cancel editing
  const handleCancel = () => {
    Alert.alert(
      'Cancel Editing',
      'Are you sure you want to cancel? All changes will be lost.',
      [
        { text: 'No', style: 'cancel' },
        { text: 'Yes', onPress: () => router.back() }
      ]
    );
  };

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#f97316" />
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      
      <Stack.Screen options={{ title: 'Update Task' }} />
      
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.headerContainer}>
            <ThemedText style={styles.headerTitle}>Edit Task</ThemedText>
            <ThemedText style={styles.headerSubtitle}>Update task details and track progress</ThemedText>
          </View>
          
          {/* Overall Progress Bar */}
          <View style={styles.overallProgressSection}>
            <ThemedText style={styles.overallProgressTitle}>Overall Progress: {overallProgress}%</ThemedText>
            <View style={styles.progressBarContainer}>
              <View 
                style={[
                  styles.progressBar, 
                  { width: `${overallProgress}%`, backgroundColor: '#f97316' }
                ]} 
              />
            </View>
          </View>
          
          {/* Task Information Section */}
          <View style={styles.section}>
            <ThemedText style={styles.sectionTitle}>Task Information</ThemedText>
            
            <View style={styles.formGroup}>
              <View style={styles.labelContainer}>
                <ThemedText style={styles.label}>Task Name</ThemedText>
                <Text style={styles.required}>*</Text>
              </View>
              <TextInput
                style={[
                  styles.input, 
                  isDark && styles.inputDark,
                  nameError ? styles.inputError : null
                ]}
                placeholder="Enter task name"
                placeholderTextColor={isDark ? "#94a3b8" : "#94a3b8"}
                value={name}
                onChangeText={(text) => {
                  setName(text);
                  if (text.trim()) setNameError('');
                }}
              />
              {nameError ? <Text style={styles.errorText}>{nameError}</Text> : null}
            </View>
            
            <View style={styles.formGroup}>
              <View style={styles.labelContainer}>
                <ThemedText style={styles.label}>Work Category</ThemedText>
                <Text style={styles.required}>*</Text>
              </View>
              <View style={[
                styles.pickerContainer, 
                isDark && styles.pickerContainerDark,
                categoryError ? styles.pickerContainerError : null
              ]}>
                <Picker
                  selectedValue={category}
                  onValueChange={(itemValue) => {
                    setCategory(itemValue);
                    if (itemValue) setCategoryError('');
                  }}
                  style={styles.picker}
                  dropdownIconColor={isDark ? '#fff' : '#000'}
                  itemStyle={{ color: isDark ? '#ffffff' : '#000000' }}
                >
                  <Picker.Item label="Select a category" value="" color={isDark ? "#94a3b8" : "#94a3b8"} />
                  {WORK_CATEGORIES.map((cat) => (
                    <Picker.Item key={cat} label={cat} value={cat} color={isDark ? "#ffffff" : undefined} />
                  ))}
                </Picker>
              </View>
              {categoryError ? <Text style={styles.errorText}>{categoryError}</Text> : null}
            </View>
            
            <View style={styles.formGroup}>
              <ThemedText style={styles.label}>Status</ThemedText>
              <View style={styles.statusContainer}>
                {STATUS_OPTIONS.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.statusOption,
                      isDark && styles.statusOptionDark,
                      status === option.value && { backgroundColor: option.color },
                    ]}
                    onPress={() => setStatus(option.value)}
                  >
                    <Text style={[
                      styles.statusText,
                      isDark && styles.statusTextDark,
                      status === option.value && styles.statusTextSelected
                    ]}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            
            <View style={styles.formGroup}>
              <View style={styles.labelContainer}>
                <ThemedText style={styles.label}>Due Date</ThemedText>
                <Text style={styles.required}>*</Text>
              </View>
              <TouchableOpacity
                style={[styles.datePickerButton, isDark && styles.datePickerButtonDark]}
                onPress={() => setShowDatePicker(true)}
              >
                <ThemedText style={styles.dateText}>
                  {format(dueDate, 'MMMM d, yyyy')}
                </ThemedText>
                <MaterialIcons name="event" size={24} color={isDark ? '#fff' : '#000'} />
              </TouchableOpacity>
              
              {showDatePicker && (
                <DateTimePicker
                  value={dueDate}
                  mode="date"
                  display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                  onChange={onDateChange}
                  minimumDate={new Date()}
                  textColor={isDark ? '#ffffff' : undefined}
                />
              )}
            </View>
          </View>
          
          {/* Subcategories Section */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <ThemedText style={styles.sectionTitle}>Subcategories</ThemedText>
              <TouchableOpacity 
                style={styles.addButton}
                onPress={addSubcategory}
              >
                <MaterialIcons name="add" size={20} color="#fff" />
                <Text style={styles.addButtonText}>Add Subcategory</Text>
              </TouchableOpacity>
            </View>
            
            {subcategories.filter(s => !s.deleted).map((subcategory, index) => (
              <View key={subcategory.id} style={[
                styles.subcategoryContainer,
                isDark && styles.subcategoryContainerDark
              ]}>
                <View style={styles.subcategoryHeader}>
                  <ThemedText style={styles.subcategoryTitle}>
                    Subcategory {index + 1}
                  </ThemedText>
                  {subcategories.filter(s => !s.deleted).length > 1 && (
                    <TouchableOpacity 
                      style={styles.removeButton}
                      onPress={() => removeSubcategory(subcategory.id)}
                    >
                      <MaterialIcons name="delete" size={20} color="#ef4444" />
                    </TouchableOpacity>
                  )}
                </View>
                
                <View style={styles.formGroup}>
                  <View style={styles.labelContainer}>
                    <ThemedText style={styles.label}>Name</ThemedText>
                    <Text style={styles.required}>*</Text>
                  </View>
                  <TextInput
                    style={[
                      styles.input, 
                      isDark && styles.inputDark,
                      subcategoryErrors[subcategory.id]?.name ? styles.inputError : null
                    ]}
                    placeholder="Enter subcategory name"
                    placeholderTextColor={isDark ? "#94a3b8" : "#94a3b8"}
                    value={subcategory.name}
                    onChangeText={(text) => updateSubcategory(subcategory.id, 'name', text)}
                  />
                  {subcategoryErrors[subcategory.id]?.name ? (
                    <Text style={styles.errorText}>{subcategoryErrors[subcategory.id].name}</Text>
                  ) : null}
                </View>
                
                <View style={styles.formRow}>
                  <View style={[styles.formGroup, styles.formGroupHalf]}>
                    <View style={styles.labelContainer}>
                      <ThemedText style={styles.label}>Target Quantity</ThemedText>
                      <Text style={styles.required}>*</Text>
                    </View>
                    <TextInput
                      style={[
                        styles.input, 
                        isDark && styles.inputDark,
                        subcategoryErrors[subcategory.id]?.quantity ? styles.inputError : null
                      ]}
                      placeholder="0"
                      placeholderTextColor={isDark ? "#94a3b8" : "#94a3b8"}
                      value={subcategory.quantity}
                      onChangeText={(text) => updateSubcategory(subcategory.id, 'quantity', text)}
                      keyboardType="numeric"
                    />
                    {subcategoryErrors[subcategory.id]?.quantity ? (
                      <Text style={styles.errorText}>{subcategoryErrors[subcategory.id].quantity}</Text>
                    ) : null}
                  </View>
                  
                  <View style={[styles.formGroup, styles.formGroupHalf]}>
                    <View style={styles.labelContainer}>
                      <ThemedText style={styles.label}>Unit</ThemedText>
                      <Text style={styles.required}>*</Text>
                    </View>
                    <View style={[
                      styles.pickerContainer, 
                      isDark && styles.pickerContainerDark,
                      subcategoryErrors[subcategory.id]?.unit ? styles.pickerContainerError : null
                    ]}>
                      <Picker
                        selectedValue={subcategory.unit_of_measure}
                        onValueChange={(itemValue) => updateSubcategory(subcategory.id, 'unit_of_measure', itemValue)}
                        style={styles.picker}
                        dropdownIconColor={isDark ? '#fff' : '#000'}
                        itemStyle={{ color: isDark ? '#ffffff' : '#000000' }}
                      >
                        <Picker.Item label="Select unit" value="" color={isDark ? "#94a3b8" : "#94a3b8"} />
                        {UNITS_OF_MEASURE.map((unit) => (
                          <Picker.Item key={unit} label={unit} value={unit} color={isDark ? "#ffffff" : undefined} />
                        ))}
                      </Picker>
                    </View>
                    {subcategoryErrors[subcategory.id]?.unit ? (
                      <Text style={styles.errorText}>{subcategoryErrors[subcategory.id].unit}</Text>
                    ) : null}
                  </View>
                </View>
                
                <View style={styles.formGroup}>
                  <View style={styles.labelContainer}>
                    <ThemedText style={styles.label}>Completed Quantity</ThemedText>
                    <Text style={styles.required}>*</Text>
                  </View>
                  <TextInput
                    style={[
                      styles.input, 
                      isDark && styles.inputDark,
                      subcategoryErrors[subcategory.id]?.completedQuantity ? styles.inputError : null
                    ]}
                    placeholder="0"
                    placeholderTextColor={isDark ? "#94a3b8" : "#94a3b8"}
                    value={subcategory.completed_quantity}
                    onChangeText={(text) => updateSubcategory(subcategory.id, 'completed_quantity', text)}
                    keyboardType="numeric"
                  />
                  {subcategoryErrors[subcategory.id]?.completedQuantity ? (
                    <Text style={styles.errorText}>{subcategoryErrors[subcategory.id].completedQuantity}</Text>
                  ) : null}
                </View>
                
                {/* Subcategory Progress Bar */}
                <View style={styles.subcategoryProgressContainer}>
                  <View style={styles.subcategoryProgressLabelRow}>
                    <ThemedText style={styles.subcategoryProgressLabel}>Progress</ThemedText>
                    <ThemedText style={styles.subcategoryProgressPercentage}>{subcategory.progress_percentage}%</ThemedText>
                  </View>
                  <View style={styles.progressBarContainer}>
                    <View 
                      style={[
                        styles.progressBar, 
                        { 
                          width: `${subcategory.progress_percentage}%`,
                          backgroundColor: '#f97316'
                        }
                      ]} 
                    />
                  </View>
                </View>
              </View>
            ))}
          </View>
          
          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={styles.cancelButton}
              onPress={handleCancel}
              disabled={saving}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.saveButton}
              onPress={handleSaveChanges}
              disabled={saving}
            >
              {saving ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.saveButtonText}>Save Changes</Text>
              )}
            </TouchableOpacity>
          </View>
          
          {/* Add extra padding at bottom for better spacing on all screens */}
          <View style={styles.bottomPadding} />
        </ScrollView>
      </KeyboardAvoidingView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
    width: '100%',
  },
  headerContainer: {
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#94a3b8',
  },
  overallProgressSection: {
    marginBottom: 20,
  },
  overallProgressTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  progressBarContainer: {
    height: 20,
    backgroundColor: '#e5e7eb',
    borderRadius: 10,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#f97316',
  },
  section: {
    backgroundColor: 'rgba(100, 100, 100, 0.06)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    width: '100%',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  formGroup: {
    marginBottom: 10,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  label: {
    fontSize: 16,
  },
  required: {
    color: 'red',
    marginLeft: 5,
  },
  input: {
    flex: 1,
    padding: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    fontSize: 16,
    color: '#000000',
  },
  inputDark: {
    borderColor: '#334155',
    color: '#ffffff',
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
  },
  inputError: {
    borderColor: 'red',
  },
  pickerContainer: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 5,
    backgroundColor: 'transparent',
  },
  pickerContainerDark: {
    borderColor: '#334155',
    color: '#ffffff',
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
  },
  pickerContainerError: {
    borderColor: 'red',
  },
  picker: {
    flex: 1,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginTop: 5,
  },
  statusOption: {
    flex: 1,
    minWidth: 100,
    padding: 10,
    margin: 2,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 5,
    alignItems: 'center',
  },
  statusOptionDark: {
    borderColor: '#334155',
  },
  statusText: {
    fontSize: 16,
  },
  statusTextDark: {
    color: '#ffffff',
  },
  statusTextSelected: {
    fontWeight: 'bold',
    color: '#ffffff',
  },
  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
  },
  datePickerButtonDark: {
    borderColor: '#334155',
  },
  dateText: {
    fontSize: 16,
    marginRight: 10,
  },
  subcategoryContainer: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 8,
    width: '100%',
  },
  subcategoryContainerDark: {
    backgroundColor: 'rgba(30, 41, 59, 0.5)',
  },
  subcategoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  subcategoryTitle: {
    fontSize: 16,
  },
  removeButton: {
    padding: 5,
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    width: '100%',
  },
  formGroupHalf: {
    width: '48%',
    minWidth: 120,
  },
  subcategoryProgressContainer: {
    marginBottom: 10,
  },
  subcategoryProgressLabelRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  subcategoryProgressLabel: {
    fontSize: 16,
  },
  subcategoryProgressPercentage: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    marginBottom: 8,
    paddingHorizontal: 5,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#94a3b8',
    padding: 14,
    borderRadius: 8,
    marginRight: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#f97316',
    padding: 14,
    borderRadius: 8,
    marginLeft: 8,
    alignItems: 'center',
  },
  saveButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  addButton: {
    backgroundColor: '#f97316',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 5,
    paddingHorizontal: 10,
    borderRadius: 5,
  },
  addButtonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 5,
  },
  bottomPadding: {
    height: 80, // Increased extra padding at bottom
  },
}); 