import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { supabase } from '@/lib/supabase';
import { MaterialIcons } from '@expo/vector-icons';
import { router, Stack, useLocalSearchParams, useNavigation } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useCallback, useEffect, useLayoutEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Animated,
  Dimensions,
  FlatList,
  KeyboardAvoidingView,
  Modal,
  PanResponder,
  Platform,
  RefreshControl,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';

const { width, height } = Dimensions.get('window');

type SubContractor = {
  id: string;
  name: string;
  category: string;
  site_id: string;
  created_at: string;
  updated_at: string;
};

type Site = {
  id: string;
  name: string;
  organization_name: string;
};

export default function SubContractorsScreen() {
  const { id } = useLocalSearchParams();
  const siteId = Array.isArray(id) ? id[0] : id;
  const colorScheme = useColorScheme();
  const navigation = useNavigation();
  
  const [subContractors, setSubContractors] = useState<SubContractor[]>([]);
  const [site, setSite] = useState<Site | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  
  // Form states
  const [name, setName] = useState('');
  const [category, setCategory] = useState('');
  
  // Bottom sheet animation
  const [slideAnim] = useState(new Animated.Value(height));
  const [backdropOpacity] = useState(new Animated.Value(0));

  // Set navigation options immediately to prevent route path from showing
  useLayoutEffect(() => {
    navigation.setOptions({
      title: 'Sub-Contractors',
      headerTitle: 'Sub-Contractors',
      headerBackTitle: 'Back',
      headerShown: true,
      headerTitleAlign: 'center',
      headerRight: () => (
        <TouchableOpacity onPress={() => {
          setShowAddModal(true);
          Animated.parallel([
            Animated.spring(slideAnim, {
              toValue: 0,
              useNativeDriver: true,
              tension: 100,
              friction: 8,
            }),
            Animated.timing(backdropOpacity, {
              toValue: 1,
              duration: 300,
              useNativeDriver: true,
            }),
          ]).start();
        }} style={styles.headerButton}>
          <MaterialIcons name="add" size={24} color="#f97316" />
        </TouchableOpacity>
      ),
    });
  }, [navigation, slideAnim, backdropOpacity]);

  // Pan responder for swipe to dismiss
  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: (_, gestureState) => {
      return Math.abs(gestureState.dy) > 5;
    },
    onPanResponderMove: (_, gestureState) => {
      if (gestureState.dy > 0) {
        slideAnim.setValue(gestureState.dy);
      }
    },
    onPanResponderRelease: (_, gestureState) => {
      if (gestureState.dy > 100) {
        closeModal();
      } else {
        Animated.spring(slideAnim, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      }
    },
  });

  const loadData = async () => {
    if (!siteId) return;
    
    try {
      // Load site info
      const { data: siteData, error: siteError } = await supabase
        .from('sites')
        .select('id, name, organization_name')
        .eq('id', siteId)
        .single();

      if (siteError) throw siteError;
      setSite(siteData);

      // Load sub-contractors
      const { data: subContractorsData, error: subContractorsError } = await supabase
        .from('sub_contractors')
        .select('*')
        .eq('site_id', siteId)
        .order('created_at', { ascending: false });

      if (subContractorsError) throw subContractorsError;
      setSubContractors(subContractorsData || []);
    } catch (error) {
      console.error('Error loading data:', error);
      Alert.alert('Error', 'Failed to load data. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadData();
  }, [siteId]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadData();
  }, []);

  const openModal = () => {
    setShowAddModal(true);
    Animated.parallel([
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }),
      Animated.timing(backdropOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const closeModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: height,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(backdropOpacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setShowAddModal(false);
      resetForm();
    });
  };

  const resetForm = () => {
    setName('');
    setCategory('');
  };

  const handleSubmit = async () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter a name for the sub-contractor');
      return;
    }

    if (!category.trim()) {
      Alert.alert('Error', 'Please enter a category');
      return;
    }

    setSubmitting(true);

    try {
      const { data, error } = await supabase
        .from('sub_contractors')
        .insert([
          {
            name: name.trim(),
            category: category.trim(),
            site_id: siteId,
          }
        ])
        .select()
        .single();

      if (error) throw error;

      setSubContractors(prev => [data, ...prev]);
      closeModal();
      
      Alert.alert('Success', 'Sub-contractor added successfully');
    } catch (error) {
      console.error('Error adding sub-contractor:', error);
      Alert.alert('Error', 'Failed to add sub-contractor. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteSubContractor = (item: SubContractor) => {
    Alert.alert(
      'Delete Sub-Contractor',
      `Are you sure you want to delete "${item.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('sub_contractors')
                .delete()
                .eq('id', item.id);

              if (error) throw error;

              setSubContractors(prev => prev.filter(sc => sc.id !== item.id));
              Alert.alert('Success', 'Sub-contractor deleted successfully');
            } catch (error) {
              console.error('Error deleting sub-contractor:', error);
              Alert.alert('Error', 'Failed to delete sub-contractor');
            }
          }
        }
      ]
    );
  };

  const getCategoryIcon = (category: string) => {
    const iconMap: { [key: string]: string } = {
      'Electrical': 'electrical-services',
      'Plumbing': 'plumbing',
      'Painting': 'brush',
      'Masonry': 'construction',
      'Carpentry': 'carpenter',
      'Roofing': 'roofing',
      'Flooring': 'floor',
      'HVAC': 'air',
      'Landscaping': 'grass',
      'Security': 'security',
      'Other': 'work'
    };
    return iconMap[category] || 'work';
  };

  const getCategoryColor = (category: string) => {
    const colorMap: { [key: string]: string } = {
      'Electrical': '#f59e0b',
      'Plumbing': '#3b82f6',
      'Painting': '#ec4899',
      'Masonry': '#8b5cf6',
      'Carpentry': '#f97316',
      'Roofing': '#ef4444',
      'Flooring': '#10b981',
      'HVAC': '#06b6d4',
      'Landscaping': '#84cc16',
      'Security': '#6366f1',
      'Other': '#64748b'
    };
    return colorMap[category] || '#64748b';
  };

  const renderSubContractorItem = ({ item }: { item: SubContractor }) => {
    const categoryColor = getCategoryColor(item.category);
    const categoryIcon = getCategoryIcon(item.category);

    return (
      <TouchableOpacity 
        style={[
          styles.subContractorCard,
          { backgroundColor: colorScheme === 'dark' ? '#1f2937' : '#ffffff' }
        ]}
        activeOpacity={0.7}
        onPress={() => router.push(`/site/${siteId}/sub-contractor-attendance?contractorId=${item.id}&contractorName=${encodeURIComponent(item.name)}`)}
      >
        <View style={styles.cardContent}>
          <View style={[styles.iconContainer, { backgroundColor: `${categoryColor}15` }]}>
            <MaterialIcons name={categoryIcon as any} size={24} color={categoryColor} />
          </View>
          
          <View style={styles.infoContainer}>
            <ThemedText style={styles.contractorName}>{item.name}</ThemedText>
            <View style={styles.categoryBadge}>
              <ThemedText style={[styles.categoryText, { color: categoryColor }]}>
                {item.category}
              </ThemedText>
            </View>
          </View>
          
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleDeleteSubContractor(item)}
          >
            <MaterialIcons name="more-vert" size={20} color="#64748b" />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <View style={[styles.emptyIconContainer, { backgroundColor: 'rgba(249, 115, 22, 0.1)' }]}>
        <MaterialIcons name="engineering" size={64} color="#f97316" />
      </View>
      <ThemedText style={styles.emptyTitle}>No Sub-Contractors Yet</ThemedText>
      <ThemedText style={styles.emptyDescription}>
        Add sub-contractors to manage different categories of work on your site
      </ThemedText>
      <TouchableOpacity style={styles.emptyActionButton} onPress={openModal}>
        <MaterialIcons name="add" size={20} color="#ffffff" />
        <ThemedText style={styles.emptyActionText}>Add First Sub-Contractor</ThemedText>
      </TouchableOpacity>
    </View>
  );

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#f97316" />
        <ThemedText style={styles.loadingText}>Loading sub-contractors...</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      
      <Stack.Screen
        options={{
          title: 'Sub-Contractors',
          headerTitle: 'Sub-Contractors',
          headerBackTitle: 'Back',
          headerShown: true,
          headerTitleAlign: 'center',
          headerRight: () => (
            <TouchableOpacity onPress={openModal} style={styles.headerButton}>
              <MaterialIcons name="add" size={24} color="#f97316" />
            </TouchableOpacity>
          ),
        }}
      />

      {subContractors.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={subContractors}
          renderItem={renderSubContractorItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={['#f97316']} />
          }
          showsVerticalScrollIndicator={false}
        />
      )}

      {/* Add Sub-Contractor Bottom Sheet Modal */}
      <Modal
        visible={showAddModal}
        transparent
        animationType="none"
        onRequestClose={closeModal}
      >
        <View style={styles.modalContainer}>
          <Animated.View 
            style={[styles.backdrop, { opacity: backdropOpacity }]}
          >
            <TouchableOpacity 
              style={styles.backdropTouchable}
              onPress={closeModal}
              activeOpacity={1}
            />
          </Animated.View>
          
          <Animated.View
            style={[
              styles.bottomSheet,
              {
                transform: [{ translateY: slideAnim }],
                backgroundColor: colorScheme === 'dark' ? '#1f2937' : '#ffffff'
              }
            ]}
            {...panResponder.panHandlers}
          >
            <View style={styles.sheetHandle} />
            
            <View style={styles.sheetHeader}>
              <ThemedText style={styles.sheetTitle}>Add Sub-Contractor</ThemedText>
              <TouchableOpacity onPress={closeModal}>
                <MaterialIcons name="close" size={24} color="#64748b" />
              </TouchableOpacity>
            </View>

            <KeyboardAvoidingView
              behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
              style={styles.sheetContent}
            >
              <View style={styles.formSection}>
                <View style={styles.inputContainer}>
                  <ThemedText style={styles.inputLabel}>Name *</ThemedText>
                  <TextInput
                    style={[
                      styles.input,
                      { 
                        backgroundColor: colorScheme === 'dark' ? '#374151' : '#f8fafc',
                        color: colorScheme === 'dark' ? '#ffffff' : '#1f2937'
                      }
                    ]}
                    value={name}
                    onChangeText={setName}
                    placeholder="Enter sub-contractor name"
                    placeholderTextColor="#94a3b8"
                    autoFocus
                    maxLength={100}
                  />
                </View>

                <View style={styles.inputContainer}>
                  <ThemedText style={styles.inputLabel}>Category *</ThemedText>
                  <TextInput
                    style={[
                      styles.input,
                      { 
                        backgroundColor: colorScheme === 'dark' ? '#374151' : '#f8fafc',
                        color: colorScheme === 'dark' ? '#ffffff' : '#1f2937'
                      }
                    ]}
                    value={category}
                    onChangeText={setCategory}
                    placeholder="Enter category (e.g., Electrical, Plumbing)"
                    placeholderTextColor="#94a3b8"
                    maxLength={100}
                  />
                </View>
              </View>

              <View style={styles.actionButtons}>
                <TouchableOpacity
                  style={[styles.cancelButton, { backgroundColor: colorScheme === 'dark' ? '#374151' : '#f1f5f9' }]}
                  onPress={closeModal}
                >
                  <ThemedText style={styles.cancelButtonText}>Cancel</ThemedText>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={[
                    styles.submitButton,
                    (!name.trim() || !category.trim() || submitting) && styles.disabledButton
                  ]}
                  onPress={handleSubmit}
                  disabled={!name.trim() || !category.trim() || submitting}
                >
                  {submitting ? (
                    <ActivityIndicator size="small" color="#ffffff" />
                  ) : (
                    <>
                      <MaterialIcons name="add" size={20} color="#ffffff" />
                      <ThemedText style={styles.submitButtonText}>Add Sub-Contractor</ThemedText>
                    </>
                  )}
                </TouchableOpacity>
              </View>
            </KeyboardAvoidingView>
          </Animated.View>
        </View>
      </Modal>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#64748b',
  },
  headerButton: {
    padding: 8,
  },
  listContainer: {
    padding: 16,
  },
  subContractorCard: {
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  infoContainer: {
    flex: 1,
  },
  contractorName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  categoryBadge: {
    alignSelf: 'flex-start',
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  actionButton: {
    padding: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  emptyActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f97316',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  emptyActionText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  backdrop: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  backdropTouchable: {
    flex: 1,
  },
  bottomSheet: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: height * 0.8,
    minHeight: 400,
  },
  sheetHandle: {
    width: 36,
    height: 4,
    backgroundColor: '#d1d5db',
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  sheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  sheetTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  sheetContent: {
    flex: 1,
    paddingHorizontal: 24,
  },
  formSection: {
    flex: 1,
    paddingTop: 24,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
    color: '#374151',
  },
  input: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },

  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    paddingVertical: 24,
  },
  cancelButton: {
    flex: 1,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#64748b',
  },
  submitButton: {
    flex: 2,
    backgroundColor: '#f97316',
    borderRadius: 8,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
  },
  disabledButton: {
    backgroundColor: '#94a3b8',
  },
  submitButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
}); 