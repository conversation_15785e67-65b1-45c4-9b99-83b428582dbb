import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { supabase } from '@/lib/supabase';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { Picker } from '@react-native-picker/picker';
import { addDays, format } from 'date-fns';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';

// Work categories array
const WORK_CATEGORIES = [
  'Foundation Work',
  'Structural Work',
  'Masonry',
  'Roofing',
  'Electrical',
  'Plumbing',
  'HVAC',
  'Interior Finishing',
  'Exterior Finishing',
  'Landscaping',
  'Site Preparation',
  'Concrete Work',
  'Steel Work',
  'Carpentry',
  'Painting',
  'Flooring',
  'Windows and Doors',
  'Insulation',
  'Waterproofing',
  'Safety and Security',
];

// Units of measure array
const UNITS_OF_MEASURE = [
  'Square Meter',
  'Cubic Meter',
  'Linear Meter',
  'Kilogram',
  'Piece',
  'Lot',
  'Set',
  'Hour',
  'Day',
  'Week',
  'No.s',
  'Box',
  'Each',
  'Feet',
  'Running Feet',
  'Square Feet',
  'Tones',
  'Liter',
  'Cubic Feet',
  'Bag',
  'Other',
];

// Status options
const STATUS_OPTIONS = [
  { label: 'Pending', value: 'pending', color: '#f59e0b' },
  { label: 'In Progress', value: 'in progress', color: '#3b82f6' },
  { label: 'Completed', value: 'completed', color: '#10b981' },
];

// Type for subcategory
type Subcategory = {
  id: string;
  name: string;
  quantity: string;
  unit_of_measure: string;
};

// Add a simple ID generator function before the component
// Generate a simple unique ID for subcategories (replacement for uuid)
const generateId = () => {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
};

export default function CreateTaskScreen() {
  const { id: siteId } = useLocalSearchParams();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  // Form state
  const [name, setName] = useState('');
  const [category, setCategory] = useState('');
  const [status, setStatus] = useState('pending');
  const [dueDate, setDueDate] = useState(addDays(new Date(), 7));
  const [showDatePicker, setShowDatePicker] = useState(false);
  
  // Validation state
  const [nameError, setNameError] = useState('');
  const [categoryError, setCategoryError] = useState('');
  const [subcategoryErrors, setSubcategoryErrors] = useState<{[key: string]: {name: string; quantity: string; unit: string}}>({}); 
  
  // Subcategories state
  const [subcategories, setSubcategories] = useState<Subcategory[]>([
    { 
      id: generateId(), // Use our custom generator instead of uuidv4
      name: '', 
      quantity: '', 
      unit_of_measure: '' 
    }
  ]);
  
  // Loading state
  const [loading, setLoading] = useState(false);
  const [initialChecking, setInitialChecking] = useState(true);
  const [userRole, setUserRole] = useState<string | null>(null);
  
  // Check user permissions
  useEffect(() => {
    const checkUserPermissions = async () => {
      try {
        // Get current user
        const { data: userData } = await supabase.auth.getUser();
        if (!userData.user) {
          Alert.alert('Error', 'You must be logged in to create tasks');
          router.back();
          return;
        }
        
        // Get user role for the site
        const { data: userMemberData, error } = await supabase
          .from('site_members')
          .select('role')
          .eq('site_id', siteId)
          .eq('user_id', userData.user.id)
          .single();
        
        if (error || !userMemberData) {
          Alert.alert('Error', 'You do not have permission to access this site');
          router.back();
          return;
        }
        
        if (userMemberData.role === 'Member') {
          Alert.alert('Permission Denied', 'You do not have permission to create tasks');
          router.back();
          return;
        }
        
        setUserRole(userMemberData.role);
      } catch (error) {
        console.error('Error checking user permissions:', error);
        Alert.alert('Error', 'An unexpected error occurred');
        router.back();
      } finally {
        setInitialChecking(false);
      }
    };
    
    checkUserPermissions();
  }, [siteId]);
  
  // Handle date picker
  const onDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setDueDate(selectedDate);
    }
  };
  
  // Add subcategory
  const addSubcategory = () => {
    setSubcategories([
      ...subcategories,
      { 
        id: generateId(), // Use our custom generator instead of uuidv4
        name: '', 
        quantity: '', 
        unit_of_measure: '' 
      }
    ]);
  };
  
  // Remove subcategory
  const removeSubcategory = (id: string) => {
    if (subcategories.length === 1) {
      Alert.alert('Cannot Remove', 'At least one subcategory is required');
      return;
    }
    
    setSubcategories(subcategories.filter(item => item.id !== id));
    
    // Clear errors for removed subcategory
    const updatedErrors = {...subcategoryErrors};
    delete updatedErrors[id];
    setSubcategoryErrors(updatedErrors);
  };
  
  // Update subcategory field
  const updateSubcategory = (id: string, field: keyof Subcategory, value: string) => {
    const updatedSubcategories = subcategories.map(item => 
      item.id === id ? {...item, [field]: value} : item
    );
    setSubcategories(updatedSubcategories);
    
    // Clear error when field is updated
    if (subcategoryErrors[id]) {
      const fieldMap: {[key: string]: keyof typeof subcategoryErrors[string]} = {
        name: 'name',
        quantity: 'quantity',
        unit_of_measure: 'unit'
      };
      
      const updatedErrors = {...subcategoryErrors};
      if (updatedErrors[id]) {
        updatedErrors[id] = {
          ...updatedErrors[id],
          [fieldMap[field]]: ''
        };
      }
      setSubcategoryErrors(updatedErrors);
    }
  };
  
  // Validate form
  const validateForm = () => {
    let isValid = true;
    
    // Validate task name
    if (!name.trim()) {
      setNameError('Task name is required');
      isValid = false;
    } else {
      setNameError('');
    }
    
    // Validate category
    if (!category) {
      setCategoryError('Work category is required');
      isValid = false;
    } else {
      setCategoryError('');
    }
    
    // Validate subcategories
    const errors: typeof subcategoryErrors = {};
    
    subcategories.forEach(subcategory => {
      const subcategoryError = {
        name: '',
        quantity: '',
        unit: ''
      };
      
      if (!subcategory.name.trim()) {
        subcategoryError.name = 'Name is required';
        isValid = false;
      }
      
      if (!subcategory.quantity.trim()) {
        subcategoryError.quantity = 'Quantity is required';
        isValid = false;
      } else if (isNaN(Number(subcategory.quantity)) || Number(subcategory.quantity) <= 0) {
        subcategoryError.quantity = 'Must be a positive number';
        isValid = false;
      }
      
      if (!subcategory.unit_of_measure) {
        subcategoryError.unit = 'Unit is required';
        isValid = false;
      }
      
      if (subcategoryError.name || subcategoryError.quantity || subcategoryError.unit) {
        errors[subcategory.id] = subcategoryError;
      }
    });
    
    setSubcategoryErrors(errors);
    return isValid;
  };
  
  // Create task
  const handleCreateTask = async () => {
    if (!validateForm()) {
      // Scroll to the top to show validation errors
      return;
    }
    
    try {
      setLoading(true);
      
      // Get user info
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) {
        Alert.alert('Error', 'User not found');
        return;
      }
      
      // Insert task using the user ID directly (not the profile ID)
      const { data: task, error: taskError } = await supabase
        .from('tasks')
        .insert({
          site_id: siteId,
          name,
          work_category: category,
          status,
          overall_progress: 0, // Initialize at 0%
          due_date: format(dueDate, 'yyyy-MM-dd'),
          created_by: userData.user.id // Use user ID directly, not profile ID
        })
        .select()
        .single();
      
      if (taskError || !task) {
        console.error('Error creating task:', taskError);
        Alert.alert('Error', 'Failed to create task: ' + taskError?.message);
        return;
      }
      
      // Insert subcategories
      const subcategoryRecords = subcategories.map(subcategory => ({
        task_id: task.id,
        name: subcategory.name,
        quantity: parseFloat(subcategory.quantity),
        completed_quantity: 0,
        unit_of_measure: subcategory.unit_of_measure,
        progress_percentage: 0
      }));
      
      const { error: subcategoryError } = await supabase
        .from('task_subcategories')
        .insert(subcategoryRecords);
      
      if (subcategoryError) {
        console.error('Error creating subcategories:', subcategoryError);
        Alert.alert('Warning', 'Task created but some subcategories failed to save');
      }
      
      Alert.alert(
        'Success',
        'Task created successfully',
        [{ text: 'OK', onPress: () => router.back() }]
      );
      
    } catch (error) {
      console.error('Error creating task:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };
  
  // Cancel task creation
  const handleCancel = () => {
    Alert.alert(
      'Cancel Task Creation',
      'Are you sure you want to cancel? All entered data will be lost.',
      [
        { text: 'No', style: 'cancel' },
        { text: 'Yes', onPress: () => router.back() }
      ]
    );
  };
  
  if (initialChecking) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#f97316" />
      </ThemedView>
    );
  }
  
  return (
    <ThemedView style={styles.container}>
      <StatusBar style={isDark ? 'light' : 'dark'} />
      
      <Stack.Screen options={{ title: 'Create Task' }} />
      
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.headerContainer}>
            <ThemedText style={styles.headerTitle}>Create New Task</ThemedText>
            <ThemedText style={styles.headerSubtitle}>Add task details and subcategories</ThemedText>
          </View>
          
          {/* Task Information Section */}
          <View style={styles.section}>
            <ThemedText style={styles.sectionTitle}>Task Information</ThemedText>
            
            <View style={styles.formGroup}>
              <View style={styles.labelContainer}>
                <ThemedText style={styles.label}>Task Name</ThemedText>
                <Text style={styles.required}>*</Text>
              </View>
              <TextInput
                style={[
                  styles.input, 
                  isDark && styles.inputDark,
                  nameError ? styles.inputError : null
                ]}
                placeholder="Enter task name"
                placeholderTextColor="#94a3b8"
                value={name}
                onChangeText={(text) => {
                  setName(text);
                  if (text.trim()) setNameError('');
                }}
              />
              {nameError ? <Text style={styles.errorText}>{nameError}</Text> : null}
            </View>
            
            <View style={styles.formGroup}>
              <View style={styles.labelContainer}>
                <ThemedText style={styles.label}>Work Category</ThemedText>
                <Text style={styles.required}>*</Text>
              </View>
              <View style={[
                styles.pickerContainer, 
                isDark && styles.pickerContainerDark,
                categoryError ? styles.pickerContainerError : null
              ]}>
                <Picker
                  selectedValue={category}
                  onValueChange={(itemValue) => {
                    setCategory(itemValue);
                    if (itemValue) setCategoryError('');
                  }}
                  style={styles.picker}
                  dropdownIconColor={isDark ? '#fff' : '#000'}
                >
                  <Picker.Item label="Select a category" value="" color="#94a3b8" />
                  {WORK_CATEGORIES.map((cat) => (
                    <Picker.Item key={cat} label={cat} value={cat} />
                  ))}
                </Picker>
              </View>
              {categoryError ? <Text style={styles.errorText}>{categoryError}</Text> : null}
            </View>
            
            <View style={styles.formGroup}>
              <ThemedText style={styles.label}>Status</ThemedText>
              <View style={styles.statusContainer}>
                {STATUS_OPTIONS.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.statusOption,
                      status === option.value && { backgroundColor: option.color },
                    ]}
                    onPress={() => setStatus(option.value)}
                  >
                    <Text style={[
                      styles.statusText,
                      status === option.value && styles.statusTextSelected
                    ]}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
            
            <View style={styles.formGroup}>
              <View style={styles.labelContainer}>
                <ThemedText style={styles.label}>Due Date</ThemedText>
                <Text style={styles.required}>*</Text>
              </View>
              <TouchableOpacity
                style={[styles.datePickerButton, isDark && styles.datePickerButtonDark]}
                onPress={() => setShowDatePicker(true)}
              >
                <ThemedText style={styles.dateText}>
                  {format(dueDate, 'MMMM d, yyyy')}
                </ThemedText>
                <MaterialIcons name="event" size={24} color={isDark ? '#fff' : '#000'} />
              </TouchableOpacity>
              
              {showDatePicker && (
                <DateTimePicker
                  value={dueDate}
                  mode="date"
                  display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                  onChange={onDateChange}
                  minimumDate={new Date()}
                />
              )}
            </View>
          </View>
          
          {/* Subcategories Section */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <ThemedText style={styles.sectionTitle}>Subcategories</ThemedText>
              <TouchableOpacity 
                style={styles.addButton}
                onPress={addSubcategory}
              >
                <MaterialIcons name="add" size={20} color="#fff" />
                <Text style={styles.addButtonText}>Add Subcategory</Text>
              </TouchableOpacity>
            </View>
            
            {subcategories.map((subcategory, index) => (
              <View key={subcategory.id} style={styles.subcategoryContainer}>
                <View style={styles.subcategoryHeader}>
                  <ThemedText style={styles.subcategoryTitle}>
                    Subcategory {index + 1}
                  </ThemedText>
                  {subcategories.length > 1 && (
                    <TouchableOpacity 
                      style={styles.removeButton}
                      onPress={() => removeSubcategory(subcategory.id)}
                    >
                      <MaterialIcons name="delete" size={20} color="#ef4444" />
                    </TouchableOpacity>
                  )}
                </View>
                
                <View style={styles.formGroup}>
                  <View style={styles.labelContainer}>
                    <ThemedText style={styles.label}>Name</ThemedText>
                    <Text style={styles.required}>*</Text>
                  </View>
                  <TextInput
                    style={[
                      styles.input, 
                      isDark && styles.inputDark,
                      subcategoryErrors[subcategory.id]?.name ? styles.inputError : null
                    ]}
                    placeholder="Enter subcategory name"
                    placeholderTextColor="#94a3b8"
                    value={subcategory.name}
                    onChangeText={(text) => updateSubcategory(subcategory.id, 'name', text)}
                  />
                  {subcategoryErrors[subcategory.id]?.name ? (
                    <Text style={styles.errorText}>{subcategoryErrors[subcategory.id].name}</Text>
                  ) : null}
                </View>
                
                <View style={styles.formRow}>
                  <View style={[styles.formGroup, styles.formGroupHalf]}>
                    <View style={styles.labelContainer}>
                      <ThemedText style={styles.label}>Quantity</ThemedText>
                      <Text style={styles.required}>*</Text>
                    </View>
                    <TextInput
                      style={[
                        styles.input, 
                        isDark && styles.inputDark,
                        subcategoryErrors[subcategory.id]?.quantity ? styles.inputError : null
                      ]}
                      placeholder="0"
                      placeholderTextColor="#94a3b8"
                      value={subcategory.quantity}
                      onChangeText={(text) => updateSubcategory(subcategory.id, 'quantity', text)}
                      keyboardType="numeric"
                    />
                    {subcategoryErrors[subcategory.id]?.quantity ? (
                      <Text style={styles.errorText}>{subcategoryErrors[subcategory.id].quantity}</Text>
                    ) : null}
                  </View>
                  
                  <View style={[styles.formGroup, styles.formGroupHalf]}>
                    <View style={styles.labelContainer}>
                      <ThemedText style={styles.label}>Unit</ThemedText>
                      <Text style={styles.required}>*</Text>
                    </View>
                    <View style={[
                      styles.pickerContainer, 
                      isDark && styles.pickerContainerDark,
                      subcategoryErrors[subcategory.id]?.unit ? styles.pickerContainerError : null
                    ]}>
                      <Picker
                        selectedValue={subcategory.unit_of_measure}
                        onValueChange={(itemValue) => updateSubcategory(subcategory.id, 'unit_of_measure', itemValue)}
                        style={styles.picker}
                        dropdownIconColor={isDark ? '#fff' : '#000'}
                      >
                        <Picker.Item label="Select unit" value="" color="#94a3b8" />
                        {UNITS_OF_MEASURE.map((unit) => (
                          <Picker.Item key={unit} label={unit} value={unit} />
                        ))}
                      </Picker>
                    </View>
                    {subcategoryErrors[subcategory.id]?.unit ? (
                      <Text style={styles.errorText}>{subcategoryErrors[subcategory.id].unit}</Text>
                    ) : null}
                  </View>
                </View>
              </View>
            ))}
          </View>
          
          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={styles.cancelButton}
              onPress={handleCancel}
              disabled={loading}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.createButton}
              onPress={handleCreateTask}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color="#fff" />
              ) : (
                <Text style={styles.createButtonText}>Create Task</Text>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 40,
  },
  headerContainer: {
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 8,
  },
  section: {
    backgroundColor: 'rgba(100, 100, 100, 0.06)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  formGroupHalf: {
    width: '48%',
  },
  labelContainer: {
    flexDirection: 'row',
    marginBottom: 6,
  },
  label: {
    fontSize: 15,
    fontWeight: '500',
  },
  required: {
    color: '#ef4444',
    marginLeft: 4,
  },
  input: {
    borderWidth: 1,
    borderColor: 'rgba(100, 100, 100, 0.2)',
    borderRadius: 8,
    padding: 12,
    fontSize: 15,
    backgroundColor: '#fff',
  },
  inputDark: {
    backgroundColor: '#1e293b',
    borderColor: 'rgba(255, 255, 255, 0.2)',
    color: '#fff',
  },
  inputError: {
    borderColor: '#ef4444',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 12,
    marginTop: 4,
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: 'rgba(100, 100, 100, 0.2)',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  pickerContainerDark: {
    backgroundColor: '#1e293b',
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  pickerContainerError: {
    borderColor: '#ef4444',
  },
  picker: {
    height: 50,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statusOption: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(100, 100, 100, 0.2)',
    marginHorizontal: 4,
  },
  statusText: {
    fontWeight: '500',
  },
  statusTextSelected: {
    color: '#fff',
    fontWeight: 'bold',
  },
  datePickerButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(100, 100, 100, 0.2)',
    borderRadius: 8,
    padding: 12,
    backgroundColor: '#fff',
  },
  datePickerButtonDark: {
    backgroundColor: '#1e293b',
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  dateText: {
    fontSize: 15,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f97316',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  addButtonText: {
    color: '#fff',
    fontWeight: '600',
    marginLeft: 4,
  },
  subcategoryContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  subcategoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  subcategoryTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  removeButton: {
    padding: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  cancelButton: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    marginRight: 8,
    borderWidth: 1,
    borderColor: 'rgba(100, 100, 100, 0.2)',
    alignItems: 'center',
  },
  cancelButtonText: {
    fontWeight: '600',
    color: '#64748b',
  },
  createButton: {
    flex: 1,
    padding: 15,
    borderRadius: 8,
    marginLeft: 8,
    backgroundColor: '#f97316',
    alignItems: 'center',
  },
  createButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
}); 