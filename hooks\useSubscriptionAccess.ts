import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/lib/supabase';
import { useCallback, useEffect, useState } from 'react';

export interface UserSubscription {
  id: string;
  status: 'trial' | 'active' | 'cancelled' | 'expired';
  plan_id: string;
  current_period_end: string;
  trial_end?: string;
  auto_renew: boolean;
}

export const useSubscriptionAccess = () => {
  const { user } = useAuth();
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [loading, setLoading] = useState(true);

  // Function to check and automatically expire subscriptions
  const checkAndExpireSubscription = useCallback(async (sub: UserSubscription) => {
    const now = new Date();
    const currentPeriodEnd = new Date(sub.current_period_end);
    const trialEnd = sub.trial_end ? new Date(sub.trial_end) : null;
    
    let shouldExpire = false;
    let expiryReason = '';

    // Check if trial has expired
    if (sub.status === 'trial' && trialEnd && now >= trialEnd) {
      shouldExpire = true;
      expiryReason = 'trial_expired';
    }
    
    // Check if subscription period has ended
    if ((sub.status === 'active' || sub.status === 'trial') && now >= currentPeriodEnd) {
      shouldExpire = true;
      expiryReason = 'subscription_expired';
    }

    if (shouldExpire && sub.status !== 'expired') {
      console.log(`Expiring subscription: ${expiryReason}`);
      
      try {
        // Update subscription status to expired
        const { error: updateError } = await supabase
          .from('user_subscriptions')
          .update({
            status: 'expired',
            auto_renew: false, // Cancel auto-renewal
            updated_at: new Date().toISOString()
          })
          .eq('id', sub.id);

        if (updateError) {
          console.error('Error expiring subscription:', updateError);
          return sub; // Return original if update failed
        }

        // Return updated subscription
        return {
          ...sub,
          status: 'expired' as const,
          auto_renew: false
        };
      } catch (error) {
        console.error('Error expiring subscription:', error);
        return sub; // Return original if update failed
      }
    }

    return sub; // Return original if no expiry needed
  }, []);

  const fetchAndCheckSubscription = useCallback(async () => {
    if (!user) {
      setSubscription(null);
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      
      // Fetch subscription
      const { data: subData, error: subError } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (subError && subError.code !== 'PGRST116') {
        console.error('Error fetching subscription:', subError);
        setSubscription(null);
        return;
      }
      
      if (subData) {
        // Check and potentially expire the subscription
        const checkedSubscription = await checkAndExpireSubscription(subData);
        setSubscription(checkedSubscription);
      } else {
        setSubscription(null);
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
      setSubscription(null);
    } finally {
      setLoading(false);
    }
  }, [user, checkAndExpireSubscription]);

  // Check if user has access to features
  const isSubscribed = useCallback(() => {
    if (!subscription) return false;
    
    const status = subscription.status?.toLowerCase();
    return status === 'active' || status === 'trial';
  }, [subscription]);

  // Get subscription status
  const getSubscriptionStatus = useCallback(() => {
    return subscription?.status || null;
  }, [subscription]);

  // Check if subscription is expired
  const isExpired = useCallback(() => {
    return subscription?.status === 'expired';
  }, [subscription]);

  // Check if subscription is in trial
  const isTrial = useCallback(() => {
    return subscription?.status === 'trial';
  }, [subscription]);

  // Get days remaining for trial or subscription
  const getDaysRemaining = useCallback(() => {
    if (!subscription) return 0;
    
    const now = new Date();
    let endDate: Date;
    
    if (subscription.status === 'trial' && subscription.trial_end) {
      endDate = new Date(subscription.trial_end);
    } else {
      endDate = new Date(subscription.current_period_end);
    }
    
    const diffTime = endDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }, [subscription]);

  useEffect(() => {
    fetchAndCheckSubscription();
  }, [user]);

  // Set up real-time subscription listener for immediate updates
  useEffect(() => {
    if (!user) return;

    const channelName = `subscription-access-${user.id}-${Date.now()}`;

    const subscription = supabase
      .channel(channelName)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'user_subscriptions',
        filter: `user_id=eq.${user.id}`
      }, (payload) => {
        console.log('Real-time subscription update detected:', payload);
        // Immediately refresh subscription status
        fetchAndCheckSubscription();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, [user, fetchAndCheckSubscription]);

  // Auto-refresh subscription every minute to check for expiry
  useEffect(() => {
    const interval = setInterval(() => {
      if (subscription && (subscription.status === 'trial' || subscription.status === 'active')) {
        fetchAndCheckSubscription();
      }
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [subscription, fetchAndCheckSubscription]);

  return {
    subscription,
    loading,
    isSubscribed,
    isExpired,
    isTrial,
    getSubscriptionStatus,
    getDaysRemaining,
    refreshSubscription: fetchAndCheckSubscription
  };
}; 