import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/lib/supabase';
import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';
import * as Linking from 'expo-linking';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    Image,
    KeyboardTypeOptions,
    ScrollView,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    useColorScheme,
    View
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// For testing purposes - remove in production
const supabaseUrl = 'https://vsnhscndlifvaptwdfsw.supabase.co';

// Define interfaces for better type safety
interface ProfileFormState {
  full_name: string;
  email: string;
  city: string;
  phone_number: string;
}

interface InputFieldProps {
  label: string;
  value: string;
  onChangeText: (text: string) => void;
  keyboardType?: KeyboardTypeOptions;
  editable?: boolean;
}

interface LegalLinkProps {
  title: string;
  onPress: () => void;
}

// Primary color constants
const PRIMARY_COLOR = '#FF6B00'; // Orange
const SECONDARY_COLOR = '#FF8C3D'; // Light orange
const DANGER_COLOR = '#FF3B30'; // Red for delete button

export default function ProfileScreen() {
  const insets = useSafeAreaInsets();
  const { profile, user, signOut, refreshProfile, clearProfile } = useAuth();
  const colorScheme = useColorScheme();
  const [uploading, setUploading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [profileForm, setProfileForm] = useState<ProfileFormState>({
    full_name: profile?.full_name || '',
    email: profile?.email || '',
    city: profile?.city || '',
    phone_number: profile?.phone_number || '',
  });

  useEffect(() => {
    if (profile) {
      setProfileForm({
        full_name: profile.full_name || '',
        email: profile.email || '',
        city: profile.city || '',
        phone_number: profile.phone_number || '',
      });
    }
  }, [profile]);

  const pickImage = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: "images",
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8, // Reduced quality for smaller file size
    });

    if (!result.canceled && result.assets[0]?.uri) {
      await uploadProfileImage(result.assets[0].uri);
    }
  };

  const uploadProfileImage = async (uri: string) => {
    try {
      setUploading(true);
      console.log('Starting profile image upload with URI:', uri);
      
      // Check if the image URI is valid
      let imageUri = uri;
      if (!imageUri.startsWith('file://') && !imageUri.startsWith('content://')) {
        console.log('Adding file:// prefix to URI');
        imageUri = `file://${imageUri}`;
      }
      
      if (!user?.id) {
        throw new Error('User ID not found');
      }
      
      // Define a simple file name with timestamp to avoid conflicts
      const timestamp = new Date().getTime();
      const filePath = `${user.id}/profile-${timestamp}.jpg`;
      
      console.log('Will upload to path:', filePath);
      
      // Get file info to verify it exists
      const fileInfo = await FileSystem.getInfoAsync(imageUri);
      if (!fileInfo.exists) {
        throw new Error('File does not exist at ' + imageUri);
      }
      
      console.log('File info:', fileInfo);
      
      try {
        // Instead of trying to create blobs with fetch API,
        // use Expo FileSystem to handle the upload
        console.log('Starting upload with Expo FileSystem');
        
        // Get user session for auth
        const {
          data: { session },
        } = await supabase.auth.getSession();
        
        if (!session) {
          throw new Error('No authenticated session found');
        }
        
        console.log('Got authenticated session, token length:', session.access_token.length);
        
        // First, create a FormData-like object that works with Expo FileSystem
        // Upload using Expo FileSystem and direct REST API with proper auth token
        const formData = new FormData();
        formData.append('file', {
          uri: imageUri,
          name: `profile-${timestamp}.jpg`,
          type: 'image/jpeg'
        } as any);
        
        // Use standard fetch with proper auth token
        console.log('Uploading with fetch and FormData...');
        const response = await fetch(
          `${supabaseUrl}/storage/v1/object/user-profiles/${filePath}`,
          {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${session.access_token}`,
              'x-upsert': 'true',
            },
            body: formData
          }
        );
        
        const responseText = await response.text();
        console.log('Upload response status:', response.status, 'body:', responseText);
        
        if (!response.ok) {
          throw new Error(`Upload failed with status ${response.status}: ${responseText}`);
        }
        
        console.log('Upload successful');
      } catch (uploadError) {
        console.error('Upload error:', uploadError);
        throw uploadError;
      }
      
      // Get public URL for the uploaded image
      const { data: urlData } = supabase.storage
        .from('user-profiles')
        .getPublicUrl(filePath);
      
      if (!urlData?.publicUrl) {
        throw new Error('Failed to get public URL');
      }
      
      // Add a cache-busting parameter to ensure fresh image is loaded
      const publicUrl = `${urlData.publicUrl}?t=${timestamp}`;
      console.log('Profile image uploaded successfully:', publicUrl);
      
      // Update profile in database with the new image URL
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ profile_image_url: publicUrl })
        .eq('user_id', user.id);
      
      if (updateError) {
        console.error('Error updating profile with new image URL:', updateError);
        throw updateError;
      }
      
      // Refresh profile to show updated image
      await refreshProfile();
      
      Alert.alert('Success', 'Profile image updated successfully');
    } catch (error) {
      console.error('Error uploading image:', error);
      Alert.alert('Upload failed', 'Error uploading profile image. Please check your connection and try again.');
    } finally {
      setUploading(false);
    }
  };

  const updateProfile = async () => {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: profileForm.full_name,
          email: profileForm.email,
          city: profileForm.city,
          phone_number: profileForm.phone_number,
        })
        .eq('user_id', user?.id);
      
      if (error) throw error;
      
      await refreshProfile();
      Alert.alert('Success', 'Profile updated successfully');
    } catch (error) {
      console.error('Error updating profile:', error);
      Alert.alert('Update failed', 'Error updating profile details');
    }
  };

  const handleDeleteAccount = async () => {
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone.',
      [
        { 
          text: 'Cancel', 
          style: 'cancel' 
        },
        {
          text: 'Delete', 
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('Deleting account...');
              
              // First, reset the local state to empty
              clearProfile();
              setProfileForm({
                full_name: '',
                email: '',
                city: '',
                phone_number: '',
              });
              
              // Then delete the profile data from database
              const { error: profileError } = await supabase
                .from('profiles')
                .delete()
                .eq('user_id', user?.id);
              
              if (profileError) throw profileError;
              
              // Delete images after profile is deleted and UI is cleared
              if (user?.id) {
                try {
                  // Try to delete from storage buckets
                  console.log('Deleting images from user-profiles bucket...');
                  const { data: storageData, error: listError } = await supabase.storage
                    .from('user-profiles')
                    .list(user.id);
                  
                  if (listError) {
                    console.warn('Error listing profile images:', listError);
                  } else if (storageData && storageData.length > 0) {
                    // Get file paths to delete
                    const filesToDelete = storageData.map(file => `${user.id}/${file.name}`);
                    
                    // Delete all files
                    const { error: deleteError } = await supabase.storage
                      .from('user-profiles')
                      .remove(filesToDelete);
                    
                    if (deleteError) {
                      console.warn('Error removing profile images:', deleteError);
                    }
                  }
                  
                  // Also try to delete from the old bucket for backward compatibility
                  console.log('Attempting to clean up from old profile-images bucket...');
                  const filePath = `profiles/${user.id}/profile-image`;
                  await supabase.storage
                    .from('profile-images')
                    .remove([filePath])
                    .then(({ error }) => {
                      if (error) console.log('No images in old bucket or error:', error);
                      else console.log('Successfully removed old profile image');
                    });
                } catch (storageError) {
                  console.warn('Error during storage cleanup:', storageError);
                }
              }
              
              // Force navigation to auth screen before sign out
              // This helps avoid race conditions where the app might try to
              // access profile data during navigation transitions
              router.replace('/auth/phone');
              
              // Finally sign out the user
              setTimeout(async () => {
                await signOut();
                
                // Inform the user that their profile data has been deleted
                Alert.alert(
                  'Account Deactivated', 
                  'Your profile data has been deleted. Please contact support if you want your authentication data permanently removed.'
                );
              }, 500); // Small delay to ensure navigation completes first
              
            } catch (error) {
              console.error('Error deleting account:', error);
              Alert.alert('Error', 'Failed to delete account. Please try again.');
            }
          }
        }
      ]
    );
  };

  const ProfileImage = () => (
    <View style={styles.profileHeaderContainer}>
      <TouchableOpacity 
        style={styles.imageContainer} 
        onPress={pickImage} 
        disabled={uploading}
      >
        {profile?.profile_image_url ? (
          <View>
            <Image
              source={{ uri: profile.profile_image_url }}
              style={styles.profileImage}
            />
            {uploading && (
              <View style={styles.uploadingOverlay}>
                <ThemedText style={styles.uploadingText}>Uploading...</ThemedText>
              </View>
            )}
          </View>
        ) : (
          <View style={styles.placeholderImage}>
            {uploading ? (
              <ThemedText style={styles.uploadingText}>Uploading...</ThemedText>
            ) : (
              <ThemedText style={styles.placeholderText}>
                {profile?.full_name?.charAt(0) || user?.email?.charAt(0) || '?'}
              </ThemedText>
            )}
          </View>
        )}
        <View style={[styles.changeImageButton, uploading && styles.disabledButton]}>
          <ThemedText style={styles.changeImageText}>
            {uploading ? 'Uploading...' : 'Change'}
          </ThemedText>
        </View>
      </TouchableOpacity>
      <ThemedText style={styles.welcomeText}>
        Welcome, {profile?.full_name || 'User'}
      </ThemedText>
    </View>
  );

  const EditProfileTab = () => (
    <ScrollView 
      style={styles.tabContent} 
      contentContainerStyle={styles.tabContentContainer}
      showsVerticalScrollIndicator={false}
    >
      <InputField 
        label="Full Name"
        value={profileForm.full_name}
        onChangeText={(text: string) => setProfileForm(prev => ({ ...prev, full_name: text }))}
      />
      <InputField 
        label="Email"
        value={profileForm.email}
        onChangeText={(text: string) => setProfileForm(prev => ({ ...prev, email: text }))}
        keyboardType="email-address"
      />
      <InputField 
        label="City"
        value={profileForm.city}
        onChangeText={(text: string) => setProfileForm(prev => ({ ...prev, city: text }))}
      />
      <InputField 
        label="Phone Number"
        value={profileForm.phone_number}
        onChangeText={(text: string) => setProfileForm(prev => ({ ...prev, phone_number: text }))}
        keyboardType="phone-pad"
        editable={false}
      />
      <TouchableOpacity style={styles.updateButton} onPress={updateProfile}>
        <ThemedText style={styles.buttonText}>Update Profile</ThemedText>
      </TouchableOpacity>
    </ScrollView>
  );

  const AccountTab = () => (
    <ScrollView 
      style={styles.tabContent} 
      contentContainerStyle={styles.tabContentContainer} 
      showsVerticalScrollIndicator={false}
    >
      <TouchableOpacity 
        style={styles.signOutButton} 
        onPress={() => {
          console.log('Sign out button pressed');
          signOut();
        }}
      >
        <ThemedText style={styles.buttonText}>Sign Out</ThemedText>
      </TouchableOpacity>
      <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteAccount}>
        <ThemedText style={styles.buttonText}>Delete Account</ThemedText>
      </TouchableOpacity>
    </ScrollView>
  );

  const LegalTab = () => (
    <ScrollView 
      style={styles.tabContent} 
      contentContainerStyle={styles.tabContentContainer}
      showsVerticalScrollIndicator={false}
    >
      <LegalLink title="Privacy Policy" onPress={() => Linking.openURL('https://infratask.com/privacy')} />
      <LegalLink title="Legal" onPress={() => Linking.openURL('https://infratask.com/legal')} />
      <LegalLink title="Terms and Conditions" onPress={() => Linking.openURL('https://infratask.com/terms')} />
      <LegalLink title="Support" onPress={() => Linking.openURL('https://infratask.com/support')} />
    </ScrollView>
  );

  // Helper components
  const InputField = ({ label, value, onChangeText, keyboardType = 'default', editable = true }: InputFieldProps) => (
    <View style={styles.inputContainer}>
      <ThemedText style={styles.inputLabel}>{label}</ThemedText>
      <TextInput
        style={[
          styles.input,
          !editable && styles.disabledInput,
          {
            backgroundColor: colorScheme === 'dark' ? '#374151' : '#FAFAFA',
            color: colorScheme === 'dark' ? '#ffffff' : '#1f2937',
            borderColor: colorScheme === 'dark' ? '#4b5563' : '#E0E0E0',
          }
        ]}
        value={value}
        onChangeText={onChangeText}
        keyboardType={keyboardType}
        editable={editable}
        placeholderTextColor={colorScheme === 'dark' ? '#9ca3af' : '#999'}
      />
    </View>
  );

  const LegalLink = ({ title, onPress }: LegalLinkProps) => (
    <TouchableOpacity style={styles.legalLink} onPress={onPress}>
      <ThemedText style={styles.legalLinkText}>{title}</ThemedText>
    </TouchableOpacity>
  );

  // Custom tab bar
  const CustomTabBar = () => (
    <View style={styles.tabBarContainer}>
      <TouchableOpacity
        style={[styles.tabButton, activeTab === 0 && styles.tabButtonActive]}
        onPress={() => setActiveTab(0)}
      >
        <ThemedText style={[styles.tabButtonText, activeTab === 0 && styles.tabButtonTextActive]}>
          Edit Profile
        </ThemedText>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tabButton, activeTab === 1 && styles.tabButtonActive]}
        onPress={() => setActiveTab(1)}
      >
        <ThemedText style={[styles.tabButtonText, activeTab === 1 && styles.tabButtonTextActive]}>
          Account
        </ThemedText>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tabButton, activeTab === 2 && styles.tabButtonActive]}
        onPress={() => setActiveTab(2)}
      >
        <ThemedText style={[styles.tabButtonText, activeTab === 2 && styles.tabButtonTextActive]}>
          Legal
        </ThemedText>
      </TouchableOpacity>
    </View>
  );

  // Render the active tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 0:
        return <EditProfileTab />;
      case 1:
        return <AccountTab />;
      case 2:
        return <LegalTab />;
      default:
        return <EditProfileTab />;
    }
  };

  return (
    <ThemedView style={[styles.container, { paddingTop: insets.top + 20 }]}>
      <ProfileImage />
      <CustomTabBar />
      {renderTabContent()}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#FFFFFF',
  },
  profileHeaderContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  welcomeText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 12,
    color: '#333',
  },
  imageContainer: {
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 3,
    borderColor: PRIMARY_COLOR,
  },
  placeholderImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: PRIMARY_COLOR,
  },
  placeholderText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#999',
  },
  changeImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: PRIMARY_COLOR,
    borderRadius: 15,
    padding: 5,
    paddingHorizontal: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  changeImageText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  tabBarContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    borderRadius: 25,
    backgroundColor: '#F6F6F6',
    padding: 5,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 20,
  },
  tabButtonActive: {
    backgroundColor: `${PRIMARY_COLOR}20`, // 20% opacity
  },
  tabButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#777',
  },
  tabButtonTextActive: {
    color: PRIMARY_COLOR,
    fontWeight: '600',
  },
  tabContent: {
    flex: 1,
  },
  tabContentContainer: {
    padding: 16,
    paddingBottom: 40, // Extra padding at the bottom for better spacing
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    marginBottom: 8,
    fontWeight: '600',
    color: '#555',
    fontSize: 15,
  },
  input: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    padding: 14,
    fontSize: 16,
    backgroundColor: '#FAFAFA',
  },
  disabledInput: {
    backgroundColor: '#F0F0F0',
    color: '#999',
  },
  updateButton: {
    backgroundColor: PRIMARY_COLOR,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20, // Added bottom margin
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  signOutButton: {
    backgroundColor: PRIMARY_COLOR,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  deleteButton: {
    backgroundColor: DANGER_COLOR,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  legalLink: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EBEBEB',
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: '#FAFAFA',
  },
  legalLinkText: {
    fontSize: 16,
    color: '#444',
  },
  uploadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadingText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  disabledButton: {
    backgroundColor: '#E0E0E0',
  },
});