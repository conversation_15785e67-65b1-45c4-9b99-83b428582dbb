-- Create subscription system tables
-- Migration: Create subscription tables for the SaaS pricing model

-- Subscription Plans Table
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    billing_cycle VARCHAR(20) DEFAULT 'monthly',
    max_users INT NULL, -- NULL for unlimited
    features JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- User Subscriptions Table
CREATE TABLE user_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    plan_id VARCHAR(50) NOT NULL, -- Will reference plan names like 'pay-as-you-go', 'premium'
    status VARCHAR(20) DEFAULT 'active', -- active, cancelled, expired, trial
    current_period_start TIMESTAMP,
    current_period_end TIMESTAMP,
    trial_end TIMESTAMP,
    additional_users INT DEFAULT 0,
    payment_method_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Subscription Users (for team management)
CREATE TABLE subscription_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subscription_id UUID REFERENCES user_subscriptions(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    role VARCHAR(50) DEFAULT 'member', -- owner, admin, member
    invited_by UUID REFERENCES auth.users(id),
    invited_at TIMESTAMP,
    joined_at TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active', -- active, invited, removed
    created_at TIMESTAMP DEFAULT NOW()
);

-- Insert default subscription plans
INSERT INTO subscription_plans (name, price, billing_cycle, max_users, features, is_active) VALUES
(
    'Pay As You Go',
    199.00,
    'monthly',
    1,
    '["1 user included", "All core construction management features", "Project management tools", "Basic reporting and analytics", "Email support", "Mobile & Web access", "Additional users: ₹199/user/month"]'::jsonb,
    true
),
(
    'Premium',
    999.00,
    'monthly',
    NULL,
    '["Unlimited users", "All core construction management features", "Advanced reporting and analytics", "Priority customer support", "Advanced integrations", "Custom workflows", "Data export & backup", "Mobile & Web access"]'::jsonb,
    true
);

-- Enable RLS (Row Level Security)
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_users ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- Subscription plans are readable by everyone (public pricing)
CREATE POLICY "Subscription plans are viewable by everyone" ON subscription_plans
    FOR SELECT USING (true);

-- Users can only view and manage their own subscriptions
CREATE POLICY "Users can view their own subscriptions" ON user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own subscriptions" ON user_subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own subscriptions" ON user_subscriptions
    FOR UPDATE USING (auth.uid() = user_id);

-- Subscription users policies
CREATE POLICY "Users can view subscription users for their subscriptions" ON subscription_users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_subscriptions 
            WHERE id = subscription_users.subscription_id 
            AND user_id = auth.uid()
        )
        OR auth.uid() = subscription_users.user_id
    );

CREATE POLICY "Subscription owners can manage subscription users" ON subscription_users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM user_subscriptions 
            WHERE id = subscription_users.subscription_id 
            AND user_id = auth.uid()
        )
    );

-- Create indexes for better performance
CREATE INDEX idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX idx_subscription_users_subscription_id ON subscription_users(subscription_id);
CREATE INDEX idx_subscription_users_user_id ON subscription_users(user_id);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_subscription_plans_updated_at BEFORE UPDATE ON subscription_plans
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_subscriptions_updated_at BEFORE UPDATE ON user_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 