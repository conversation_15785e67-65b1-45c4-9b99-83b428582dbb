import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';
import 'react-native-url-polyfill/auto';

const supabaseUrl = 'https://vsnhscndlifvaptwdfsw.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.0MxRajYjYvdoZePb15nv32A7a6s5rX-pn7DRKzSTAAI';

// Safe storage wrapper that works in SSR environments
const createSafeStorage = () => {
  // Check if we're in a server environment
  const isServer = typeof window === 'undefined';
  
  // If on server, return a no-op storage
  if (isServer) {
    return {
      getItem: async (key: string) => null,
      setItem: async (key: string, value: string) => {},
      removeItem: async (key: string) => {},
    };
  }
  
  // Use our polyfilled AsyncStorage for client
  return {
    getItem: async (key: string) => {
      try {
        return await AsyncStorage.getItem(key);
      } catch (error) {
        console.warn('Storage getItem error:', error);
        return null;
      }
    },
    setItem: async (key: string, value: string) => {
      try {
        await AsyncStorage.setItem(key, value);
      } catch (error) {
        console.warn('Storage setItem error:', error);
      }
    },
    removeItem: async (key: string) => {
      try {
        await AsyncStorage.removeItem(key);
      } catch (error) {
        console.warn('Storage removeItem error:', error);
      }
    },
  };
};

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: createSafeStorage(),
    autoRefreshToken: true,
    persistSession: typeof window !== 'undefined', // Only persist sessions on client
    detectSessionInUrl: false,
    storageKey: 'infratask-auth-storage',
    flowType: 'pkce',
  },
}); 