import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { supabase } from '@/lib/supabase';
import { Feather, MaterialIcons } from '@expo/vector-icons';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Image,
  Keyboard,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  useColorScheme,
  View,
} from 'react-native';
import DropDownPicker from 'react-native-dropdown-picker';

interface UserProfile {
  id: string;
  user_id: string;
  full_name: string;
  profile_image_url: string | null;
  phone_number: string | null;
  email: string | null;
}

export default function AddTeamMemberScreen() {
  const params = useLocalSearchParams();
  const siteId = typeof params.id === 'string' ? params.id : Array.isArray(params.id) ? params.id[0] : '';
  const colorScheme = useColorScheme();
  
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<UserProfile[]>([]);
  const [searching, setSearching] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  
  const [phoneNumber, setPhoneNumber] = useState('');
  const [inviteName, setInviteName] = useState('');
  const [sendingInvite, setSendingInvite] = useState(false);
  const [inviteSent, setInviteSent] = useState(false);
  
  // Role dropdown
  const [openRoleDropdown, setOpenRoleDropdown] = useState(false);
  const [selectedRole, setSelectedRole] = useState('Member');
  const [roles] = useState([
    { label: 'Admin', value: 'Admin' },
    { label: 'Member', value: 'Member' },
  ]);
  
  // Category dropdown
  const [openCategoryDropdown, setOpenCategoryDropdown] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [categories] = useState([
    { label: 'Worker', value: 'Worker' },
    { label: 'Site Owner', value: 'Site Owner' },
    { label: 'Manager', value: 'Manager' },
    { label: 'Site Engineer', value: 'Site Engineer' },
    { label: 'Project Manager', value: 'Project Manager' },
  ]);
  
  // Log categories for debugging
  useEffect(() => {
    console.log('Categories available:', categories);
  }, [categories]);
  
  // Log when dropdown is opened/closed
  useEffect(() => {
    console.log('Category dropdown open state:', openCategoryDropdown);
  }, [openCategoryDropdown]);
  
  // Search for users
  const searchUsers = async (query: string) => {
    if (!query || query.trim() === '') {
      setSearchResults([]);
      setSearching(false);
      return;
    }
    
    try {
      setSearching(true);
      console.log('Searching users with query:', query);
      
      // Use the simple_search_profiles function
      const { data, error } = await supabase.rpc('simple_search_profiles', { 
        search_term: query 
      });
      
      if (error) {
        console.error('Error searching users with RPC:', error);
        
        // Fallback to direct query with no row-level security
        console.log('Falling back to direct query...');
        try {
          // Get current user's token for authentication
          const { data: authData } = await supabase.auth.getSession();
          if (!authData.session) {
            console.error('No authenticated session');
            setSearching(false);
            return;
          }
          
          // Direct query using authenticated fetch
          const response = await fetch(
            `https://vsnhscndlifvaptwdfsw.supabase.co/rest/v1/profiles?select=id,user_id,full_name,email,phone_number,profile_image_url&or=(full_name.ilike.%25${encodeURIComponent(query)}%25,phone_number.ilike.%25${encodeURIComponent(query)}%25,email.ilike.%25${encodeURIComponent(query)}%25)&limit=50`,
            {
              method: 'GET',
              headers: {
                'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.0MxRajYjYvdoZePb15nv32A7a6s5rX-pn7DRKzSTAAI',
                'Authorization': `Bearer ${authData.session.access_token}`,
                'Content-Type': 'application/json'
              }
            }
          );
          
          if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
          }
          
          const directData = await response.json();
          console.log('Direct API search results:', directData);
          setSearchResults(directData || []);
        } catch (fetchError) {
          console.error('Error with direct fetch:', fetchError);
          Alert.alert('Error', 'Failed to search users');
        }
      } else {
        console.log('RPC search results:', data);
        setSearchResults(data || []);
      }
    } catch (error) {
      console.error('Error in search:', error);
      Alert.alert('Error', 'An error occurred while searching');
    } finally {
      setSearching(false);
    }
  };
  
  // Handle search input change with debounce
  useEffect(() => {
    const debounceTimeout = setTimeout(() => {
      if (searchQuery) {
        searchUsers(searchQuery);
      }
    }, 500);
    
    return () => clearTimeout(debounceTimeout);
  }, [searchQuery]);
  
  // Select a user from search results
  const handleSelectUser = (user: UserProfile) => {
    setSelectedUser(user);
    setSearchQuery(''); // Clear search
    setSearchResults([]); // Clear results
    Keyboard.dismiss();
  };
  
  // Add selected user as team member
  const handleAddMember = async () => {
    if (!selectedUser) {
      Alert.alert('Error', 'Please select a user');
      return;
    }
    
    if (!selectedRole) {
      Alert.alert('Error', 'Please select a role');
      return;
    }
    
    try {
      setLoading(true);
      
      // Check if user is already a member of this site
      const { data: existingMember, error: checkError } = await supabase
        .from('site_members')
        .select('id')
        .eq('site_id', siteId)
        .eq('user_id', selectedUser.user_id)
        .maybeSingle();
      
      if (checkError) {
        console.error('Error checking existing member:', checkError);
        Alert.alert('Error', 'Failed to check if user is already a member');
        return;
      }
      
      if (existingMember) {
        Alert.alert('Already a Member', 'This user is already a member of this site');
        return;
      }
      
      // Add user as a team member
      const { error: addError } = await supabase
        .from('site_members')
        .insert({
          site_id: siteId,
          user_id: selectedUser.user_id,
          role: selectedRole,
          category: selectedCategory || null,
        });
      
      if (addError) {
        console.error('Error adding team member:', addError);
        Alert.alert('Error', 'Failed to add team member');
        return;
      }
      
      Alert.alert(
        'Success',
        'Team member added successfully',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    } catch (error) {
      console.error('Error in add member:', error);
      Alert.alert('Error', 'An error occurred while adding the member');
    } finally {
      setLoading(false);
    }
  };
  
  // Send invitation to join the app
  const handleSendInvite = async () => {
    if (!phoneNumber || phoneNumber.trim() === '') {
      Alert.alert('Error', 'Please enter a phone number');
      return;
    }
    
    try {
      setSendingInvite(true);
      
      // Here you would integrate with your SMS or WhatsApp service
      // For demonstration, we'll just simulate a successful invitation
      console.log('Sending invite to:', phoneNumber, 'with name:', inviteName);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Show success
      setInviteSent(true);
      
      // Reset after a few seconds
      setTimeout(() => {
        setInviteSent(false);
        setPhoneNumber('');
        setInviteName('');
      }, 3000);
      
    } catch (error) {
      console.error('Error sending invite:', error);
      Alert.alert('Error', 'Failed to send invitation');
    } finally {
      setSendingInvite(false);
    }
  };
  
  // Render search result item
  const renderUserItem = ({ item }: { item: UserProfile }) => (
    <TouchableOpacity
      style={styles.userItem}
      onPress={() => handleSelectUser(item)}
    >
      {item.profile_image_url ? (
        <Image 
          source={{ uri: item.profile_image_url }} 
          style={styles.userImage} 
        />
      ) : (
        <View style={[styles.userImage, styles.userImagePlaceholder]}>
          <Feather name="user" size={18} color="#94a3b8" />
        </View>
      )}
      
      <View style={styles.userInfo}>
        <ThemedText style={styles.userName}>{item.full_name}</ThemedText>
        {item.phone_number && (
          <ThemedText style={styles.userPhone}>{item.phone_number}</ThemedText>
        )}
      </View>
    </TouchableOpacity>
  );
  
  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <ThemedView style={styles.container}>
        <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
        
        <Stack.Screen options={{ title: 'Add Team Member' }} />
        
        <View style={styles.content}>
          {/* Search section */}
          <View style={styles.searchSection}>
            <View style={styles.searchContainer}>
              <Feather 
                name="search" 
                size={18} 
                color={colorScheme === 'dark' ? '#94a3b8' : '#64748b'} 
                style={styles.searchIcon} 
              />
              <TextInput
                style={[
                  styles.searchInput,
                  { color: colorScheme === 'dark' ? '#e2e8f0' : '#0f172a' }
                ]}
                placeholder="Search by name or phone number"
                placeholderTextColor={colorScheme === 'dark' ? '#64748b' : '#94a3b8'}
                value={searchQuery}
                onChangeText={setSearchQuery}
                returnKeyType="search"
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity 
                  onPress={() => setSearchQuery('')}
                  style={styles.clearButton}
                >
                  <Feather 
                    name="x" 
                    size={16} 
                    color={colorScheme === 'dark' ? '#94a3b8' : '#64748b'} 
                  />
                </TouchableOpacity>
              )}
            </View>
          </View>
          
          {/* Search results */}
          {searching ? (
            <View style={styles.centeredContent}>
              <ActivityIndicator size="small" color="#f97316" />
              <ThemedText style={styles.searchingText}>Searching users...</ThemedText>
            </View>
          ) : searchResults.length > 0 ? (
            <FlatList
              data={searchResults}
              renderItem={renderUserItem}
              keyExtractor={(item) => item.id}
              style={styles.resultsList}
              contentContainerStyle={styles.resultsContent}
            />
          ) : searchQuery && !searching ? (
            <View style={styles.noResultsContainer}>
              <MaterialIcons name="person-search" size={48} color="#94a3b8" />
              <ThemedText style={styles.noResultsTitle}>No user found</ThemedText>
              <ThemedText style={styles.noResultsText}>
                Invite them to join the app
              </ThemedText>
              
              <View style={styles.inviteContainer}>
                <ThemedText style={styles.inviteLabel}>Phone Number</ThemedText>
                <TextInput
                  style={[
                    styles.inviteInput,
                    { color: colorScheme === 'dark' ? '#e2e8f0' : '#0f172a' }
                  ]}
                  value={phoneNumber}
                  onChangeText={setPhoneNumber}
                  placeholder="Enter phone number"
                  placeholderTextColor={colorScheme === 'dark' ? '#64748b' : '#94a3b8'}
                  keyboardType="phone-pad"
                />
                
                <ThemedText style={styles.inviteLabel}>Name (Optional)</ThemedText>
                <TextInput
                  style={[
                    styles.inviteInput,
                    { color: colorScheme === 'dark' ? '#e2e8f0' : '#0f172a' }
                  ]}
                  value={inviteName}
                  onChangeText={setInviteName}
                  placeholder="Enter name for personalized invite"
                  placeholderTextColor={colorScheme === 'dark' ? '#64748b' : '#94a3b8'}
                />
                
                <TouchableOpacity
                  style={styles.sendInviteButton}
                  onPress={handleSendInvite}
                  disabled={sendingInvite || inviteSent}
                >
                  {sendingInvite ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : inviteSent ? (
                    <>
                      <Feather name="check" size={16} color="#fff" />
                      <ThemedText style={styles.buttonText}>Invitation Sent</ThemedText>
                    </>
                  ) : (
                    <>
                      <Feather name="send" size={16} color="#fff" />
                      <ThemedText style={styles.buttonText}>Send Invite</ThemedText>
                    </>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          ) : null}
          
          {/* Selected user section */}
          {selectedUser && (
            <View style={styles.selectedUserSection}>
              <View style={styles.selectedUserHeader}>
                <ThemedText style={styles.sectionTitle}>Selected User</ThemedText>
                <TouchableOpacity
                  onPress={() => setSelectedUser(null)}
                  style={styles.deselectButton}
                >
                  <ThemedText style={styles.deselectText}>Change</ThemedText>
                </TouchableOpacity>
              </View>
              
              <View style={styles.selectedUserCard}>
                {selectedUser.profile_image_url ? (
                  <Image 
                    source={{ uri: selectedUser.profile_image_url }} 
                    style={styles.selectedUserImage} 
                  />
                ) : (
                  <View style={[styles.selectedUserImage, styles.userImagePlaceholder]}>
                    <Feather name="user" size={24} color="#94a3b8" />
                  </View>
                )}
                
                <View style={styles.selectedUserInfo}>
                  <ThemedText style={styles.selectedUserName}>
                    {selectedUser.full_name}
                  </ThemedText>
                  {selectedUser.phone_number && (
                    <ThemedText style={styles.selectedUserPhone}>
                      {selectedUser.phone_number}
                    </ThemedText>
                  )}
                </View>
              </View>
              
              <View style={styles.dropdownContainer}>
                <ThemedText style={styles.dropdownLabel}>Role</ThemedText>
                <DropDownPicker
                  open={openRoleDropdown}
                  value={selectedRole}
                  items={roles}
                  setOpen={setOpenRoleDropdown}
                  setValue={setSelectedRole}
                  style={[
                    styles.dropdown,
                    { backgroundColor: colorScheme === 'dark' ? '#1e293b' : '#f8fafc' }
                  ]}
                  textStyle={{ 
                    color: colorScheme === 'dark' ? '#e2e8f0' : '#0f172a',
                    fontSize: 16
                  }}
                  dropDownContainerStyle={[
                    styles.dropdownContainer,
                    { backgroundColor: colorScheme === 'dark' ? '#1e293b' : '#f8fafc' }
                  ]}
                  placeholderStyle={{ color: '#94a3b8' }}
                  zIndex={3000}
                />
              </View>
              
              <View style={[styles.dropdownContainer, { zIndex: 1000 }]}>
                <ThemedText style={styles.dropdownLabel}>Category</ThemedText>
                <DropDownPicker
                  open={openCategoryDropdown}
                  value={selectedCategory}
                  items={categories}
                  setOpen={setOpenCategoryDropdown}
                  setValue={setSelectedCategory}
                  style={[
                    styles.dropdown,
                    { backgroundColor: colorScheme === 'dark' ? '#1e293b' : '#f8fafc' }
                  ]}
                  textStyle={{ 
                    color: colorScheme === 'dark' ? '#e2e8f0' : '#0f172a',
                    fontSize: 16
                  }}
                  dropDownContainerStyle={[
                    styles.dropdownContainer,
                    { backgroundColor: colorScheme === 'dark' ? '#1e293b' : '#f8fafc' }
                  ]}
                  placeholder="Select a category"
                  placeholderStyle={{ color: '#94a3b8' }}
                  zIndex={2000}
                  listMode="SCROLLVIEW"
                  scrollViewProps={{
                    nestedScrollEnabled: true,
                  }}
                />
              </View>
              
              <View style={styles.buttonsContainer}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => router.back()}
                >
                  <ThemedText style={styles.cancelButtonText}>Cancel</ThemedText>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={styles.addButton}
                  onPress={handleAddMember}
                  disabled={loading}
                >
                  {loading ? (
                    <ActivityIndicator size="small" color="#fff" />
                  ) : (
                    <>
                      <Feather name="plus" size={16} color="#fff" />
                      <ThemedText style={styles.buttonText}>Add Member</ThemedText>
                    </>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      </ThemedView>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  searchSection: {
    marginBottom: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(100, 100, 100, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 12,
    height: 48,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    height: 48,
  },
  clearButton: {
    padding: 6,
  },
  resultsList: {
    flex: 1,
  },
  resultsContent: {
    paddingBottom: 16,
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: 'rgba(100, 100, 100, 0.06)',
  },
  userImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  userImagePlaceholder: {
    backgroundColor: 'rgba(148, 163, 184, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  userPhone: {
    fontSize: 14,
    color: '#64748b',
  },
  centeredContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchingText: {
    marginTop: 8,
    fontSize: 16,
    color: '#64748b',
  },
  noResultsContainer: {
    flex: 1,
    alignItems: 'center',
    paddingTop: 60,
    paddingHorizontal: 20,
  },
  noResultsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  noResultsText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#64748b',
    marginBottom: 24,
  },
  inviteContainer: {
    width: '100%',
    padding: 16,
    backgroundColor: 'rgba(100, 100, 100, 0.06)',
    borderRadius: 12,
  },
  inviteLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  inviteInput: {
    backgroundColor: 'rgba(100, 100, 100, 0.1)',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 16,
  },
  sendInviteButton: {
    backgroundColor: '#f97316',
    borderRadius: 8,
    padding: 14,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
    marginLeft: 8,
  },
  selectedUserSection: {
    paddingTop: 16,
  },
  selectedUserHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  deselectButton: {
    padding: 6,
  },
  deselectText: {
    color: '#f97316',
    fontWeight: '500',
  },
  selectedUserCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: 'rgba(100, 100, 100, 0.06)',
    borderRadius: 12,
    marginBottom: 20,
  },
  selectedUserImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 16,
  },
  selectedUserInfo: {
    flex: 1,
  },
  selectedUserName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  selectedUserPhone: {
    fontSize: 16,
    color: '#64748b',
  },
  dropdownContainer: {
    marginBottom: 20,
    zIndex: 2000,
  },
  dropdownLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
  },
  dropdown: {
    borderWidth: 1,
    borderColor: 'rgba(100, 100, 100, 0.2)',
    borderRadius: 8,
  },
  buttonsContainer: {
    flexDirection: 'row',
    marginTop: 24,
  },
  cancelButton: {
    flex: 1,
    padding: 14,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(100, 100, 100, 0.2)',
    alignItems: 'center',
    marginRight: 8,
  },
  cancelButtonText: {
    fontWeight: '600',
    fontSize: 16,
  },
  addButton: {
    flex: 2,
    backgroundColor: '#f97316',
    padding: 14,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
}); 