-- Create sub_contractors table
CREATE TABLE IF NOT EXISTS sub_contractors (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    site_id UUID NOT NULL REFERENCES sites(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_sub_contractors_site_id ON sub_contractors(site_id);
CREATE INDEX IF NOT EXISTS idx_sub_contractors_category ON sub_contractors(category);

-- Enable Row Level Security (RLS)
ALTER TABLE sub_contractors ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only access sub-contractors for sites they have access to
CREATE POLICY "Users can view sub-contractors for their sites" ON sub_contractors
    FOR SELECT USING (
        site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert sub-contractors for their sites" ON sub_contractors
    FOR INSERT WITH CHECK (
        site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update sub-contractors for their sites" ON sub_contractors
    FOR UPDATE USING (
        site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete sub-contractors for their sites" ON sub_contractors
    FOR DELETE USING (
        site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    );

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_sub_contractors_updated_at 
    BEFORE UPDATE ON sub_contractors 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column(); 