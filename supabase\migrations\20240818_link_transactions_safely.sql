-- Create a function to link transactions without triggering RLS policy recursion
CREATE OR REPLACE FUNCTION link_transactions_safely(transaction1_id UUID, transaction2_id UUID)
RETURNS VOID AS $$
BEGIN
  -- Direct update on the database level that bypasses RLS policies
  UPDATE transactions
  SET linked_transaction_id = transaction2_id
  WHERE id = transaction1_id;
  
  -- No need to update the second transaction as it already has the link from creation
  -- But we can add this as a safety measure
  IF (SELECT linked_transaction_id FROM transactions WHERE id = transaction2_id) IS NULL THEN
    UPDATE transactions
    SET linked_transaction_id = transaction1_id
    WHERE id = transaction2_id;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 