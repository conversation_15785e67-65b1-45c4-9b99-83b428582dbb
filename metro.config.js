const { getDefaultConfig } = require('expo/metro-config');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

config.resolver.unstable_enablePackageExports = false;

// Web platform configuration
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Platform-specific resolutions
config.resolver.alias = {
  ...config.resolver.alias,
  '@react-native-async-storage/async-storage': require.resolve('@react-native-async-storage/async-storage'),
};

// Additional resolver configuration for better compatibility
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];
config.resolver.sourceExts = [...config.resolver.sourceExts, 'mjs'];

module.exports = config; 