import { useColorScheme } from '@/hooks/useColorScheme';
import { supabase } from '@/lib/supabase';
import { MaterialIcons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  Image,
  Linking,
  Pressable,
  StyleSheet,
  Text,
  View,
} from 'react-native';

interface CategoryBannerData {
  id: number;
  category_id: number;
  title: string;
  description: string;
  image_url: string;
  banner_url: string;
  is_active: boolean;
  display_order: number;
}

interface CategoryBannerProps {
  categoryId: number | null;
}

export default function CategoryBanner({ categoryId }: CategoryBannerProps) {
  const colorScheme = useColorScheme();
  const [bannerData, setBannerData] = useState<CategoryBannerData | null>(null);
  const [loading, setLoading] = useState(false);
  const { width } = Dimensions.get('window');

  // Colors
  const backgroundColor = '#fff';
  const textColor = '#000';
  const secondaryTextColor = '#666';
  const primaryColor = '#f97316';

  // Load banner data for the selected category
  const loadBannerData = async (catId: number) => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('category_banners')
        .select('*')
        .eq('category_id', catId)
        .eq('is_active', true)
        .order('display_order')
        .limit(1)
        .single();

      if (error) {
        console.log('No banner found for category:', catId);
        setBannerData(null);
        return;
      }

      setBannerData(data);
    } catch (error) {
      console.error('Error loading banner data:', error);
      setBannerData(null);
    } finally {
      setLoading(false);
    }
  };

  // Load banner when category changes
  useEffect(() => {
    if (categoryId) {
      loadBannerData(categoryId);
    } else {
      setBannerData(null);
    }
  }, [categoryId]);

  // Handle banner press
  const handleBannerPress = async () => {
    if (bannerData?.banner_url) {
      try {
        const supported = await Linking.canOpenURL(bannerData.banner_url);
        if (supported) {
          await Linking.openURL(bannerData.banner_url);
        }
      } catch (error) {
        console.error('Error opening banner URL:', error);
      }
    }
  };

  // Don't render if no category selected or no banner data
  if (!categoryId || !bannerData) {
    return null;
  }

  if (loading) {
    return (
      <View style={[styles.container, { width: width - 32 }]}>
        <View style={[styles.bannerContainer, styles.loadingContainer]}>
          <ActivityIndicator size="large" color={primaryColor} />
          <Text style={[styles.loadingText, { color: secondaryTextColor }]}>
            Loading banner...
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { width: width - 32 }]}>
      <Pressable
        style={styles.bannerContainer}
        onPress={handleBannerPress}
        android_ripple={{ color: 'rgba(0, 0, 0, 0.1)' }}
      >
        {/* Banner Image */}
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: bannerData.image_url }}
            style={styles.bannerImage}
            resizeMode="cover"
          />
          {/* Gradient Overlay */}
          <View style={styles.gradientOverlay} />
        </View>

        {/* Banner Content */}
        <View style={styles.contentContainer}>
          <View style={styles.textContainer}>
            <Text style={[styles.bannerTitle, { color: '#fff' }]} numberOfLines={2}>
              {bannerData.title}
            </Text>
            <Text style={[styles.bannerDescription, { color: '#fff' }]} numberOfLines={2}>
              {bannerData.description}
            </Text>
          </View>

          {/* Action Button */}
          <View style={styles.actionContainer}>
            <View style={[styles.actionButton, { backgroundColor: primaryColor }]}>
              <Text style={styles.actionButtonText}>Shop Now</Text>
              <MaterialIcons name="arrow-forward" size={16} color="#fff" />
            </View>
          </View>
        </View>
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
    alignSelf: 'center',
  },
  bannerContainer: {
    height: 200,
    borderRadius: 16,
    overflow: 'hidden',
    backgroundColor: '#f8f9fa',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
  },
  imageContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  bannerImage: {
    width: '100%',
    height: '100%',
  },
  gradientOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
  },
  contentContainer: {
    flex: 1,
    padding: 20,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  textContainer: {
    flex: 1,
    marginRight: 16,
  },
  bannerTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 6,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  bannerDescription: {
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 18,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  actionContainer: {
    alignItems: 'flex-end',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginRight: 6,
  },
});
