import { Alert } from 'react-native';
import { supabase } from '@/lib/supabase';

export interface AdditionalUserPaymentRequest {
  subscriptionId: string;
  userId: string;
  userEmail: string;
  additionalUserCount: number;
  totalAmount: number; // in rupees
}

export interface PaymentResponse {
  success: boolean;
  paymentId?: string;
  orderId?: string;
  error?: string;
}

/**
 * Create Razorpay order for additional user payment
 */
export async function createAdditionalUserOrder(request: AdditionalUserPaymentRequest): Promise<any> {
  try {
    // Calculate amount in paise (Razorpay uses paise)
    const amountInPaise = request.totalAmount * 100;
    
    // Create order data
    const orderData = {
      amount: amountInPaise,
      currency: 'INR',
      receipt: `additional_user_${request.subscriptionId}_${Date.now()}`,
      notes: {
        subscription_id: request.subscriptionId,
        user_id: request.userId,
        additional_users: request.additionalUserCount.toString(),
        type: 'additional_user_payment'
      }
    };

    // TODO: Replace with actual Razorpay order creation
    // For now, we'll simulate the order creation
    const simulatedOrder = {
      id: `order_${Date.now()}`,
      amount: amountInPaise,
      currency: 'INR',
      receipt: orderData.receipt,
      status: 'created',
      notes: orderData.notes
    };

    return simulatedOrder;
  } catch (error) {
    console.error('Error creating additional user order:', error);
    throw new Error('Failed to create payment order');
  }
}

/**
 * Process additional user payment
 */
export async function processAdditionalUserPayment(
  request: AdditionalUserPaymentRequest
): Promise<PaymentResponse> {
  try {
    // Create Razorpay order
    const order = await createAdditionalUserOrder(request);
    
    // TODO: Integrate with actual Razorpay payment
    // For now, we'll simulate the payment process
    return new Promise((resolve) => {
      Alert.alert(
        'Additional User Payment',
        `Payment of ₹${request.totalAmount} for ${request.additionalUserCount} additional user(s) is required.\n\nThis will integrate with Razorpay for actual payment processing.`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => resolve({ success: false, error: 'Payment cancelled by user' })
          },
          {
            text: 'Simulate Payment',
            onPress: async () => {
              try {
                // Simulate payment success
                const paymentResult = await simulatePaymentSuccess(order, request);
                resolve(paymentResult);
              } catch (error) {
                resolve({ 
                  success: false, 
                  error: error instanceof Error ? error.message : 'Payment failed' 
                });
              }
            }
          }
        ]
      );
    });
  } catch (error) {
    console.error('Error processing additional user payment:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Payment processing failed'
    };
  }
}

/**
 * Simulate payment success (for demo purposes)
 */
async function simulatePaymentSuccess(
  order: any, 
  request: AdditionalUserPaymentRequest
): Promise<PaymentResponse> {
  try {
    // Simulate payment processing delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Generate simulated payment data
    const paymentId = `pay_${Date.now()}`;
    const signature = `signature_${Date.now()}`;
    
    // Update subscription with additional user payment
    const { error: updateError } = await supabase
      .from('user_subscriptions')
      .update({
        additional_users: request.additionalUserCount,
        updated_at: new Date().toISOString()
      })
      .eq('id', request.subscriptionId);

    if (updateError) {
      throw new Error('Failed to update subscription with additional users');
    }

    // Record the payment transaction (you might want to create a separate payments table)
    const { error: paymentError } = await supabase
      .from('razorpay_orders')
      .insert({
        user_id: request.userId,
        order_id: order.id,
        payment_id: paymentId,
        amount: order.amount,
        currency: order.currency,
        status: 'paid',
        notes: JSON.stringify({
          ...order.notes,
          payment_type: 'additional_user',
          user_email: request.userEmail
        }),
        created_at: new Date().toISOString()
      });

    if (paymentError) {
      console.error('Error recording payment:', paymentError);
      // Don't fail the payment for this, just log it
    }

    return {
      success: true,
      paymentId: paymentId,
      orderId: order.id
    };
  } catch (error) {
    console.error('Error in simulated payment:', error);
    throw error;
  }
}

/**
 * Calculate additional user cost
 */
export function calculateAdditionalUserCost(
  currentUserCount: number,
  includedUsers: number = 5,
  pricePerUser: number = 249
): { additionalUsers: number; totalCost: number } {
  const additionalUsers = Math.max(0, currentUserCount - includedUsers);
  const totalCost = additionalUsers * pricePerUser;
  
  return {
    additionalUsers,
    totalCost
  };
}

/**
 * Get payment summary for additional users
 */
export function getAdditionalUserPaymentSummary(
  currentActiveUsers: number,
  newUsersToAdd: number = 1,
  includedUsers: number = 5,
  pricePerUser: number = 249
) {
  const totalUsersAfterAddition = currentActiveUsers + 1 + newUsersToAdd; // +1 for owner
  const currentAdditionalUsers = Math.max(0, (currentActiveUsers + 1) - includedUsers);
  const newAdditionalUsers = Math.max(0, totalUsersAfterAddition - includedUsers);
  const additionalUsersToPayFor = newAdditionalUsers - currentAdditionalUsers;
  const paymentRequired = additionalUsersToPayFor > 0;
  const paymentAmount = additionalUsersToPayFor * pricePerUser;

  return {
    currentTotalUsers: currentActiveUsers + 1,
    newTotalUsers: totalUsersAfterAddition,
    currentAdditionalUsers,
    newAdditionalUsers,
    additionalUsersToPayFor,
    paymentRequired,
    paymentAmount,
    pricePerUser,
    includedUsers
  };
}

/**
 * Validate if user can be added without payment
 */
export function canAddUserWithoutPayment(
  currentActiveUsers: number,
  includedUsers: number = 5
): boolean {
  const totalUsers = currentActiveUsers + 1; // +1 for owner
  return totalUsers < includedUsers;
}

/**
 * Real Razorpay integration (to be implemented)
 */
export async function createRazorpayOrder(orderData: any): Promise<any> {
  // TODO: Implement actual Razorpay order creation
  // This would typically call your backend API that creates the order using Razorpay API
  
  try {
    const response = await fetch('/api/razorpay/create-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderData),
    });

    if (!response.ok) {
      throw new Error('Failed to create Razorpay order');
    }

    return await response.json();
  } catch (error) {
    console.error('Error creating Razorpay order:', error);
    throw error;
  }
}

/**
 * Handle Razorpay payment success
 */
export async function handleRazorpaySuccess(
  paymentData: any,
  subscriptionId: string,
  additionalUserCount: number
): Promise<void> {
  try {
    // Verify payment signature (should be done on backend)
    // Update subscription with additional users
    const { error } = await supabase
      .from('user_subscriptions')
      .update({
        additional_users: additionalUserCount,
        updated_at: new Date().toISOString()
      })
      .eq('id', subscriptionId);

    if (error) {
      throw new Error('Failed to update subscription');
    }

    // Record payment details
    await supabase
      .from('razorpay_orders')
      .insert({
        order_id: paymentData.razorpay_order_id,
        payment_id: paymentData.razorpay_payment_id,
        signature: paymentData.razorpay_signature,
        status: 'paid',
        notes: JSON.stringify({
          type: 'additional_user_payment',
          additional_users: additionalUserCount
        }),
        created_at: new Date().toISOString()
      });

  } catch (error) {
    console.error('Error handling Razorpay success:', error);
    throw error;
  }
}
