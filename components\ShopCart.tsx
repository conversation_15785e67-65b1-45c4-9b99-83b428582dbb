import { useColorScheme } from '@/hooks/useColorScheme';
import { supabase } from '@/lib/supabase';
import { FontAwesome5, MaterialIcons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import { Dimensions, FlatList, Image, Pressable, ScrollView, StyleSheet, Text, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width } = Dimensions.get('window');

interface CartItem {
  id: number;
  name: string;
  price: number;
  original_price?: number;
  quantity: number;
  unit: string;
  brand?: string;
}

interface Product {
  id: number;
  categories: number;
  name: string;
  description: string;
  image_url: string;
  price: number;
  original_price: number;
  unit: string;
  brand: string;
  stock_quantity: number;
  is_active: boolean;
}

interface ShopCartProps {
  cartItems: CartItem[];
  onUpdateQuantity: (itemId: number, quantity: number) => void;
  onRemoveItem: (itemId: number) => void;
  onCheckout: () => void;
  onAddToCart?: (product: Product) => void;
}

export default function ShopCart({ cartItems, onUpdateQuantity, onRemoveItem, onCheckout, onAddToCart }: ShopCartProps) {
  const colorScheme = useColorScheme();
  const insets = useSafeAreaInsets();
  const primaryColor = '#f97316';
  const [similarProducts, setSimilarProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);

  // Force light mode for shop cart
  const backgroundColor = '#fff';
  const cardBackground = '#fff';
  const textColor = '#000';
  const secondaryTextColor = '#666';
  const borderColor = '#e5e5e5';

  const getTotalPrice = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const getTotalItems = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  };

  // Load similar products
  const loadSimilarProducts = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('shop_products')
        .select('*')
        .eq('is_active', true)
        .limit(6)
        .order('id');

      if (error) throw error;

      if (data) {
        const processedProducts = data.map(product => ({
          ...product,
          price: parseFloat(product.price) || 0,
          original_price: parseFloat(product.original_price) || 0
        }));
        setSimilarProducts(processedProducts);
      }
    } catch (error) {
      console.error('Error loading similar products:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSimilarProducts();
  }, []);

  // Render similar product item
  const renderSimilarProduct = ({ item }: { item: Product }) => (
    <View style={[styles.similarProductCard, { backgroundColor: cardBackground }]}>
      <Image
        source={{ uri: item.image_url || 'https://via.placeholder.com/100' }}
        style={styles.similarProductImage}
        resizeMode="cover"
      />
      <Text style={[styles.similarProductName, { color: textColor }]} numberOfLines={2}>
        {item.name}
      </Text>
      <Text style={[styles.similarProductPrice, { color: primaryColor }]}>
        ₹{item.price.toFixed(2)}
      </Text>
      <Pressable
        style={[styles.addSimilarButton, { backgroundColor: primaryColor }]}
        onPress={() => onAddToCart && onAddToCart(item)}
      >
        <Text style={styles.addSimilarButtonText}>ADD</Text>
      </Pressable>
    </View>
  );

  if (cartItems.length === 0) {
    return (
      <View style={[styles.emptyContainer, { backgroundColor }]}>
        <FontAwesome5 name="shopping-cart" size={64} color={secondaryTextColor} />
        <Text style={[styles.emptyTitle, { color: textColor }]}>Your cart is empty</Text>
        <Text style={[styles.emptySubtitle, { color: secondaryTextColor }]}>
          Add some items to get started
        </Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor }]}>
      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
        <Text style={[styles.headerTitle, { color: textColor }]}>Checkout</Text>
        <Pressable style={styles.shareButton}>
          <MaterialIcons name="share" size={20} color={primaryColor} />
          <Text style={[styles.shareText, { color: primaryColor }]}>Share</Text>
        </Pressable>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Delivery Info */}
        <View style={[styles.deliveryInfo, { backgroundColor: '#f0f9ff' }]}>
          <View style={styles.deliveryIconContainer}>
            <MaterialIcons name="access-time" size={16} color={primaryColor} />
          </View>
          <View style={styles.deliveryTextContainer}>
            <Text style={[styles.deliveryTitle, { color: textColor }]}>Delivery in 2-4 hours</Text>
            <Text style={[styles.deliverySubtitle, { color: secondaryTextColor }]}>
              Shipment of {getTotalItems()} item{getTotalItems() > 1 ? 's' : ''}
            </Text>
          </View>
        </View>

        {/* Cart Items */}
        {cartItems.map((item) => (
          <View key={item.id} style={[styles.cartItem, { backgroundColor: cardBackground }]}>
            <View style={styles.itemImageContainer}>
              <Image
                source={{ uri: 'https://via.placeholder.com/60' }}
                style={styles.itemImage}
                resizeMode="cover"
              />
            </View>
            <View style={styles.itemDetails}>
              <Text style={[styles.itemName, { color: textColor }]} numberOfLines={2}>
                {item.name}
              </Text>
              <View style={styles.priceContainer}>
                {item.original_price && item.original_price > item.price && (
                  <Text style={[styles.originalPrice, { color: secondaryTextColor }]}>
                    ₹{item.original_price.toFixed(0)}
                  </Text>
                )}
                <Text style={[styles.currentPrice, { color: textColor }]}>
                  ₹{item.price.toFixed(0)}
                </Text>
              </View>
            </View>
            <View style={styles.itemQuantityContainer}>
              <Pressable
                style={[styles.quantityButton, { backgroundColor: primaryColor }]}
                onPress={() => {
                  if (item.quantity > 1) {
                    onUpdateQuantity(item.id, item.quantity - 1);
                  } else {
                    onRemoveItem(item.id);
                  }
                }}
              >
                <MaterialIcons name="remove" size={16} color="#fff" />
              </Pressable>
              <Text style={[styles.quantityText, { color: '#fff', backgroundColor: primaryColor }]}>
                {item.quantity}
              </Text>
              <Pressable
                style={[styles.quantityButton, { backgroundColor: primaryColor }]}
                onPress={() => onUpdateQuantity(item.id, item.quantity + 1)}
              >
                <MaterialIcons name="add" size={16} color="#fff" />
              </Pressable>
            </View>
          </View>
        ))}

        {/* Similar Products Section */}
        <View style={styles.similarProductsSection}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>You might also like</Text>
          <FlatList
            data={similarProducts}
            renderItem={renderSimilarProduct}
            keyExtractor={(item) => item.id.toString()}
            numColumns={2}
            columnWrapperStyle={styles.similarProductRow}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        </View>

        {/* Delivery Address */}
        <View style={[styles.deliveryAddress, { backgroundColor: cardBackground }]}>
          <MaterialIcons name="home" size={20} color={primaryColor} />
          <View style={styles.addressTextContainer}>
            <Text style={[styles.addressTitle, { color: textColor }]}>Delivering to Home</Text>
            <Text style={[styles.addressSubtitle, { color: secondaryTextColor }]}>
              5th floor, Flat no. 507, J6 Block, Marasandra ...
            </Text>
          </View>
          <Pressable>
            <Text style={[styles.changeText, { color: primaryColor }]}>Change</Text>
          </Pressable>
        </View>
      </ScrollView>

      {/* Bottom Order Summary */}
      <View style={[styles.orderSummary, { backgroundColor: cardBackground }]}>
        <View style={styles.totalContainer}>
          <Text style={[styles.totalAmount, { color: '#fff' }]}>₹{getTotalPrice().toFixed(0)}</Text>
          <Text style={[styles.totalLabel, { color: '#fff' }]}>TOTAL</Text>
        </View>
        <View style={styles.paymentMethod}>
          <View style={styles.paymentTextContainer}>
            <Text style={[styles.paymentText, { color: textColor }]}>PAY USING</Text>
            <Text style={[styles.paymentUPI, { color: primaryColor }]}>PhonePe UPI</Text>
          </View>
        </View>
        <Pressable
          style={[styles.placeOrderButton, { backgroundColor: primaryColor }]}
          onPress={onCheckout}
        >
          <Text style={styles.placeOrderText}>Place Order</Text>
          <MaterialIcons name="arrow-forward" size={16} color="#fff" />
        </Pressable>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e5e5',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  shareText: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '500',
  },
  scrollView: {
    flex: 1,
  },
  deliveryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 8,
  },
  deliveryIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  deliveryTextContainer: {
    flex: 1,
  },
  deliveryTitle: {
    fontSize: 14,
    fontWeight: '600',
  },
  deliverySubtitle: {
    fontSize: 12,
    marginTop: 2,
  },
  cartItem: {
    flexDirection: 'row',
    padding: 16,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 8,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  itemImageContainer: {
    marginRight: 12,
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  itemUnit: {
    fontSize: 12,
    marginBottom: 4,
  },
  itemPrice: {
    fontSize: 12,
    marginBottom: 8,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  originalPrice: {
    fontSize: 12,
    textDecorationLine: 'line-through',
    marginRight: 8,
  },
  currentPrice: {
    fontSize: 14,
    fontWeight: '600',
  },
  wishlistButton: {
    alignSelf: 'flex-start',
  },
  wishlistText: {
    fontSize: 12,
    textDecorationLine: 'underline',
  },
  itemQuantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 24,
    height: 24,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityText: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
    minWidth: 24,
  },
  similarProductsSection: {
    padding: 16,
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 16,
  },
  similarProductRow: {
    justifyContent: 'space-between',
  },
  similarProductCard: {
    width: (width - 48) / 2,
    padding: 12,
    borderRadius: 8,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  similarProductImage: {
    width: '100%',
    height: 80,
    borderRadius: 6,
    marginBottom: 8,
  },
  similarProductName: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
    height: 32,
  },
  similarProductPrice: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  addSimilarButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    alignItems: 'center',
  },
  addSimilarButtonText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  deliveryAddress: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 8,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
  },
  addressTextContainer: {
    flex: 1,
    marginLeft: 12,
  },
  addressTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  addressSubtitle: {
    fontSize: 12,
  },
  changeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  orderSummary: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e5e5e5',
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  paymentTextContainer: {
    alignItems: 'center',
  },
  paymentText: {
    fontSize: 10,
    fontWeight: '500',
    marginBottom: 2,
  },
  paymentUPI: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  totalContainer: {
    backgroundColor: '#333',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 6,
    marginRight: 12,
    alignItems: 'center',
    minWidth: 80,
  },
  totalAmount: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  totalLabel: {
    fontSize: 11,
    fontWeight: '500',
  },
  placeOrderButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 6,
    minWidth: 140,
    justifyContent: 'center',
  },
  placeOrderText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginRight: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
});
