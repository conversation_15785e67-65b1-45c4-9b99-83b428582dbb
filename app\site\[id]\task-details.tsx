import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { exportTaskReportAsExcel, exportTaskReportAsPDF } from '@/lib/export';
import { supabase } from '@/lib/supabase';
import { MaterialIcons } from '@expo/vector-icons';
import { isPast, isToday, parseISO } from 'date-fns';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Platform,
  ScrollView,
  StyleSheet,
  ToastAndroid,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';

// Type definitions
type Task = {
  id: string;
  site_id: string;
  name: string;
  work_category: string;
  status: 'pending' | 'in progress' | 'completed';
  overall_progress: number;
  due_date: string;
  created_at: string;
  updated_at: string;
  created_by: string;
};

type Subcategory = {
  id: string;
  task_id: string;
  name: string;
  quantity: number;
  completed_quantity: number;
  unit_of_measure: string;
  progress_percentage: number;
  created_at: string;
  updated_at: string;
};

export default function TaskDetailsScreen() {
  const { id: siteId, taskId } = useLocalSearchParams();
  const [task, setTask] = useState<Task | null>(null);
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [creatorName, setCreatorName] = useState('');
  const [siteName, setSiteName] = useState('');
  const [isExporting, setIsExporting] = useState(false);
  const colorScheme = useColorScheme();

  // Fetch task data
  useEffect(() => {
    const fetchTaskData = async () => {
      if (!siteId || !taskId) return;
      
      try {
        setLoading(true);
        
        // Get current user
        const { data: userData } = await supabase.auth.getUser();
        if (!userData.user) return;
        
        // Get user role for the site
        const { data: userMemberData } = await supabase
          .from('site_members')
          .select('role')
          .eq('site_id', siteId)
          .eq('user_id', userData.user.id)
          .single();
        
        if (userMemberData) {
          setUserRole(userMemberData.role);
        }
        
        // Fetch site name for the PDF report
        const { data: siteData } = await supabase
          .from('sites')
          .select('name')
          .eq('id', siteId)
          .single();
          
        if (siteData) {
          setSiteName(siteData.name);
        }
        
        // Fetch task data
        const { data: taskData, error: taskError } = await supabase
          .from('tasks')
          .select('*')
          .eq('id', taskId)
          .single();
        
        if (taskError || !taskData) {
          console.error('Error fetching task:', taskError);
          Alert.alert('Error', 'Failed to load task details');
          router.back();
          return;
        }
        
        setTask(taskData as Task);
        
        // Fetch creator name
        if (taskData.created_by) {
          const { data: profileData } = await supabase
            .from('profiles')
            .select('full_name')
            .eq('id', taskData.created_by)
            .single();
          
          if (profileData) {
            setCreatorName(profileData.full_name);
          }
        }
        
        // Fetch subcategories
        const { data: subcategoriesData, error: subcategoriesError } = await supabase
          .from('task_subcategories')
          .select('*')
          .eq('task_id', taskId)
          .order('created_at', { ascending: true });
        
        if (!subcategoriesError && subcategoriesData) {
          setSubcategories(subcategoriesData as Subcategory[]);
        }
        
      } catch (error) {
        console.error('Error:', error);
        Alert.alert('Error', 'An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };
    
    fetchTaskData();
  }, [siteId, taskId]);

  // Handle task edit navigation
  const handleEditTask = () => {
    if (userRole === 'Member') {
      Alert.alert('Permission Denied', 'You do not have permission to edit tasks');
      return;
    }
    
    router.push(`/site/${siteId}/edit-task?taskId=${taskId}`);
  };

  // Handle task deletion
  const handleDeleteTask = async () => {
    // Only Super Admin can delete tasks
    if (userRole !== 'Super Admin') {
      Alert.alert('Permission Denied', 'Only Super Admins can delete tasks');
      return;
    }
    
    Alert.alert(
      'Delete Task',
      'Are you sure you want to delete this task? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('tasks')
                .delete()
                .eq('id', taskId);
              
              if (error) {
                console.error('Error deleting task:', error);
                Alert.alert('Error', 'Failed to delete task');
                return;
              }
              
              Alert.alert('Success', 'Task deleted successfully');
              router.back();
            } catch (error) {
              console.error('Error:', error);
              Alert.alert('Error', 'An unexpected error occurred');
            }
          },
        },
      ]
    );
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#f59e0b'; // Amber
      case 'in progress':
        return '#3b82f6'; // Blue
      case 'completed':
        return '#10b981'; // Green
      default:
        return '#64748b'; // Gray
    }
  };

  // Determine due date styling based on date
  const getDueDateStyle = (dueDateStr: string) => {
    const dueDate = parseISO(dueDateStr);
    
    if (isPast(dueDate) && !isToday(dueDate)) {
      return { color: '#ef4444' }; // Red for overdue
    } else if (isToday(dueDate)) {
      return { color: '#f59e0b' }; // Amber for today
    }
    
    return {}; // Default styling
  };

  // Handle PDF export
  const handleExportPDF = async () => {
    if (!task || !subcategories || !siteName) return;
    
    try {
      setIsExporting(true);
      // Prepare task subcategories in the format expected by the export function
      const taskSubcategories: Record<string, Subcategory[]> = {
        [task.id]: subcategories
      };
      
      await exportTaskReportAsPDF([task], taskSubcategories, siteName);
      if (Platform.OS === 'android') {
        ToastAndroid.show('PDF exported successfully', ToastAndroid.SHORT);
      } else {
        Alert.alert('Success', 'PDF exported successfully');
      }
    } catch (error) {
      console.error('Error exporting PDF:', error);
      Alert.alert('Error', 'Failed to export PDF');
    } finally {
      setIsExporting(false);
    }
  };

  // Handle Excel export
  const handleExportExcel = async () => {
    if (!task || !subcategories || !siteName) return;
    
    try {
      setIsExporting(true);
      await exportTaskReportAsExcel(task, subcategories, siteName, creatorName);
      if (Platform.OS === 'android') {
        ToastAndroid.show('Excel file exported successfully', ToastAndroid.SHORT);
      } else {
        Alert.alert('Success', 'Excel file exported successfully');
      }
    } catch (error) {
      console.error('Error exporting Excel:', error);
      Alert.alert('Error', 'Failed to export Excel file');
    } finally {
      setIsExporting(false);
    }
  };

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#f97316" />
      </ThemedView>
    );
  }

  if (!task) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText>Task not found</ThemedText>
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      
      <Stack.Screen options={{ title: 'Tasks Details' }} />
      
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Task Details */}
        <View style={styles.header}>
          <ThemedText style={styles.title}>{task.name}</ThemedText>
          <View style={styles.actionsContainer}>
            {/* Export Buttons */}
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: '#4CAF50' }]}
              onPress={handleExportExcel}
              disabled={isExporting}
            >
              <MaterialIcons
                name="file-download"
                size={24}
                color="white"
              />
              <ThemedText style={styles.actionButtonText}>Excel</ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: '#FF5722' }]}
              onPress={handleExportPDF}
              disabled={isExporting}
            >
              <MaterialIcons
                name="picture-as-pdf"
                size={24}
                color="white"
              />
              <ThemedText style={styles.actionButtonText}>PDF</ThemedText>
            </TouchableOpacity>

            {userRole !== 'Member' && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleEditTask}
              >
                <MaterialIcons
                  name="edit"
                  size={24}
                  color={colorScheme === 'dark' ? 'white' : 'black'}
                />
              </TouchableOpacity>
            )}
            
            {userRole === 'Super Admin' && (
              <TouchableOpacity
                style={[styles.actionButton, styles.deleteButton]}
                onPress={handleDeleteTask}
              >
                <MaterialIcons name="delete" size={24} color="white" />
              </TouchableOpacity>
            )}
          </View>
        </View>
        
        <View style={styles.progressSection}>
          <ThemedText style={styles.sectionTitle}>Overall Progress</ThemedText>
          <View style={styles.progressContainer}>
            <View style={styles.progressBarContainer}>
              <View 
                style={[
                  styles.progressBar, 
                  { width: `${task.overall_progress}%`, backgroundColor: getStatusColor(task.status) }
                ]} 
              />
            </View>
            <ThemedText style={styles.progressPercentage}>{task.overall_progress}%</ThemedText>
          </View>
        </View>
        
        <View style={styles.subcategoriesSection}>
          <ThemedText style={styles.sectionTitle}>Subcategories</ThemedText>
          
          {subcategories.length === 0 ? (
            <ThemedText style={styles.emptySubtext}>No subcategories found for this task</ThemedText>
          ) : (
            subcategories.map((subcategory) => (
              <View key={subcategory.id} style={styles.subcategoryCard}>
                <ThemedText style={styles.subcategoryName}>{subcategory.name}</ThemedText>
                
                <View style={styles.quantityRow}>
                  <View style={styles.quantityItem}>
                    <ThemedText style={styles.quantityLabel}>Target</ThemedText>
                    <ThemedText style={styles.quantityValue}>
                      {subcategory.quantity} {subcategory.unit_of_measure}
                    </ThemedText>
                  </View>
                  
                  <View style={styles.quantityItem}>
                    <ThemedText style={styles.quantityLabel}>Completed</ThemedText>
                    <ThemedText style={styles.quantityValue}>
                      {subcategory.completed_quantity} {subcategory.unit_of_measure}
                    </ThemedText>
                  </View>
                </View>
                
                <View style={styles.subcategoryProgressContainer}>
                  <ThemedText style={styles.subcategoryProgressLabel}>Progress</ThemedText>
                  <View style={styles.progressRow}>
                    <View style={styles.progressBarContainer}>
                      <View 
                        style={[
                          styles.progressBar, 
                          { width: `${subcategory.progress_percentage}%`, backgroundColor: getStatusColor(task.status) }
                        ]} 
                      />
                    </View>
                    <ThemedText style={styles.progressPercentage}>{subcategory.progress_percentage}%</ThemedText>
                  </View>
                </View>
              </View>
            ))
          )}
        </View>
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 24,
    backgroundColor: 'rgba(100, 100, 100, 0.06)',
    borderRadius: 12,
    padding: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  taskName: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 12,
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    marginLeft: 8,
    color: '#64748b',
  },
  progressSection: {
    marginBottom: 24,
    backgroundColor: 'rgba(100, 100, 100, 0.06)',
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  progressBarContainer: {
    flex: 1,
    height: 12,
    backgroundColor: 'rgba(100, 100, 100, 0.1)',
    borderRadius: 6,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 6,
  },
  progressPercentage: {
    fontSize: 16,
    fontWeight: '600',
    minWidth: 40,
    textAlign: 'right',
  },
  subcategoriesSection: {
    marginBottom: 100, // Extra space for action buttons
    backgroundColor: 'rgba(100, 100, 100, 0.06)',
    borderRadius: 12,
    padding: 16,
  },
  subcategoryCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
  },
  subcategoryName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  quantityRow: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  quantityItem: {
    flex: 1,
  },
  quantityLabel: {
    fontSize: 12,
    color: '#64748b',
    marginBottom: 2,
  },
  quantityValue: {
    fontSize: 14,
    fontWeight: '500',
  },
  subcategoryProgressContainer: {
    marginTop: 4,
  },
  subcategoryProgressLabel: {
    fontSize: 12,
    color: '#64748b',
    marginBottom: 4,
  },
  progressRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  emptySubtext: {
    textAlign: 'center',
    color: '#94a3b8',
    fontSize: 14,
    paddingVertical: 20,
  },
  actionButtonsContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    padding: 16,
    gap: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderTopWidth: 1,
    borderTopColor: 'rgba(100, 100, 100, 0.1)',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    marginLeft: 8,
  },
  actionButtonText: {
    color: 'white',
    marginLeft: 4,
    fontSize: 14,
  },
  deleteButton: {
    backgroundColor: '#ef4444',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 12,
  },
  actionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
}); 