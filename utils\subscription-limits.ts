import { Alert } from 'react-native';
import { UserLimits } from '@/hooks/useSubscription';

export interface SubscriptionLimitCheck {
  canProceed: boolean;
  requiresPayment: boolean;
  paymentAmount: number;
  message: string;
  title: string;
}

/**
 * Check if user can add a new team member to their subscription
 */
export function checkUserAdditionLimit(
  userLimits: UserLimits | null,
  usersToAdd: number = 1
): SubscriptionLimitCheck {
  if (!userLimits) {
    return {
      canProceed: false,
      requiresPayment: false,
      paymentAmount: 0,
      message: 'Unable to check subscription limits. Please try again.',
      title: 'Error'
    };
  }

  const newTotalUsers = userLimits.totalUsers + usersToAdd;
  const newAdditionalUsers = Math.max(0, newTotalUsers - userLimits.includedUsers);
  const additionalUsersToPayFor = newAdditionalUsers - userLimits.additionalUsers;
  const paymentAmount = Math.max(0, additionalUsersToPayFor) * 249; // ₹249 per additional user

  // If within included limits
  if (newTotalUsers <= userLimits.includedUsers) {
    return {
      canProceed: true,
      requiresPayment: false,
      paymentAmount: 0,
      message: `You can add ${usersToAdd} user${usersToAdd > 1 ? 's' : ''} within your included limit.`,
      title: 'Add User'
    };
  }

  // If requires payment for additional users
  return {
    canProceed: true,
    requiresPayment: true,
    paymentAmount,
    message: `Adding ${usersToAdd} user${usersToAdd > 1 ? 's' : ''} will require payment of ₹${paymentAmount} (₹249 per additional user per month). Would you like to proceed?`,
    title: 'Additional User Payment Required'
  };
}

/**
 * Show alert for user addition with payment options
 */
export function showUserAdditionAlert(
  limitCheck: SubscriptionLimitCheck,
  onProceed: () => void,
  onPaymentRequired: () => void,
  onCancel?: () => void
): void {
  if (!limitCheck.canProceed) {
    Alert.alert(limitCheck.title, limitCheck.message, [
      { text: 'OK', onPress: onCancel }
    ]);
    return;
  }

  if (limitCheck.requiresPayment) {
    Alert.alert(
      limitCheck.title,
      limitCheck.message,
      [
        { text: 'Cancel', style: 'cancel', onPress: onCancel },
        { text: 'Proceed to Payment', onPress: onPaymentRequired }
      ]
    );
  } else {
    Alert.alert(
      limitCheck.title,
      limitCheck.message,
      [
        { text: 'Cancel', style: 'cancel', onPress: onCancel },
        { text: 'Add User', onPress: onProceed }
      ]
    );
  }
}

/**
 * Check if user can access premium features based on subscription
 */
export function checkPremiumFeatureAccess(
  subscription: any,
  featureName: string = 'this feature'
): { canAccess: boolean; message: string } {
  if (!subscription) {
    return {
      canAccess: false,
      message: `You need an active subscription to access ${featureName}.`
    };
  }

  if (subscription.status === 'cancelled' || subscription.status === 'expired') {
    return {
      canAccess: false,
      message: `Your subscription has been ${subscription.status}. Please reactivate to access ${featureName}.`
    };
  }

  if (subscription.status === 'trial') {
    return {
      canAccess: true,
      message: `You're accessing ${featureName} with your trial subscription.`
    };
  }

  return {
    canAccess: true,
    message: `You have access to ${featureName} with your ${subscription.plan_name || 'current'} subscription.`
  };
}

/**
 * Check if user can invite members to a site based on subscription limits
 */
export function checkSiteMemberInviteLimit(
  userLimits: UserLimits | null,
  currentSiteMembers: number,
  membersToInvite: number = 1
): SubscriptionLimitCheck {
  if (!userLimits) {
    return {
      canProceed: false,
      requiresPayment: false,
      paymentAmount: 0,
      message: 'Unable to check subscription limits. Please try again.',
      title: 'Error'
    };
  }

  // For site members, we need to consider the total subscription user limit
  const totalUsersAfterInvite = userLimits.totalUsers + membersToInvite;
  
  if (totalUsersAfterInvite <= userLimits.includedUsers) {
    return {
      canProceed: true,
      requiresPayment: false,
      paymentAmount: 0,
      message: `You can invite ${membersToInvite} member${membersToInvite > 1 ? 's' : ''} to this site.`,
      title: 'Invite Members'
    };
  }

  const additionalUsersNeeded = totalUsersAfterInvite - userLimits.includedUsers - userLimits.additionalUsers;
  const paymentAmount = Math.max(0, additionalUsersNeeded) * 249;

  if (paymentAmount > 0) {
    return {
      canProceed: true,
      requiresPayment: true,
      paymentAmount,
      message: `Inviting ${membersToInvite} member${membersToInvite > 1 ? 's' : ''} will require payment of ₹${paymentAmount} for additional subscription users.`,
      title: 'Additional Payment Required'
    };
  }

  return {
    canProceed: true,
    requiresPayment: false,
    paymentAmount: 0,
    message: `You can invite ${membersToInvite} member${membersToInvite > 1 ? 's' : ''} using your additional user allowance.`,
    title: 'Invite Members'
  };
}

/**
 * Get subscription status message for UI display
 */
export function getSubscriptionStatusMessage(subscription: any): {
  status: 'active' | 'trial' | 'cancelled' | 'expired' | 'none';
  message: string;
  color: string;
} {
  if (!subscription) {
    return {
      status: 'none',
      message: 'No active subscription',
      color: '#6b7280'
    };
  }

  switch (subscription.status) {
    case 'trial':
      const trialEnd = subscription.trial_end ? new Date(subscription.trial_end) : null;
      const daysLeft = trialEnd ? Math.ceil((trialEnd.getTime() - Date.now()) / (1000 * 60 * 60 * 24)) : 0;
      return {
        status: 'trial',
        message: `Trial active (${daysLeft} days left)`,
        color: '#f59e0b'
      };
    
    case 'active':
      return {
        status: 'active',
        message: 'Active subscription',
        color: '#10b981'
      };
    
    case 'cancelled':
      const accessUntil = subscription.current_period_end ? new Date(subscription.current_period_end) : null;
      return {
        status: 'cancelled',
        message: `Cancelled (access until ${accessUntil?.toLocaleDateString() || 'unknown'})`,
        color: '#ef4444'
      };
    
    case 'expired':
      return {
        status: 'expired',
        message: 'Subscription expired',
        color: '#ef4444'
      };
    
    default:
      return {
        status: 'none',
        message: 'Unknown subscription status',
        color: '#6b7280'
      };
  }
}

/**
 * Calculate the cost breakdown for subscription changes
 */
export function calculateSubscriptionCostBreakdown(
  currentLimits: UserLimits,
  newUserCount: number
): {
  currentCost: number;
  newCost: number;
  additionalCost: number;
  breakdown: string[];
} {
  const basePlanCost = 999; // Premium plan base cost
  const currentAdditionalCost = currentLimits.additionalCost;
  const currentCost = basePlanCost + currentAdditionalCost;

  const newTotalUsers = currentLimits.totalUsers + newUserCount;
  const newAdditionalUsers = Math.max(0, newTotalUsers - currentLimits.includedUsers);
  const newAdditionalCost = newAdditionalUsers * 249;
  const newCost = basePlanCost + newAdditionalCost;
  const additionalCost = newCost - currentCost;

  const breakdown = [
    `Base Premium Plan: ₹${basePlanCost}/month`,
    `Current additional users: ${currentLimits.additionalUsers} × ₹249 = ₹${currentAdditionalCost}/month`,
    `New additional users: ${newAdditionalUsers} × ₹249 = ₹${newAdditionalCost}/month`,
    `Additional monthly cost: ₹${additionalCost}/month`
  ];

  return {
    currentCost,
    newCost,
    additionalCost,
    breakdown
  };
}
