// Test script for the create-razorpay-order edge function
const fetch = require('node-fetch');

const testFunction = async () => {
  const url = 'https://vsnhscndlifvaptwdfsw.supabase.co/functions/v1/create-razorpay-order';
  const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Ej5zQJlmkU3KQOLnRnG7VJQGgLXsrh7jgOkBaQOBaQs';

  const testData = {
    amount: 24900,
    currency: 'INR',
    receipt: 'test_receipt_' + Date.now(),
    notes: {
      plan_id: 'standard',
      test: 'true'
    }
  };

  try {
    console.log('Testing edge function...');
    console.log('URL:', url);
    console.log('Test data:', testData);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${anon<PERSON>ey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const responseData = await response.text();
    console.log('Response body:', responseData);

    if (response.ok) {
      const jsonData = JSON.parse(responseData);
      console.log('✅ Success! Order ID:', jsonData.order?.id);
    } else {
      console.log('❌ Error response');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
};

testFunction();
