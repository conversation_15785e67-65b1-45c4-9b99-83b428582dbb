import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  StatusBar,
  Alert
} from 'react-native';
import { useLocalSearchParams, router, Stack } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { MaterialIcons, Ionicons, FontAwesome5 } from '@expo/vector-icons';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/lib/supabase';

const { width, height } = Dimensions.get('window');

interface OrderDetails {
  id: number;
  order_number: string;
  status: string;
  total_amount: number;
  delivery_address: string;
  delivery_time: string;
  created_at: string;
  estimated_delivery_time?: string;
}

export default function OrderConfirmation() {
  const params = useLocalSearchParams();
  const insets = useSafeAreaInsets();
  const { user } = useAuth();
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null);
  const [loading, setLoading] = useState(true);

  const primaryColor = '#f97316';
  const backgroundColor = '#fff';
  const textColor = '#000';
  const secondaryTextColor = '#666';

  useEffect(() => {
    if (params.orderId) {
      fetchOrderDetails();
    }
  }, [params.orderId]);

  const fetchOrderDetails = async () => {
    try {
      const { data, error } = await supabase
        .from('shop_orders')
        .select('*')
        .eq('id', params.orderId)
        .eq('user_id', user?.id)
        .single();

      if (error) {
        console.error('Error fetching order details:', error);
        Alert.alert('Error', 'Failed to load order details');
        return;
      }

      setOrderDetails(data);
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getEstimatedDeliveryTime = () => {
    if (orderDetails?.estimated_delivery_time) {
      return new Date(orderDetails.estimated_delivery_time).toLocaleTimeString('en-IN', {
        hour: '2-digit',
        minute: '2-digit'
      });
    }
    
    // Calculate estimated delivery time (30-45 minutes from order time)
    const orderTime = new Date(orderDetails?.created_at || Date.now());
    const estimatedTime = new Date(orderTime.getTime() + 35 * 60 * 1000); // 35 minutes
    return estimatedTime.toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getDeliveryMinutes = () => {
    const orderTime = new Date(orderDetails?.created_at || Date.now());
    const estimatedTime = new Date(orderTime.getTime() + 35 * 60 * 1000);
    const now = new Date();
    const diffMinutes = Math.max(0, Math.ceil((estimatedTime.getTime() - now.getTime()) / (1000 * 60)));
    return diffMinutes;
  };

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor, justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={[styles.loadingText, { color: textColor }]}>Loading order details...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <StatusBar backgroundColor={primaryColor} barStyle="light-content" />
      
      <Stack.Screen
        options={{
          headerShown: false
        }}
      />

      {/* Header */}
      <View style={[styles.header, { backgroundColor: primaryColor, paddingTop: insets.top + 10 }]}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.replace('/(main)/shop')}
        >
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Order confirmed</Text>
        <View style={styles.headerRight} />
      </View>

      {/* Delivery Status */}
      <View style={[styles.statusContainer, { backgroundColor: primaryColor }]}>
        <Text style={styles.statusText}>Arriving in {getDeliveryMinutes()} minutes</Text>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Map Section */}
        <View style={styles.mapContainer}>
          <View style={styles.mapPlaceholder}>
            <MaterialIcons name="location-on" size={40} color={primaryColor} />
            <Text style={[styles.mapText, { color: secondaryTextColor }]}>
              Tracking your order...
            </Text>
          </View>
          
          {/* Delivery Route Indicator */}
          <View style={styles.routeIndicator}>
            <View style={styles.routeDots}>
              <View style={[styles.dot, { backgroundColor: primaryColor }]} />
              <View style={[styles.dotLine, { backgroundColor: '#ddd' }]} />
              <View style={[styles.dot, { backgroundColor: '#ddd' }]} />
            </View>
          </View>
        </View>

        {/* Delivery Partner Info */}
        <View style={[styles.deliveryPartnerContainer, { backgroundColor }]}>
          <View style={styles.deliveryPartnerInfo}>
            <View style={styles.partnerAvatar}>
              <FontAwesome5 name="user" size={20} color={primaryColor} />
            </View>
            <View style={styles.partnerDetails}>
              <Text style={[styles.partnerName, { color: textColor }]}>
                We'll assign a delivery partner once your order is packed
              </Text>
              <Text style={[styles.partnerMessage, { color: secondaryTextColor }]}>
                I have picked up your order, and I am on the way to your location
              </Text>
            </View>
            <TouchableOpacity style={styles.callButton}>
              <MaterialIcons name="phone" size={20} color={primaryColor} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Order Summary */}
        <View style={[styles.orderSummaryContainer, { backgroundColor }]}>
          <Text style={[styles.sectionTitle, { color: textColor }]}>
            Delivering happiness at your doorstep!
          </Text>
          <Text style={[styles.thankYouText, { color: secondaryTextColor }]}>
            Thank them by leaving a tip
          </Text>
          
          {/* Tip Options */}
          <View style={styles.tipContainer}>
            <TouchableOpacity style={[styles.tipButton, { borderColor: '#ddd' }]}>
              <FontAwesome5 name="rupee-sign" size={12} color={textColor} />
              <Text style={[styles.tipAmount, { color: textColor }]}>20</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.tipButton, { borderColor: primaryColor, backgroundColor: primaryColor }]}>
              <FontAwesome5 name="rupee-sign" size={12} color="#fff" />
              <Text style={[styles.tipAmount, { color: '#fff' }]}>30</Text>
              <Text style={[styles.tipLabel, { color: '#fff' }]}>MOST TIPPED</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.tipButton, { borderColor: '#ddd' }]}>
              <FontAwesome5 name="rupee-sign" size={12} color={textColor} />
              <Text style={[styles.tipAmount, { color: textColor }]}>50</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.tipButton, { borderColor: '#ddd' }]}>
              <Text style={[styles.tipOther, { color: textColor }]}>Other</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Store Info */}
        <View style={[styles.storeInfoContainer, { backgroundColor }]}>
          <View style={styles.storeInfo}>
            <MaterialIcons name="store" size={20} color={primaryColor} />
            <Text style={[styles.storeText, { color: textColor }]}>
              Your Infratask store is only 0.5 km away
            </Text>
          </View>
          <TouchableOpacity>
            <Text style={[styles.learnMore, { color: primaryColor }]}>
              Learn about delivery partner safety →
            </Text>
          </TouchableOpacity>
        </View>

        {/* Order Details */}
        <View style={[styles.orderDetailsContainer, { backgroundColor }]}>
          <Text style={[styles.orderNumber, { color: textColor }]}>
            Order #{orderDetails?.order_number}
          </Text>
          <Text style={[styles.orderAmount, { color: textColor }]}>
            Total: ₹{orderDetails?.total_amount}
          </Text>
          <Text style={[styles.deliveryAddress, { color: secondaryTextColor }]}>
            Delivering to: {orderDetails?.delivery_address}
          </Text>
        </View>
      </ScrollView>

      {/* Bottom Payment Section */}
      <View style={[styles.bottomContainer, { backgroundColor }]}>
        <Text style={[styles.paymentText, { color: textColor }]}>
          Pay ₹{orderDetails?.total_amount} before or on delivery
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  backButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
  },
  headerRight: {
    width: 32,
  },
  statusContainer: {
    paddingVertical: 12,
    alignItems: 'center',
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  scrollView: {
    flex: 1,
  },
  mapContainer: {
    height: 200,
    backgroundColor: '#f5f5f5',
    position: 'relative',
  },
  mapPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapText: {
    marginTop: 8,
    fontSize: 14,
  },
  routeIndicator: {
    position: 'absolute',
    left: 20,
    top: 20,
  },
  routeDots: {
    alignItems: 'center',
  },
  dot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  dotLine: {
    width: 2,
    height: 30,
    marginVertical: 4,
  },
  deliveryPartnerContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  deliveryPartnerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  partnerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  partnerDetails: {
    flex: 1,
  },
  partnerName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  partnerMessage: {
    fontSize: 12,
    lineHeight: 16,
  },
  callButton: {
    padding: 8,
  },
  orderSummaryContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  thankYouText: {
    fontSize: 12,
    marginBottom: 16,
  },
  tipContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  tipButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginHorizontal: 4,
    borderWidth: 1,
    borderRadius: 8,
    position: 'relative',
  },
  tipAmount: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  tipLabel: {
    position: 'absolute',
    bottom: -2,
    fontSize: 8,
    fontWeight: '500',
  },
  tipOther: {
    fontSize: 14,
    fontWeight: '500',
  },
  storeInfoContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  storeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  storeText: {
    fontSize: 14,
    marginLeft: 8,
  },
  learnMore: {
    fontSize: 12,
    fontWeight: '500',
  },
  orderDetailsContainer: {
    padding: 16,
  },
  orderNumber: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  orderAmount: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  deliveryAddress: {
    fontSize: 12,
    lineHeight: 16,
  },
  bottomContainer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    alignItems: 'center',
  },
  paymentText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
