import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { supabase } from '@/lib/supabase';
import { Feather, MaterialIcons } from '@expo/vector-icons';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useCallback, useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Image,
    RefreshControl,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    useColorScheme,
    View,
} from 'react-native';

// Interface for database SQL response
interface MemberSqlResponse {
  id: string;
  user_id: string;
  role: 'Super Admin' | 'Admin' | 'Member';
  category: string | null;
  joined_at: string;
  full_name: string;
  profile_image_url: string | null;
  phone_number: string | null;
}

// Type for our processed member data
type MemberProfile = {
  id: string;
  user_id: string;
  role: 'Super Admin' | 'Admin' | 'Member';
  category: string | null;
  joined_at: string;
  profile: {
    full_name: string;
    profile_image_url: string | null;
    phone_number: string | null;
  };
};

type SiteDetails = {
  id: string;
  name: string;
  owner_id: string;
};

export default function TeamMembersScreen() {
  const params = useLocalSearchParams();
  console.log('Raw URL params:', JSON.stringify(params));
  const siteId = typeof params.id === 'string' ? params.id : Array.isArray(params.id) ? params.id[0] : '';
  console.log('Extracted site ID:', siteId);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [members, setMembers] = useState<MemberProfile[]>([]);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [isOwner, setIsOwner] = useState(false);
  const [siteDetails, setSiteDetails] = useState<SiteDetails | null>(null);
  const [userId, setUserId] = useState<string | null>(null);
  const colorScheme = useColorScheme();

  // Fetch team members function
  const fetchTeamMembersData = async (isRefreshing = false) => {
    if (!siteId) {
      console.error('No site ID provided');
      Alert.alert('Error', 'No site ID provided');
      router.back();
      return;
    }
    
    try {
      if (!isRefreshing) {
        setLoading(true);
      }
      
      console.log('Fetching team members for site ID:', siteId);
      
      // Get current user
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) {
        console.log('No authenticated user found');
        return;
      }
      
      console.log('Current user ID:', userData.user.id);
      setUserId(userData.user.id);
      
      // Get user profile
      const { data: profileData } = await supabase
        .from('profiles')
        .select('id')
        .eq('user_id', userData.user.id)
        .single();
      
      if (!profileData) {
        console.log('No profile found for user');
        return;
      }
      
      console.log('User profile ID:', profileData.id);
      
      // Get site details using Supabase
      const { data: siteData, error: siteError } = await supabase
        .from('sites')
        .select('id, name, owner_id')
        .eq('id', siteId)
        .single();
      
      if (siteError) {
        console.error('Error fetching site:', siteError);
        Alert.alert('Error', 'Failed to load site details');
        router.back();
        return;
      }
      
      if (!siteData) {
        console.log('No site data found for ID:', siteId);
        Alert.alert('Error', 'Site not found');
        router.back();
        return;
      }
      
      console.log('Site data:', siteData);
      setSiteDetails(siteData);
      setIsOwner(siteData.owner_id === profileData.id);
      
      // Try a different query approach for members
      console.log('Fetching site members...');
      const { data: membersData, error: membersError } = await supabase
        .from('site_members')
        .select(`
          id,
          user_id,
          role,
          category,
          joined_at
        `)
        .eq('site_id', siteId);
      
      if (membersError) {
        console.error('Error fetching members:', membersError);
        Alert.alert('Error', 'Failed to load team members');
        return;
      }
      
      console.log('Members data raw:', JSON.stringify(membersData));
      
      if (membersData && membersData.length > 0) {
        // Get profiles for the members
        const userIds = membersData.map(member => member.user_id);
        const { data: profilesData, error: profilesError } = await supabase
          .from('profiles')
          .select('user_id, full_name, profile_image_url, phone_number')
          .in('user_id', userIds);
          
        if (profilesError) {
          console.error('Error fetching profiles:', profilesError);
          Alert.alert('Error', 'Failed to load team member profiles');
          return;
        }
        
        console.log('Profiles data:', JSON.stringify(profilesData));
        
        // Transform data to match expected type
        const formattedMembers = membersData.map((member: any) => {
          const profile = profilesData?.find(p => p.user_id === member.user_id);
          
          const formattedMember = {
            id: member.id,
            user_id: member.user_id,
            role: member.role,
            category: member.category,
            joined_at: member.joined_at,
            profile: {
              full_name: profile?.full_name || 'Unknown',
              profile_image_url: profile?.profile_image_url || null,
              phone_number: profile?.phone_number || null
            }
          };
          console.log('Formatted member:', formattedMember);
          return formattedMember;
        });
        
        console.log('Total members after formatting:', formattedMembers.length);
        setMembers(formattedMembers);
        
        // Find current user's role
        const userMember = formattedMembers.find((m: MemberProfile) => m.user_id === userData.user.id);
        if (userMember) {
          console.log('Current user role:', userMember.role);
          setUserRole(userMember.role);
        } else {
          console.log('Current user not found in team members');
        }
      } else {
        console.log('No members found for this site');
        setMembers([]);
      }
    } catch (error) {
      console.error('Error fetching team members:', error);
      Alert.alert('Error', 'Failed to load team members');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Handle pull-to-refresh
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchTeamMembersData(true);
  }, []);

  // Fetch team members on component mount
  useEffect(() => {
    console.log('Team members screen mounted with site ID:', siteId);
    if (siteId) {
      fetchTeamMembersData();
    } else {
      console.error('No site ID available in useEffect');
      setLoading(false);
    }
  }, [siteId]);

  // Delete team member
  const handleDeleteMember = async (memberId: string, memberUserId: string) => {
    // Check permissions - only Super Admin can delete anyone, Admin can delete Members
    const memberToDelete = members.find(m => m.id === memberId);
    
    if (!memberToDelete) return;
    
    // Super Admin (site creator) cannot be deleted
    if (memberToDelete.role === 'Super Admin') {
      Alert.alert('Cannot Delete', 'The Super Admin (site creator) cannot be removed from the site.');
      return;
    }
    
    // Check if current user has permission to delete this member
    if (userRole === 'Admin' && memberToDelete.role === 'Admin') {
      Alert.alert('Permission Denied', 'Admins cannot remove other Admins.');
      return;
    }
    
    if (userRole === 'Member') {
      Alert.alert('Permission Denied', 'Members cannot manage the team.');
      return;
    }
    
    Alert.alert(
      'Confirm Delete',
      `Are you sure you want to remove ${memberToDelete.profile.full_name} from the team?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              
              // Delete using Supabase
              const { error } = await supabase
                .from('site_members')
                .delete()
                .eq('id', memberId);
              
              if (error) {
                console.error('Error deleting member:', error);
                Alert.alert('Error', 'Failed to remove team member');
                return;
              }
              
              // Update local state
              setMembers(members.filter(m => m.id !== memberId));
              Alert.alert('Success', 'Team member removed successfully');
            } catch (error) {
              console.error('Error:', error);
              Alert.alert('Error', 'Failed to remove team member');
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  // Render a team member item
  const renderMemberItem = (member: MemberProfile, index: number) => {
    if (!member || typeof member !== 'object') {
      console.error('Invalid member data at index', index, member);
      return null;
    }
    
    const isSuperAdmin = member.role === 'Super Admin';
    const defaultAvatar = 'https://www.gravatar.com/avatar/00000000000000000000000000000000?d=mp&f=y';
    
    console.log(`Rendering member ${index}:`, member.id, member.profile?.full_name);
    
    // Ensure profile exists
    const profile = member.profile || { full_name: 'Unknown', profile_image_url: null, phone_number: null };
    
    return (
      <View key={`member-${member.id || index}`} style={styles.memberCard}>
        <Image
          source={{ uri: profile.profile_image_url || defaultAvatar }}
          style={styles.profileImage}
        />
        
        <View style={styles.memberInfo}>
          <ThemedText style={styles.memberName}>{profile.full_name}</ThemedText>
          
          <View style={styles.memberDetails}>
            <View style={[
              styles.roleBadge,
              {
                backgroundColor: 
                  member.role === 'Super Admin' ? '#fee2e2' : 
                  member.role === 'Admin' ? '#e0f2fe' : '#f3f4f6'
              }
            ]}>
              <ThemedText style={[
                styles.roleText,
                {
                  color: 
                    member.role === 'Super Admin' ? '#b91c1c' : 
                    member.role === 'Admin' ? '#0369a1' : '#4b5563'
                }
              ]}>
                {member.role || 'Member'}
              </ThemedText>
            </View>
            
            {member.category && (
              <View style={styles.categoryBadge}>
                <ThemedText style={styles.categoryText}>{member.category}</ThemedText>
              </View>
            )}
          </View>
          
          {profile.phone_number && (
            <View style={styles.contactInfo}>
              <Feather name="phone" size={12} color="#64748b" style={styles.contactIcon} />
              <ThemedText style={styles.contactText}>{profile.phone_number}</ThemedText>
            </View>
          )}
        </View>
        
        {/* Only show delete icon if:
            1. Current user is Super Admin, or
            2. Current user is Admin and this member is not an Admin or Super Admin
            3. The member is not a Super Admin (site creator)
        */}
        {((userRole === 'Super Admin' || (userRole === 'Admin' && member.role === 'Member')) && 
          !isSuperAdmin && member.user_id !== userId) && (
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={() => handleDeleteMember(member.id, member.user_id)}
            accessibilityLabel={`Remove ${member.profile.full_name}`}
          >
            <Feather name="trash-2" size={20} color="#ef4444" />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  // Handle navigation to add member screen
  const navigateToAddMember = () => {
    router.push(`/site/${siteId}/add-member`);
  };

  // Calculate team statistics
  const getTeamStats = () => {
    if (!members.length) return null;
    
    const superAdminCount = members.filter(m => m.role === 'Super Admin').length;
    const adminCount = members.filter(m => m.role === 'Admin').length;
    const memberCount = members.filter(m => m.role === 'Member').length;
    
    return { superAdminCount, adminCount, memberCount };
  };

  // Render team summary
  const renderTeamSummary = () => {
    const stats = getTeamStats();
    if (!stats) return null;
    
    return (
      <View style={styles.summaryContainer}>
        <View style={styles.summaryItem}>
          <View style={[styles.summaryIcon, { backgroundColor: 'rgba(185, 28, 28, 0.1)' }]}>
            <Feather name="shield" size={16} color="#b91c1c" />
          </View>
          <View>
            <ThemedText style={styles.summaryCount}>{stats.superAdminCount}</ThemedText>
            <ThemedText style={styles.summaryLabel}>Super Admin</ThemedText>
          </View>
        </View>
        
        <View style={styles.summaryDivider} />
        
        <View style={styles.summaryItem}>
          <View style={[styles.summaryIcon, { backgroundColor: 'rgba(3, 105, 161, 0.1)' }]}>
            <Feather name="user-check" size={16} color="#0369a1" />
          </View>
          <View>
            <ThemedText style={styles.summaryCount}>{stats.adminCount}</ThemedText>
            <ThemedText style={styles.summaryLabel}>Admins</ThemedText>
          </View>
        </View>
        
        <View style={styles.summaryDivider} />
        
        <View style={styles.summaryItem}>
          <View style={[styles.summaryIcon, { backgroundColor: 'rgba(75, 85, 99, 0.1)' }]}>
            <Feather name="users" size={16} color="#4b5563" />
          </View>
          <View>
            <ThemedText style={styles.summaryCount}>{stats.memberCount}</ThemedText>
            <ThemedText style={styles.summaryLabel}>Members</ThemedText>
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#f97316" />
      </ThemedView>
    );
  }

  // Debug logging
  console.log('Render state - members count:', members.length);
  console.log('Render state - userRole:', userRole);
  console.log('Render state - members data:', JSON.stringify(members));
  
  // Check for rendering conditions
  const canRenderMembers = Array.isArray(members) && members.length > 0;

  return (
    <ThemedView style={styles.container}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      
      <Stack.Screen
        options={{
          title: 'Team Members',
          headerRight: () => (
            (userRole === 'Super Admin' || userRole === 'Admin') ? (
              <TouchableOpacity
                style={styles.addButton}
                onPress={navigateToAddMember}
              >
                <Feather name="plus" size={18} color="#fff" />
                <ThemedText style={styles.addButtonText}>Add</ThemedText>
              </TouchableOpacity>
            ) : null
          ),
        }}
      />
      
      <ScrollView
        style={styles.container}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#f97316']}
            tintColor={colorScheme === 'dark' ? '#f97316' : undefined}
            progressBackgroundColor={colorScheme === 'dark' ? '#262626' : undefined}
          />
        }
      >
        {canRenderMembers ? (
          <>
            <View style={styles.headerRow}>
              <ThemedText style={styles.headerText}>Team Members • {members.length}</ThemedText>
            </View>
            
            {renderTeamSummary()}
            
            <View style={[styles.membersContainer, { marginTop: 16 }]}>
              {members.map((member, index) => renderMemberItem(member, index))}
            </View>
          </>
        ) : (
          <View style={styles.emptyContainer}>
            <View style={styles.emptyIconContainer}>
              <MaterialIcons name="groups" size={48} color="#94a3b8" />
            </View>
            <ThemedText style={styles.emptyTitle}>No Team Members</ThemedText>
            <ThemedText style={styles.emptyText}>
              {userRole === 'Member' 
                ? "There are no team members added to this site yet." 
                : "Add team members to collaborate on this site."}
            </ThemedText>
            
            {(userRole === 'Super Admin' || userRole === 'Admin') && (
              <TouchableOpacity
                style={styles.emptyAddButton}
                onPress={navigateToAddMember}
              >
                <Feather name="plus" size={16} color="#fff" />
                <ThemedText style={styles.emptyAddButtonText}>Add Team Member</ThemedText>
              </TouchableOpacity>
            )}
          </View>
        )}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  membersContainer: {
    gap: 12,
  },
  memberCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    backgroundColor: 'rgba(100, 100, 100, 0.06)',
  },
  profileImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
    backgroundColor: '#f1f5f9',
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  memberDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  roleBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    backgroundColor: '#f8fafc',
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#64748b',
  },
  contactInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  contactIcon: {
    marginRight: 4,
  },
  contactText: {
    fontSize: 12,
    color: '#64748b',
  },
  deleteButton: {
    padding: 10,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f97316',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    gap: 4,
  },
  addButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 80,
  },
  emptyIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(100, 100, 100, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  emptyText: {
    textAlign: 'center',
    marginBottom: 20,
    opacity: 0.7,
    maxWidth: '80%',
  },
  emptyAddButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f97316',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  emptyAddButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    backgroundColor: 'rgba(100, 100, 100, 0.06)',
  },
  summaryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  summaryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  summaryCount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  summaryLabel: {
    fontSize: 12,
    fontWeight: '500',
  },
  summaryDivider: {
    width: 1,
    height: '100%',
    backgroundColor: 'rgba(100, 100, 100, 0.1)',
  },
}); 