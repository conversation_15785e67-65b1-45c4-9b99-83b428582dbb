import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { UserLimits } from '@/hooks/useSubscription';
import { checkUserAdditionLimit } from '@/utils/subscription-limits';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Modal,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';

export interface AddUserModalProps {
  visible: boolean;
  onClose: () => void;
  onAddUser: (phone: string, requiresPayment: boolean, paymentAmount: number) => Promise<void>;
  userLimits: UserLimits | null;
  loading?: boolean;
}

const AddUserModal: React.FC<AddUserModalProps> = ({
  visible,
  onClose,
  onAddUser,
  userLimits,
  loading = false
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [phone, setPhone] = useState('');
  const [isValidPhone, setIsValidPhone] = useState(false);

  const validatePhone = (phone: string) => {
    const cleanPhone = phone.replace(/\s/g, '');
    // Check for 10-digit Indian number or 91 followed by 10 digits (database format)
    const indianPhoneRegex = /^(91)?[6-9]\d{9}$/;
    return indianPhoneRegex.test(cleanPhone);
  };

  const formatPhoneForDisplay = (text: string) => {
    // Remove all non-digits
    let cleaned = text.replace(/[^\d]/g, '');

    // If it starts with 91, keep it as is
    if (cleaned.startsWith('91') && cleaned.length <= 12) {
      return cleaned;
    }

    // If it's a 10-digit number starting with 6-9, it's a valid Indian mobile
    if (/^[6-9]\d{0,9}$/.test(cleaned)) {
      return cleaned;
    }

    return cleaned;
  };

  const handlePhoneChange = (text: string) => {
    const formattedText = formatPhoneForDisplay(text);
    setPhone(formattedText);
    setIsValidPhone(validatePhone(formattedText));
  };

  const getFullPhoneNumber = () => {
    const cleanPhone = phone.replace(/\s/g, '');

    // If already has 91 prefix, return as is
    if (cleanPhone.startsWith('91') && cleanPhone.length === 12) {
      return cleanPhone;
    }

    // If it's a 10-digit number, add 91 prefix (database format)
    if (/^[6-9]\d{9}$/.test(cleanPhone)) {
      return `91${cleanPhone}`;
    }

    return cleanPhone;
  };

  const handleAddUser = async () => {
    if (!phone.trim() || !isValidPhone) {
      Alert.alert('Invalid Phone Number', 'Please enter a valid phone number.');
      return;
    }

    if (!userLimits) {
      Alert.alert('Error', 'Unable to check subscription limits. Please try again.');
      return;
    }

    // Check if payment is required
    const limitCheck = checkUserAdditionLimit(userLimits, 1);

    if (!limitCheck.canProceed) {
      Alert.alert(limitCheck.title, limitCheck.message);
      return;
    }

    try {
      const fullPhoneNumber = getFullPhoneNumber();
      console.log('AddUserModal: Adding user with phone:', fullPhoneNumber);
      await onAddUser(fullPhoneNumber, limitCheck.requiresPayment, limitCheck.paymentAmount);
      setPhone('');
      setIsValidPhone(false);
    } catch (error) {
      console.error('Error adding user:', error);
    }
  };

  const handleClose = () => {
    setPhone('');
    setIsValidPhone(false);
    onClose();
  };

  const getPaymentInfo = () => {
    if (!userLimits) return null;
    
    const limitCheck = checkUserAdditionLimit(userLimits, 1);
    return limitCheck;
  };

  const paymentInfo = getPaymentInfo();

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={handleClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.background }]}>
          {/* Header */}
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              Add User
            </Text>
            <TouchableOpacity
              onPress={handleClose}
              style={styles.modalCloseButton}
            >
              <MaterialCommunityIcons name="close" size={24} color={colors.icon} />
            </TouchableOpacity>
          </View>

          {/* Description */}
          <Text style={[styles.modalDescription, { color: colors.icon }]}>
            Enter the 10-digit mobile number of the user you want to add. They must already have an account in the app.
          </Text>

          {/* Payment Info Banner */}
          {paymentInfo?.requiresPayment && (
            <View style={[styles.paymentBanner, { backgroundColor: '#f97316' + '15', borderColor: '#f97316' + '30' }]}>
              <MaterialCommunityIcons name="currency-inr" size={20} color="#f97316" />
              <Text style={[styles.paymentText, { color: '#f97316' }]}>
                Adding this user will require payment of ₹{paymentInfo.paymentAmount}
              </Text>
            </View>
          )}

          {/* Phone Input */}
          <View style={styles.inputContainer}>
            <Text style={[styles.inputLabel, { color: colors.text }]}>
              Phone Number
            </Text>
            <View style={[
              styles.textInputContainer,
              {
                borderColor: isValidPhone ? colors.primary : colors.icon,
                backgroundColor: colors.background
              }
            ]}>
              <TextInput
                style={[styles.textInput, { color: colors.text }]}
                value={phone}
                onChangeText={handlePhoneChange}
                placeholder="Enter 10-digit mobile number"
                placeholderTextColor={colors.icon}
                keyboardType="phone-pad"
                autoCapitalize="none"
                autoCorrect={false}
                editable={!loading}
              />
              {isValidPhone && (
                <MaterialCommunityIcons
                  name="check-circle"
                  size={20}
                  color={colors.primary}
                />
              )}
            </View>
          </View>

          {/* User Limits Info */}
          {userLimits && (
            <View style={[styles.limitsInfo, { backgroundColor: colors.icon + '10' }]}>
              <Text style={[styles.limitsText, { color: colors.icon }]}>
                Current usage: {userLimits.totalUsers} of {userLimits.includedUsers} included users
              </Text>
              {userLimits.additionalUsers > 0 && (
                <Text style={[styles.limitsText, { color: '#f97316' }]}>
                  Additional users: {userLimits.additionalUsers} (₹{userLimits.additionalCost}/month)
                </Text>
              )}
            </View>
          )}

          {/* Actions */}
          <View style={styles.modalActions}>
            <TouchableOpacity
              style={[styles.modalButton, styles.modalCancelButton, { borderColor: colors.icon }]}
              onPress={handleClose}
              disabled={loading}
            >
              <Text style={[styles.modalButtonText, { color: colors.icon }]}>
                Cancel
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.modalButton,
                styles.modalAddButton,
                {
                  backgroundColor: isValidPhone && !loading ? colors.primary : colors.icon,
                  opacity: isValidPhone && !loading ? 1 : 0.5
                }
              ]}
              onPress={handleAddUser}
              disabled={!isValidPhone || loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Text style={[styles.modalButtonText, { color: 'white' }]}>
                  {paymentInfo?.requiresPayment ? 'Add User (Payment Required)' : 'Add User'}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 16,
    padding: 24,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  modalCloseButton: {
    padding: 4,
  },
  modalDescription: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 16,
  },
  paymentBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 16,
  },
  paymentText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  textInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  textInput: {
    fontSize: 16,
    flex: 1,
    minHeight: 20,
  },
  limitsInfo: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 24,
  },
  limitsText: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  modalActions: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  modalCancelButton: {
    borderWidth: 1,
  },
  modalAddButton: {
    // backgroundColor will be set dynamically
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default AddUserModal;
