-- Fix link_transactions_safely function security
-- This migration improves the security of the SECURITY DEFINER function

-- Drop the existing function
DROP FUNCTION IF EXISTS public.link_transactions_safely(UUID, UUID);

-- Recreate with improved security checks
CREATE OR REPLACE FUNCTION public.link_transactions_safely(transaction1_id UUID, transaction2_id UUID)
RETURNS VOID AS $$
DECLARE
    user_has_access_1 BOOLEAN;
    user_has_access_2 BOOLEAN;
BEGIN
    -- Check if the current user has access to both transactions through RLS
    SELECT EXISTS (
        SELECT 1 FROM transactions 
        WHERE id = transaction1_id 
        AND site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    ) INTO user_has_access_1;
    
    SELECT EXISTS (
        SELECT 1 FROM transactions 
        WHERE id = transaction2_id 
        AND site_id IN (
            SELECT site_id FROM site_members WHERE user_id = auth.uid()
        )
    ) INTO user_has_access_2;
    
    -- Only proceed if user has access to both transactions
    IF NOT (user_has_access_1 AND user_has_access_2) THEN
        RAISE EXCEPTION 'Access denied: You do not have permission to link these transactions';
    END IF;
    
    -- Perform the linking operation
    UPDATE transactions
    SET linked_transaction_id = transaction2_id,
        updated_at = NOW()
    WHERE id = transaction1_id;
    
    -- Link the reverse relationship if not already linked
    IF (SELECT linked_transaction_id FROM transactions WHERE id = transaction2_id) IS NULL THEN
        UPDATE transactions
        SET linked_transaction_id = transaction1_id,
            updated_at = NOW()
        WHERE id = transaction2_id;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.link_transactions_safely(UUID, UUID) TO authenticated;

-- Add function comment
COMMENT ON FUNCTION public.link_transactions_safely(UUID, UUID) IS 'Safely links two transactions with proper access control checks. Requires user to have access to both transactions.';

-- Create a wrapper function that doesn't use SECURITY DEFINER for additional safety
CREATE OR REPLACE FUNCTION public.link_transactions(transaction1_id UUID, transaction2_id UUID)
RETURNS VOID AS $$
BEGIN
    -- This function relies on RLS policies for security
    -- Call the secure function which has additional checks
    PERFORM link_transactions_safely(transaction1_id, transaction2_id);
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to authenticated users for the wrapper
GRANT EXECUTE ON FUNCTION public.link_transactions(UUID, UUID) TO authenticated;

COMMENT ON FUNCTION public.link_transactions(UUID, UUID) IS 'Public wrapper for linking transactions. Uses RLS for security.';

-- Create function to safely delete transactions and their linked counterparts
CREATE OR REPLACE FUNCTION public.delete_transaction_with_linked(
    transaction_id UUID,
    requesting_user_id UUID
)
RETURNS VOID AS $$
DECLARE
    target_transaction RECORD;
    linked_transaction_id UUID;
    user_owns_transaction BOOLEAN;
BEGIN
    -- Get the target transaction details
    SELECT * INTO target_transaction
    FROM transactions
    WHERE id = transaction_id;

    -- Check if transaction exists
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Transaction not found';
    END IF;

    -- Check if the requesting user owns this transaction
    SELECT (target_transaction.user_id = requesting_user_id) INTO user_owns_transaction;

    IF NOT user_owns_transaction THEN
        RAISE EXCEPTION 'Access denied: You can only delete transactions that you created';
    END IF;

    -- Get the linked transaction ID if it exists
    linked_transaction_id := target_transaction.linked_transaction_id;

    -- Delete the original transaction
    DELETE FROM transactions WHERE id = transaction_id;

    -- If there's a linked transaction, delete it too
    IF linked_transaction_id IS NOT NULL THEN
        DELETE FROM transactions WHERE id = linked_transaction_id;
    END IF;

    -- Also delete any transactions that link back to this one
    DELETE FROM transactions WHERE linked_transaction_id = transaction_id;

END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.delete_transaction_with_linked(UUID, UUID) TO authenticated;

-- Add function comment
COMMENT ON FUNCTION public.delete_transaction_with_linked(UUID, UUID) IS 'Safely deletes a transaction and its linked counterpart. Only the transaction owner can delete their transactions.';