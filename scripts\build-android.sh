#!/bin/bash

echo "🚀 Building Android APK for Infratask with Razorpay integration"
echo "=================================================="

# Check if required tools are installed
echo "📋 Checking prerequisites..."

# Check Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed"
    exit 1
fi

# Check npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed"
    exit 1
fi

# Check EAS CLI
if ! command -v eas &> /dev/null; then
    echo "⚠️  EAS CLI not found. Installing..."
    npm install -g @expo/eas-cli
fi

echo "✅ Prerequisites check passed"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Clear Expo cache
echo "🧹 Clearing Expo cache..."
npx expo install --fix

# Run prebuild to generate native directories
echo "🔧 Running prebuild..."
npx expo prebuild --platform android --clean

# Build with EAS
echo "🏗️  Building APK with EAS..."
echo "Note: This will use the 'preview' profile to generate an APK"
eas build --platform android --profile preview

echo "✅ Build process completed!"
echo ""
echo "📱 Once the build is complete:"
echo "1. Download the APK from the EAS dashboard"
echo "2. Install on a real Android device (not emulator for payment testing)"
echo "3. Test the payment flow with test cards"
echo ""
echo "🔍 For debugging payment issues:"
echo "1. Connect device via USB"
echo "2. Run: adb logcat | grep -i razorpay"
echo "3. Check logs during payment attempts" 