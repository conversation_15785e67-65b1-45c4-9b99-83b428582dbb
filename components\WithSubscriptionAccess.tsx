import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useSubscriptionAccess } from '@/hooks/useSubscriptionAccess';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, StyleSheet, View } from 'react-native';
import { SubscriptionPopup } from './SubscriptionPopup';
import { ThemedView } from './ThemedView';

interface WithSubscriptionAccessProps {
  feature: 'Sites' | 'Transactions';
  children: React.ReactNode;
}

export const WithSubscriptionAccess: React.FC<WithSubscriptionAccessProps> = ({
  feature,
  children,
}) => {
  const { isSubscribed, loading } = useSubscriptionAccess();
  const [showPopup, setShowPopup] = useState(false);
  const [hasCheckedAccess, setHasCheckedAccess] = useState(false);
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  useEffect(() => {
    if (!loading && !hasCheckedAccess) {
      setHasCheckedAccess(true);
      
      // If user is not subscribed, show popup after a brief delay
      if (!isSubscribed()) {
        const timer = setTimeout(() => {
          setShowPopup(true);
        }, 500);

        return () => clearTimeout(timer);
      }
    }
  }, [loading, isSubscribed, hasCheckedAccess]);

  // Show loading while checking subscription status
  if (loading || !hasCheckedAccess) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </ThemedView>
    );
  }

  return (
    <View style={styles.container}>
      {children}
      
      <SubscriptionPopup
        visible={showPopup}
        onClose={() => setShowPopup(false)}
        feature={feature}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
}); 