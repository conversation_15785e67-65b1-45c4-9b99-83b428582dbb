import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    Alert,
    Pressable,
    StyleSheet,
    Text,
    View,
} from 'react-native';

export function QuickUpdateTest() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [isChecking, setIsChecking] = useState(false);

  const colors = {
    background: isDark ? '#1a1a1a' : '#ffffff',
    text: isDark ? '#ffffff' : '#000000',
    primary: '#f97316',
    border: isDark ? '#333333' : '#e5e7eb',
  };

  const testUpdates = async () => {
    setIsChecking(true);
    try {
      // Dynamic import to avoid issues if module isn't available
      const { comprehensiveUpdateManager } = await import('@/lib/comprehensiveUpdateManager');

      // In development mode, enable a mock update first
      if (__DEV__) {
        try {
          const { developmentUpdateManager } = await import('@/lib/developmentUpdateManager');
          developmentUpdateManager.enableMockUpdate('flexible');
        } catch (error) {
          console.error('Failed to load development update manager:', error);
        }
      }

      // Initialize
      const initialized = await comprehensiveUpdateManager.initialize();
      if (!initialized) {
        Alert.alert('Error', 'Failed to initialize update manager');
        return;
      }

      // Check for updates
      const updateInfo = await comprehensiveUpdateManager.checkForUpdates(true);

      if (updateInfo) {
        Alert.alert(
          'Update Found!',
          `Version: ${updateInfo.version || updateInfo.versionCode}\nType: ${updateInfo.updateType}\nSource: ${updateInfo.source}${__DEV__ ? '\n\n🔧 Development Mode' : ''}`,
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Update',
              onPress: () => comprehensiveUpdateManager.startUpdateFlow(updateInfo)
            }
          ]
        );
      } else {
        Alert.alert('No Updates', __DEV__ ? 'No mock updates enabled.\n\n🔧 Development Mode' : 'Your app is up to date!');
      }
    } catch (error) {
      console.error('Update test error:', error);
      Alert.alert('Error', `Update test failed: ${error.message}`);
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background, borderColor: colors.border }]}>
      <Text style={[styles.title, { color: colors.text }]}>
        🔄 Update Test
      </Text>
      <Pressable
        style={[styles.button, { backgroundColor: colors.primary }]}
        onPress={testUpdates}
        disabled={isChecking}
      >
        <Ionicons 
          name={isChecking ? "hourglass" : "refresh"} 
          size={16} 
          color="#ffffff" 
        />
        <Text style={styles.buttonText}>
          {isChecking ? 'Checking...' : 'Test Updates'}
        </Text>
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    margin: 8,
    alignItems: 'center',
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    gap: 6,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
  },
});
