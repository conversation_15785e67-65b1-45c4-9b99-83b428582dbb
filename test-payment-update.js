// Test script for payment update edge function
// Run this with: node test-payment-update.js

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://vsnhscndlifvaptwdfsw.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testPaymentUpdate() {
  try {
    console.log('Testing payment update edge function...');
    
    // Test data for successful payment
    const testPaymentData = {
      razorpay_order_id: 'order_test_123456',
      razorpay_payment_id: 'pay_test_123456',
      razorpay_signature: 'signature_test_123456',
      plan_id: 'standard',
      payment_status: 'success'
    };

    // Call the edge function
    const { data, error } = await supabase.functions.invoke('update-subscription-payment', {
      body: testPaymentData
    });

    if (error) {
      console.error('Edge function error:', error);
    } else {
      console.log('Edge function response:', data);
    }

    // Test data for cancelled payment
    const testCancelledData = {
      razorpay_order_id: 'order_test_cancelled_123456',
      plan_id: 'premium',
      payment_status: 'cancelled'
    };

    console.log('\nTesting cancelled payment...');
    const { data: cancelData, error: cancelError } = await supabase.functions.invoke('update-subscription-payment', {
      body: testCancelledData
    });

    if (cancelError) {
      console.error('Cancel test error:', cancelError);
    } else {
      console.log('Cancel test response:', cancelData);
    }

  } catch (error) {
    console.error('Test error:', error);
  }
}

// Run the test
testPaymentUpdate();
