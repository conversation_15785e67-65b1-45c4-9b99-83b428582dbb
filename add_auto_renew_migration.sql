-- Migration: Add missing columns to user_subscriptions table
-- Run this script in your Supabase SQL Editor to fix the schema

-- Add missing columns if they don't already exist
DO $$ 
BEGIN
    -- Add payment_id column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_subscriptions' 
        AND column_name = 'payment_id'
    ) THEN
        ALTER TABLE user_subscriptions 
        ADD COLUMN payment_id VARCHAR(100);
        
        RAISE NOTICE 'Added payment_id column to user_subscriptions table';
    ELSE
        RAISE NOTICE 'payment_id column already exists in user_subscriptions table';
    END IF;

    -- Add order_id column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_subscriptions' 
        AND column_name = 'order_id'
    ) THEN
        ALTER TABLE user_subscriptions 
        ADD COLUMN order_id VARCHAR(100);
        
        RAISE NOTICE 'Added order_id column to user_subscriptions table';
    ELSE
        RAISE NOTICE 'order_id column already exists in user_subscriptions table';
    END IF;

    -- Add auto_renew column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_subscriptions' 
        AND column_name = 'auto_renew'
    ) THEN
        ALTER TABLE user_subscriptions 
        ADD COLUMN auto_renew BOOLEAN DEFAULT true;
        
        -- Update existing records to have auto_renew = true by default
        UPDATE user_subscriptions SET auto_renew = true WHERE auto_renew IS NULL;
        
        RAISE NOTICE 'Added auto_renew column to user_subscriptions table';
    ELSE
        RAISE NOTICE 'auto_renew column already exists in user_subscriptions table';
    END IF;
END $$; 