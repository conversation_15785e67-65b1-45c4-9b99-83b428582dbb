# Create Razorpay Order Edge Function

This Supabase Edge Function creates Razorpay orders using the Razorpay Orders API. It provides a secure server-side implementation for order creation without exposing API credentials to the client.

## Features

- ✅ Secure Razorpay API integration
- ✅ Order logging to database
- ✅ CORS support for web clients
- ✅ Input validation
- ✅ Error handling
- ✅ TypeScript support

## Environment Variables

Set these environment variables in your Supabase project:

```bash
RAZORPAY_KEY_ID=rzp_test_NQ26LCch0J1GHa
RAZORPAY_KEY_SECRET=gsedvop5Yvz7fKnilyqy8L37
```

## API Endpoint

```
POST /functions/v1/create-razorpay-order
```

## Request Body

```typescript
{
  amount: number;           // Required: Amount in paise (e.g., 50000 for ₹500)
  currency?: string;        // Optional: Currency code (default: 'INR')
  receipt?: string;         // Optional: Receipt ID for tracking
  partial_payment?: boolean; // Optional: Allow partial payments (default: false)
  notes?: Record<string, string>; // Optional: Additional metadata
  user_id?: string;         // Optional: User ID for logging
  plan_id?: string;         // Optional: Subscription plan ID
}
```

## Response

### Success Response (200)
```typescript
{
  success: true,
  order: {
    id: string;              // Razorpay order ID
    entity: "order",
    amount: number,          // Amount in paise
    amount_paid: number,
    amount_due: number,
    currency: string,
    receipt: string,
    offer_id: string | null,
    status: string,          // "created"
    attempts: number,
    notes: Record<string, string>,
    created_at: number       // Unix timestamp
  },
  message: "Order created successfully"
}
```

### Error Response (400/500)
```typescript
{
  error: string,
  message: string
}
```

## Usage Examples

### Using the TypeScript Client

```typescript
import { createRazorpayOrder, createSubscriptionOrder } from '@/lib/razorpay-orders';

// Create a custom order
const order = await createRazorpayOrder({
  amount: 50000, // ₹500 in paise
  receipt: 'receipt_123',
  notes: {
    product: 'Premium Subscription',
    customer_id: 'user_123'
  }
});

// Create a subscription order
const subscriptionOrder = await createSubscriptionOrder('standard');
```

### Direct API Call

```typescript
import { supabase } from '@/lib/supabase';

const { data, error } = await supabase.functions.invoke('create-razorpay-order', {
  body: {
    amount: 50000,
    currency: 'INR',
    receipt: 'receipt_123',
    notes: {
      plan_id: 'standard',
      user_id: 'user_123'
    }
  }
});
```

### cURL Example

```bash
curl -X POST 'https://your-project.supabase.co/functions/v1/create-razorpay-order' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{
    "amount": 50000,
    "currency": "INR",
    "receipt": "receipt_123",
    "notes": {
      "plan_id": "standard"
    }
  }'
```

## Database Integration

The function automatically logs orders to the `razorpay_orders` table with the following schema:

```sql
CREATE TABLE razorpay_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id VARCHAR(255) UNIQUE NOT NULL,
    user_id UUID REFERENCES auth.users(id),
    amount INTEGER NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    receipt VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'created',
    plan_id VARCHAR(50),
    notes JSONB DEFAULT '{}',
    razorpay_created_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## Security

- ✅ Razorpay credentials are stored as environment variables
- ✅ Row Level Security (RLS) enabled on database tables
- ✅ Users can only access their own orders
- ✅ Service role has full access for edge function operations

## Error Handling

The function handles various error scenarios:

- Missing or invalid Razorpay credentials
- Invalid request parameters
- Razorpay API errors
- Database logging failures (non-blocking)

## Deployment

1. Deploy the edge function:
```bash
supabase functions deploy create-razorpay-order
```

2. Set environment variables:
```bash
supabase secrets set RAZORPAY_KEY_ID=your_key_id
supabase secrets set RAZORPAY_KEY_SECRET=your_key_secret
```

3. Run the migration to create the database table:
```bash
supabase db push
```

## Testing

Test the function locally:

```bash
supabase functions serve create-razorpay-order
```

Then make a test request:

```bash
curl -X POST 'http://localhost:54321/functions/v1/create-razorpay-order' \
  -H 'Authorization: Bearer YOUR_ANON_KEY' \
  -H 'Content-Type: application/json' \
  -d '{"amount": 50000, "receipt": "test_receipt"}'
```
