#!/usr/bin/env node

/**
 * <PERSON>ript to automatically bump Android version code before EAS submission
 * Usage: node scripts/bump-version.js
 */

const fs = require('fs');
const path = require('path');

function bumpAndroidVersionCode() {
  const appJsonPath = path.join(__dirname, '..', 'app.json');
  
  try {
    // Read the current app.json
    const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
    
    // Get current version code
    const currentVersionCode = appJson.expo.android.versionCode || 1;
    const newVersionCode = currentVersionCode + 1;
    
    // Update version code
    appJson.expo.android.versionCode = newVersionCode;
    
    // Write back to app.json
    fs.writeFileSync(appJsonPath, JSON.stringify(appJson, null, 2));
    
    console.log(`✅ Android version code bumped from ${currentVersionCode} to ${newVersionCode}`);
    console.log(`📱 App version: ${appJson.expo.version}`);
    console.log(`🔢 Version code: ${newVersionCode}`);
    
    return { success: true, oldVersionCode: currentVersionCode, newVersionCode };
  } catch (error) {
    console.error('❌ Error bumping version code:', error.message);
    return { success: false, error: error.message };
  }
}

// If called directly, run the bump function
if (require.main === module) {
  bumpAndroidVersionCode();
}

module.exports = { bumpAndroidVersionCode }; 