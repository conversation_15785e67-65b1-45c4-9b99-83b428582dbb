import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { supabase } from '@/lib/supabase';
import { MaterialIcons } from '@expo/vector-icons';
import { addDays, format, isAfter, isBefore, isPast, isToday, parseISO } from 'date-fns';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  FlatList,
  RefreshControl,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  useColorScheme,
  View,
} from 'react-native';

const { width } = Dimensions.get('window');

// Type definitions
type Task = {
  id: string;
  site_id: string;
  name: string;
  work_category: string;
  status: 'pending' | 'in progress' | 'completed';
  overall_progress: number;
  due_date: string;
  created_at: string;
  updated_at: string;
  created_by: string;
};

// Filter types
type FilterOptions = {
  category: string | null;
  status: string | null;
  dueDate: string | null;
};

export default function TasksScreen() {
  const { id: siteId } = useLocalSearchParams();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [userRole, setUserRole] = useState<string | null>(null);
  const colorScheme = useColorScheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<FilterOptions>({
    category: null,
    status: null,
    dueDate: null,
  });
  const [showFilters, setShowFilters] = useState(false);

  // Fetch tasks
  const fetchTasks = useCallback(async () => {
    if (!siteId) return;
    
    try {
      setLoading(true);
      
      // Get current user
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) return;
      
      // Get user role for the site
      const { data: userMemberData } = await supabase
        .from('site_members')
        .select('role')
        .eq('site_id', siteId)
        .eq('user_id', userData.user.id)
        .single();
      
      if (userMemberData) {
        setUserRole(userMemberData.role);
      }
      
      // Fetch tasks
      const { data: tasksData, error } = await supabase
        .from('tasks')
        .select('*')
        .eq('site_id', siteId);
      
      if (error) {
        console.error('Error fetching tasks:', error);
        Alert.alert('Error', 'Failed to load tasks');
        return;
      }
      
      if (tasksData) {
        setTasks(tasksData as Task[]);
      }
    } catch (error) {
      console.error('Error:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [siteId]);
  
  useEffect(() => {
    fetchTasks();
  }, [fetchTasks]);
  
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchTasks();
  }, [fetchTasks]);

  // Filter tasks based on search query and filters
  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      // Search query filter
      if (searchQuery && !task.name.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }
      
      // Category filter
      if (filters.category && task.work_category !== filters.category) {
        return false;
      }
      
      // Status filter
      if (filters.status && task.status !== filters.status) {
        return false;
      }
      
      // Due date filter
      if (filters.dueDate) {
        const dueDate = parseISO(task.due_date);
        
        switch (filters.dueDate) {
          case 'today':
            return isToday(dueDate);
          case 'overdue':
            return isPast(dueDate) && !isToday(dueDate);
          case 'upcoming':
            return isAfter(dueDate, new Date()) && isBefore(dueDate, addDays(new Date(), 7));
          default:
            return true;
        }
      }
      
      return true;
    });
  }, [tasks, searchQuery, filters]);

  // Reset all filters
  const resetFilters = () => {
    setFilters({
      category: null,
      status: null,
      dueDate: null,
    });
    setSearchQuery('');
  };

  // Handle task deletion (with role check)
  const handleDeleteTask = async (taskId: string) => {
    // Only Super Admin can delete tasks
    if (userRole !== 'Super Admin') {
      Alert.alert('Permission Denied', 'Only Super Admins can delete tasks');
      return;
    }
    
    Alert.alert(
      'Delete Task',
      'Are you sure you want to delete this task? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const { error } = await supabase
                .from('tasks')
                .delete()
                .eq('id', taskId);
              
              if (error) {
                console.error('Error deleting task:', error);
                Alert.alert('Error', 'Failed to delete task');
                return;
              }
              
              // Update the local state
              setTasks(prevTasks => prevTasks.filter(task => task.id !== taskId));
              Alert.alert('Success', 'Task deleted successfully');
            } catch (error) {
              console.error('Error:', error);
              Alert.alert('Error', 'An unexpected error occurred');
            }
          },
        },
      ]
    );
  };

  // Handle task edit navigation
  const handleEditTask = (taskId: string) => {
    if (userRole === 'Member') {
      Alert.alert('Permission Denied', 'You do not have permission to edit tasks');
      return;
    }
    
    router.push(`/site/${siteId}/edit-task?taskId=${taskId}`);
  };

  // Handle task creation navigation
  const handleCreateTask = () => {
    if (userRole === 'Member') {
      Alert.alert('Permission Denied', 'You do not have permission to create tasks');
      return;
    }
    
    router.push(`/site/${siteId}/create-task`);
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#f59e0b'; // Amber
      case 'in progress':
        return '#3b82f6'; // Blue
      case 'completed':
        return '#10b981'; // Green
      default:
        return '#64748b'; // Gray
    }
  };

  // Determine due date styling based on date
  const getDueDateStyle = (dueDateStr: string) => {
    const dueDate = parseISO(dueDateStr);
    const today = new Date();
    
    if (isPast(dueDate) && !isToday(dueDate)) {
      return { color: '#ef4444', fontWeight: 'bold' as const }; // Red for overdue
    } else if (isToday(dueDate)) {
      return { color: '#f59e0b', fontWeight: 'bold' as const }; // Amber for today
    } else if (isBefore(dueDate, addDays(today, 3))) {
      return { color: '#f97316', fontWeight: '600' as const }; // Orange for approaching
    }
    
    return {}; // Default styling
  };

  // Render filters section
  const renderFilters = () => (
    <View style={styles.filtersContainer}>
      <View style={styles.filterRow}>
        <View style={styles.filterGroup}>
          <ThemedText style={styles.filterLabel}>Category</ThemedText>
          <View style={styles.filterOptions}>
            {['Foundation Work', 'Structural Work', 'Masonry', 'Plumbing', 'Electrical'].map((category) => (
              <TouchableOpacity
                key={category}
                style={[
                  styles.filterChip,
                  filters.category === category && styles.filterChipSelected,
                ]}
                onPress={() => setFilters({...filters, category: filters.category === category ? null : category})}
              >
                <ThemedText style={[
                  styles.filterChipText,
                  filters.category === category && styles.filterChipTextSelected,
                ]}>
                  {category}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
      
      <View style={styles.filterRow}>
        <View style={styles.filterGroup}>
          <ThemedText style={styles.filterLabel}>Status</ThemedText>
          <View style={styles.filterOptions}>
            {['pending', 'in progress', 'completed'].map((status) => (
              <TouchableOpacity
                key={status}
                style={[
                  styles.filterChip,
                  filters.status === status && { backgroundColor: getStatusColor(status) },
                ]}
                onPress={() => setFilters({...filters, status: filters.status === status ? null : status})}
              >
                <ThemedText style={[
                  styles.filterChipText,
                  filters.status === status && styles.filterChipTextSelected,
                ]}>
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
      
      <View style={styles.filterRow}>
        <View style={styles.filterGroup}>
          <ThemedText style={styles.filterLabel}>Due Date</ThemedText>
          <View style={styles.filterOptions}>
            {[
              { label: 'Today', value: 'today' },
              { label: 'Overdue', value: 'overdue' },
              { label: 'Next 7 days', value: 'upcoming' },
            ].map(({ label, value }) => (
              <TouchableOpacity
                key={value}
                style={[
                  styles.filterChip,
                  filters.dueDate === value && styles.filterChipSelected,
                ]}
                onPress={() => setFilters({...filters, dueDate: filters.dueDate === value ? null : value})}
              >
                <ThemedText style={[
                  styles.filterChipText,
                  filters.dueDate === value && styles.filterChipTextSelected,
                ]}>
                  {label}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
      
      <TouchableOpacity style={styles.resetButton} onPress={resetFilters}>
        <ThemedText style={styles.resetButtonText}>Reset Filters</ThemedText>
      </TouchableOpacity>
    </View>
  );

  // Render task item
  const renderTaskItem = ({ item }: { item: Task }) => (
    <TouchableOpacity
      style={styles.taskCard}
      onPress={() => router.push(`/site/${siteId}/task-details?taskId=${item.id}`)}
    >
      <View style={styles.taskHeader}>
        <ThemedText style={styles.taskName}>{item.name}</ThemedText>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) + '20' }]}>
          <ThemedText style={[styles.statusText, { color: getStatusColor(item.status) }]}>
            {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
          </ThemedText>
        </View>
      </View>
      
      <View style={styles.taskCategory}>
        <MaterialIcons name="category" size={16} color="#64748b" />
        <ThemedText style={styles.categoryText}>{item.work_category}</ThemedText>
      </View>
      
      <View style={styles.progressContainer}>
        <View style={styles.progressBarContainer}>
          <View 
            style={[
              styles.progressBar, 
              { width: `${item.overall_progress}%`, backgroundColor: '#f97316' }
            ]} 
          />
        </View>
        <ThemedText style={styles.progressText}>{item.overall_progress}%</ThemedText>
      </View>
      
      <View style={styles.taskFooter}>
        <View style={styles.dateInfo}>
          <View style={styles.dateItem}>
            <MaterialIcons name="event-available" size={14} color="#64748b" />
            <ThemedText style={[styles.dateText, getDueDateStyle(item.due_date)]}>
              Due: {format(parseISO(item.due_date), 'MMM d, yyyy')}
            </ThemedText>
          </View>
          
          <View style={styles.dateItem}>
            <MaterialIcons name="history" size={14} color="#64748b" />
            <ThemedText style={styles.dateText}>
              Created: {format(parseISO(item.created_at), 'MMM d, yyyy')}
            </ThemedText>
          </View>
        </View>
        
        {/* Action buttons based on user role */}
        {userRole !== 'Member' && (
          <View style={styles.taskActions}>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={() => handleEditTask(item.id)}
            >
              <MaterialIcons name="edit" size={18} color="#3b82f6" />
            </TouchableOpacity>
            
            {userRole === 'Super Admin' && (
              <TouchableOpacity 
                style={styles.actionButton}
                onPress={() => handleDeleteTask(item.id)}
              >
                <MaterialIcons name="delete" size={18} color="#ef4444" />
              </TouchableOpacity>
            )}
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  // Loading state
  if (loading && !refreshing) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#f97316" />
      </ThemedView>
    );
  }

  return (
    <ThemedView style={styles.container}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      
      <Stack.Screen options={{ title: 'Tasks' }} />
      
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <MaterialIcons name="search" size={20} color="#94a3b8" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search tasks..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#94a3b8"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <MaterialIcons name="clear" size={20} color="#94a3b8" />
            </TouchableOpacity>
          )}
        </View>
        
        <TouchableOpacity 
          style={styles.filterButton}
          onPress={() => setShowFilters(!showFilters)}
        >
          <MaterialIcons name="filter-list" size={24} color="#f97316" />
        </TouchableOpacity>
      </View>
      
      {showFilters && renderFilters()}
      
      <FlatList
        data={filteredTasks}
        keyExtractor={(item) => item.id}
        renderItem={renderTaskItem}
        contentContainerStyle={styles.tasksList}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} colors={['#f97316']} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialIcons name="assignment" size={64} color="#cbd5e1" />
            <ThemedText style={styles.emptyText}>No tasks found</ThemedText>
            <ThemedText style={styles.emptySubtext}>
              {searchQuery || filters.category || filters.status || filters.dueDate
                ? 'Try clearing your filters'
                : 'Create a new task to get started'}
            </ThemedText>
            {(searchQuery || filters.category || filters.status || filters.dueDate) && (
              <TouchableOpacity style={styles.resetButtonEmpty} onPress={resetFilters}>
                <ThemedText style={styles.resetButtonText}>Reset Filters</ThemedText>
              </TouchableOpacity>
            )}
          </View>
        }
      />
      
      {/* Add Task FAB - hide for Members */}
      {userRole !== 'Member' && (
        <TouchableOpacity 
          style={styles.fab}
          onPress={handleCreateTask}
        >
          <MaterialIcons name="add" size={24} color="#fff" />
        </TouchableOpacity>
      )}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
    gap: 8,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(100, 100, 100, 0.06)',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 15,
    padding: 0,
  },
  filterButton: {
    padding: 8,
  },
  filtersContainer: {
    paddingHorizontal: 16,
    paddingBottom: 8,
    backgroundColor: 'rgba(100, 100, 100, 0.03)',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(100, 100, 100, 0.1)',
  },
  filterRow: {
    marginBottom: 10,
  },
  filterGroup: {
    marginBottom: 4,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 6,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterChip: {
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: 'rgba(100, 100, 100, 0.08)',
  },
  filterChipSelected: {
    backgroundColor: '#f97316',
  },
  filterChipText: {
    fontSize: 12,
    fontWeight: '500',
  },
  filterChipTextSelected: {
    color: '#fff',
  },
  resetButton: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: 'rgba(100, 100, 100, 0.1)',
    marginTop: 4,
    marginBottom: 8,
  },
  resetButtonEmpty: {
    alignSelf: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(100, 100, 100, 0.1)',
    marginTop: 12,
  },
  resetButtonText: {
    fontSize: 12,
    fontWeight: '500',
  },
  tasksList: {
    padding: 16,
    paddingBottom: 100, // Increased padding for FAB to be responsive on all screen sizes
  },
  taskCard: {
    backgroundColor: 'rgba(100, 100, 100, 0.06)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  taskName: {
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  taskCategory: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  categoryText: {
    fontSize: 14,
    marginLeft: 6,
    color: '#64748b',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  progressBarContainer: {
    flex: 1,
    height: 8,
    backgroundColor: 'rgba(100, 100, 100, 0.1)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '600',
    minWidth: 35,
    textAlign: 'right',
  },
  taskFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateInfo: {
    flex: 1,
  },
  dateItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  dateText: {
    fontSize: 12,
    marginLeft: 6,
    color: '#64748b',
  },
  taskActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 6,
    borderRadius: 6,
    backgroundColor: 'rgba(100, 100, 100, 0.08)',
  },
  fab: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#f97316',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#94a3b8',
    marginTop: 8,
    textAlign: 'center',
  },
}); 