#!/usr/bin/env node

/**
 * Script to manage app versions for in-app updates
 * Usage: node scripts/manage-app-version.js [command] [options]
 * 
 * Commands:
 *   add      Add a new app version
 *   list     List all app versions
 *   activate Set a version as active
 * 
 * Examples:
 *   node scripts/manage-app-version.js add --version 1.1.0 --versionCode 2 --platform android --mandatory false --notes "Bug fixes and improvements"
 *   node scripts/manage-app-version.js list --platform android
 *   node scripts/manage-app-version.js activate --id uuid-here
 */

const { createClient } = require('@supabase/supabase-js');
const { readFileSync } = require('fs');
const { join } = require('path');

// Load environment variables or use service key
let supabaseUrl, supabaseKey;

try {
  // For production, you should use environment variables
  supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'YOUR_SUPABASE_URL';
  supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'YOUR_SERVICE_ROLE_KEY';
  
  if (!supabaseUrl.startsWith('http') || !supabaseKey.startsWith('eyJ')) {
    throw new Error('Invalid Supabase credentials');
  }
} catch (error) {
  console.error('❌ Error loading Supabase configuration:');
  console.error('Please set EXPO_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables');
  console.error('or update this script with your Supabase credentials.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: { persistSession: false }
});

const commands = {
  async add(options) {
    const { version, versionCode, platform, mandatory, notes, downloadUrl } = options;
    
    if (!version || !versionCode || !platform) {
      console.error('❌ Error: version, versionCode, and platform are required');
      console.log('Usage: add --version 1.1.0 --versionCode 2 --platform android');
      return;
    }

    const versionData = {
      version,
      version_code: parseInt(versionCode),
      platform,
      mandatory: mandatory === 'true',
      release_notes: notes || 'Bug fixes and improvements',
      download_url: downloadUrl,
      is_active: true
    };

    try {
      // Deactivate previous versions for the same platform
      await supabase
        .from('app_versions')
        .update({ is_active: false })
        .eq('platform', platform);

      // Insert new version
      const { data, error } = await supabase
        .from('app_versions')
        .insert([versionData])
        .select()
        .single();

      if (error) {
        console.error('❌ Error adding version:', error.message);
        return;
      }

      console.log('✅ Successfully added new app version:');
      console.log(`   Version: ${data.version} (${data.version_code})`);
      console.log(`   Platform: ${data.platform}`);
      console.log(`   Mandatory: ${data.mandatory}`);
      console.log(`   Notes: ${data.release_notes}`);
      console.log(`   ID: ${data.id}`);
    } catch (error) {
      console.error('❌ Error:', error.message);
    }
  },

  async list(options) {
    const { platform } = options;
    
    try {
      let query = supabase
        .from('app_versions')
        .select('*')
        .order('version_code', { ascending: false });

      if (platform) {
        query = query.eq('platform', platform);
      }

      const { data, error } = await query;

      if (error) {
        console.error('❌ Error fetching versions:', error.message);
        return;
      }

      if (!data || data.length === 0) {
        console.log('No app versions found');
        return;
      }

      console.log('\n📱 App Versions:');
      console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
      
      data.forEach(version => {
        const status = version.is_active ? '🟢 Active' : '⚪ Inactive';
        const mandatory = version.mandatory ? '🔴 Mandatory' : '🟡 Optional';
        
        console.log(`${status} | ${version.platform.toUpperCase()} | v${version.version} (${version.version_code}) | ${mandatory}`);
        console.log(`   Notes: ${version.release_notes || 'No release notes'}`);
        console.log(`   Created: ${new Date(version.created_at).toLocaleDateString()}`);
        console.log(`   ID: ${version.id}`);
        console.log('─────────────────────────────────────────────────────────────────────────────────');
      });
    } catch (error) {
      console.error('❌ Error:', error.message);
    }
  },

  async activate(options) {
    const { id } = options;
    
    if (!id) {
      console.error('❌ Error: version ID is required');
      console.log('Usage: activate --id uuid-here');
      return;
    }

    try {
      // Get the version to activate
      const { data: versionToActivate, error: fetchError } = await supabase
        .from('app_versions')
        .select('*')
        .eq('id', id)
        .single();

      if (fetchError || !versionToActivate) {
        console.error('❌ Error: Version not found');
        return;
      }

      // Deactivate all versions for the same platform
      await supabase
        .from('app_versions')
        .update({ is_active: false })
        .eq('platform', versionToActivate.platform);

      // Activate the specified version
      const { error } = await supabase
        .from('app_versions')
        .update({ is_active: true })
        .eq('id', id);

      if (error) {
        console.error('❌ Error activating version:', error.message);
        return;
      }

      console.log('✅ Successfully activated version:');
      console.log(`   Version: ${versionToActivate.version} (${versionToActivate.version_code})`);
      console.log(`   Platform: ${versionToActivate.platform}`);
    } catch (error) {
      console.error('❌ Error:', error.message);
    }
  }
};

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const command = args[0];
  const options = {};

  for (let i = 1; i < args.length; i += 2) {
    if (args[i].startsWith('--')) {
      const key = args[i].slice(2);
      const value = args[i + 1];
      options[key] = value;
    }
  }

  return { command, options };
}

// Main execution
async function main() {
  const { command, options } = parseArgs();

  if (!command || !commands[command]) {
    console.log('📱 Infratask App Version Manager\n');
    console.log('Available commands:');
    console.log('  add      Add a new app version');
    console.log('  list     List all app versions');
    console.log('  activate Set a version as active\n');
    console.log('Examples:');
    console.log('  node scripts/manage-app-version.js add --version 1.1.0 --versionCode 2 --platform android --mandatory false --notes "Bug fixes"');
    console.log('  node scripts/manage-app-version.js list --platform android');
    console.log('  node scripts/manage-app-version.js activate --id uuid-here');
    return;
  }

  await commands[command](options);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { commands }; 