import { View, type ViewProps } from 'react-native';

import { useThemeColor } from '@/hooks/useThemeColor';

export type ThemedViewProps = ViewProps & {
  lightColor?: string;
  darkColor?: string;
};

export function ThemedView({ style, lightColor, darkColor, ...otherProps }: ThemedViewProps) {
  // Get the theme color but provide a fallback color in case theme resolution fails
  const themeBackgroundColor = useThemeColor({ light: lightColor || '#ffffff', dark: darkColor || '#121212' }, 'background');
  // Use a guaranteed visible background color
  const backgroundColor = themeBackgroundColor || '#ffffff';

  return <View style={[{ backgroundColor }, style]} {...otherProps} />;
}
