# In-App Update Troubleshooting Guide

## Issue: In-App Updates Not Working When Version Code is Increased

### Common Causes and Solutions

#### 1. Version Code Mismatch ✅ FIXED
**Problem**: Different version codes in `app.json` and `android/app/build.gradle`
- `app.json` had `versionCode: 16`
- `android/app/build.gradle` had `versionCode: 14`

**Solution**: Updated `android/app/build.gradle` to match `app.json` with `versionCode: 16`

#### 2. Google Play Console Configuration
**Check these settings in Google Play Console:**

1. **App Bundle Upload**:
   - Ensure the new version with higher version code is uploaded
   - Check that the bundle is in "Production" track (not just "Internal testing")
   - Verify the release is "Live" and not just "Pending"

2. **Release Management**:
   - Go to Release management → App releases
   - Ensure the new version is in the Production track
   - Check release status is "Live"

3. **Staged Rollout**:
   - If using staged rollout, ensure your test device is included
   - Consider increasing rollout percentage to 100%

#### 3. Device and Testing Issues

**Testing Requirements**:
- Must test on a device with the app installed from Google Play Store
- Cannot test on debug builds or sideloaded APKs
- Device must have Google Play Services
- App must be signed with the same certificate as the Play Store version

**Device Preparation**:
```bash
# Clear Google Play Store cache
adb shell pm clear com.android.vending

# Clear Google Play Services cache  
adb shell pm clear com.google.android.gms

# Restart device
adb reboot
```

#### 4. App Signing Issues
**Verify Signing Configuration**:
- Ensure production APK/AAB is signed with the same key as previous versions
- Check that the signing configuration in `android/app/build.gradle` is correct
- Verify the keystore file is the same one used for Play Store uploads

#### 5. Google Play Core Library Issues
**Check Dependencies** (Already configured):
```gradle
implementation 'com.google.android.play:app-update:2.1.0'
implementation 'com.google.android.play:app-update-ktx:2.1.0'
```

#### 6. Update Availability Timing
**Google Play Update Propagation**:
- Updates may take 2-24 hours to be available via in-app updates
- Even after the app is live on Play Store, in-app update API may not immediately detect it
- Try testing after 24 hours of the new version being live

#### 7. Testing with Internal App Sharing
**For Immediate Testing**:
1. Use Internal App Sharing in Google Play Console
2. Upload your AAB to Internal App Sharing
3. Install the previous version from Internal App Sharing
4. Upload the new version to Internal App Sharing
5. Test in-app updates between these versions

### Debugging Steps

#### 1. Use the Debug Component
Add the `InAppUpdateDebugger` component to your app:

```typescript
import { InAppUpdateDebugger } from '@/components/InAppUpdateDebugger';

// Add to your debug screen or development menu
<InAppUpdateDebugger />
```

#### 2. Check Android Logs
```bash
# Filter for in-app update logs
adb logcat | grep -E "(InAppUpdate|PlayCore)"

# Check for specific errors
adb logcat | grep -E "(ERROR|WARN)" | grep -i update
```

#### 3. Verify Version Information
The debugger will show:
- Current version code installed on device
- Available version code from Play Store
- Whether immediate/flexible updates are allowed
- Detailed error messages

#### 4. Test Update Flow
1. Install older version from Play Store
2. Upload newer version to Play Store (wait for it to go live)
3. Open app and trigger update check
4. Verify logs show correct version codes

### Enhanced Logging

The native module now includes enhanced logging:
- Current vs available version codes
- Update availability status
- Detailed error messages
- Android system logs

### Common Error Messages

#### "Update not available"
- Version codes are the same
- New version not yet propagated
- App not installed from Play Store

#### "Update manager not initialized"
- Google Play Services not available
- App not signed correctly
- Device doesn't support in-app updates

#### "Failed to start update flow"
- User cancelled previous update
- Network connectivity issues
- Google Play Store app issues

### Best Practices

1. **Always test on real devices** with Play Store installations
2. **Wait 24 hours** after publishing before testing
3. **Use Internal App Sharing** for immediate testing
4. **Check Google Play Console** for release status
5. **Monitor Android logs** for detailed error information
6. **Test both immediate and flexible** update flows
7. **Handle update failures gracefully** in your app

### Production Checklist

Before releasing with in-app updates:

- [ ] Version codes are consistent across all config files
- [ ] App is properly signed with production certificate
- [ ] Google Play Core dependencies are included
- [ ] Update checking is implemented in app startup
- [ ] Error handling is implemented for update failures
- [ ] Testing completed with Internal App Sharing
- [ ] Fallback mechanisms are in place

### Support Resources

- [Google Play Core In-App Updates Documentation](https://developer.android.com/guide/playcore/in-app-updates)
- [Internal App Sharing Guide](https://support.google.com/googleplay/android-developer/answer/9303479)
- [App Bundle Testing Guide](https://developer.android.com/guide/app-bundle/test)
