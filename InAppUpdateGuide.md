# In-App Update Feature for Infratask

## Overview

The in-app update feature automatically checks for new versions of the Infratask app and prompts users to update from the Google Play Store. This ensures users always have the latest features and security updates.

## Features

- **Automatic Version Checking**: Checks for updates when the dashboard loads
- **Smart Throttling**: Only checks for updates once per 24 hours to avoid excessive requests
- **Mandatory vs Optional Updates**: Support for both mandatory (force) and optional updates
- **Beautiful UI**: Modern, responsive update prompt with dark/light mode support
- **Release Notes**: Display what's new in the update
- **Play Store Integration**: Direct links to Play Store for seamless updates

## Implementation Details

### 1. Database Schema

The feature uses an `app_versions` table to store version information:

```sql
CREATE TABLE app_versions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    version VARCHAR(20) NOT NULL,
    version_code INTEGER NOT NULL,
    platform VARCHAR(10) NOT NULL CHECK (platform IN ('android', 'ios')),
    mandatory BOOLEAN DEFAULT FALSE,
    release_notes TEXT,
    download_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. Core Components

#### InAppUpdateManager (`lib/inAppUpdate.ts`)
- Singleton class that manages update checking and prompting
- Integrates with Expo Application API to get current version
- Queries Supabase for latest version information
- Handles Play Store redirection

#### UpdatePrompt Component (`components/UpdatePrompt.tsx`)
- Reusable React component for displaying update notifications
- Supports both mandatory and optional updates
- Responsive design with dark/light mode support
- Custom styling options

### 3. Dashboard Integration

The update check is integrated into the main dashboard (`app/(tabs)/index.tsx`):
- Automatically checks for updates when user loads the dashboard
- Shows update prompt at the top of the dashboard if available
- Respects 24-hour throttling to avoid excessive checks

## Usage Guide

### For Developers

1. **Setup Database**: Run the SQL migration to create the `app_versions` table
2. **Install Dependencies**: The feature uses `expo-application`
3. **Environment Variables**: Ensure your Supabase configuration is properly set up

### For App Releases

1. **Add New Version**: When releasing a new version, add it to the database using the management script
2. **List Versions**: Check existing versions
3. **Activate Version**: Ensure the correct version is active

### Update Types

#### Optional Updates
- Users can choose "Later" to dismiss the prompt
- Shown with orange accent color
- Will prompt again after 24 hours

#### Mandatory Updates
- Users must update to continue using the app
- Shown with red accent color
- No "Later" option available
- Cannot be dismissed

## Configuration

### Version Code Management

Version codes must be incremental integers:
- Version 1.0.0 → Version Code 1
- Version 1.1.0 → Version Code 2
- Version 1.2.0 → Version Code 3
- Version 2.0.0 → Version Code 4

### Platform Support

Currently supports:
- **Android**: Uses Google Play Store links
- **iOS**: Ready for App Store implementation (requires iOS-specific store URLs)

## Security Considerations

- **RLS Policies**: The table uses Row Level Security to control access
- **Service Role**: Version management script requires service role key
- **Validation**: Version codes and platforms are validated before insertion
- **Rate Limiting**: Built-in 24-hour throttling prevents excessive requests

---

*This feature ensures your users always have the latest version of Infratask with the newest features and security improvements.* 