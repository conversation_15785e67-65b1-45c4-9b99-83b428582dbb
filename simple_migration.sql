-- Simple Migration: Add missing columns to user_subscriptions table
-- Run this script in your Supabase SQL Editor

-- Add payment_id column (ignore error if already exists)
ALTER TABLE user_subscriptions ADD COLUMN IF NOT EXISTS payment_id VARCHAR(100);

-- Add order_id column (ignore error if already exists)
ALTER TABLE user_subscriptions ADD COLUMN IF NOT EXISTS order_id VARCHAR(100);

-- Add auto_renew column (ignore error if already exists)
ALTER TABLE user_subscriptions ADD COLUMN IF NOT EXISTS auto_renew BOOLEAN DEFAULT true; 