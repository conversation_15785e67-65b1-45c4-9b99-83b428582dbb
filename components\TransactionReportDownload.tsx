import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { Modal, StyleSheet, TouchableOpacity, View } from 'react-native';
import { ThemedText } from './ThemedText';

interface TransactionReportDownloadProps {
  visible: boolean;
  onClose: () => void;
  onPdfDownload: () => void;
  onExcelDownload: () => void;
  isGenerating: boolean;
}

export default function TransactionReportDownload({
  visible,
  onClose,
  onPdfDownload,
  onExcelDownload,
  isGenerating
}: TransactionReportDownloadProps) {
  const colorScheme = useColorScheme();
  const primaryColor = '#f97316'; // Infratask primary color
  
  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <View style={[
          styles.modalView,
          { backgroundColor: colorScheme === 'dark' ? '#1c1c1e' : 'white' }
        ]}>
          <View style={styles.modalHeader}>
            <ThemedText type="subtitle">Download Report</ThemedText>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color={colorScheme === 'dark' ? 'white' : 'black'} />
            </TouchableOpacity>
          </View>
          
          <View style={styles.optionsContainer}>
            <TouchableOpacity 
              style={[styles.downloadOption, { borderColor: colorScheme === 'dark' ? '#333' : '#e5e5e5' }]}
              onPress={onPdfDownload}
              disabled={isGenerating}
            >
              <Ionicons name="document-text-outline" size={30} color={primaryColor} />
              <ThemedText style={styles.optionText}>PDF Format</ThemedText>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.downloadOption, { borderColor: colorScheme === 'dark' ? '#333' : '#e5e5e5' }]}
              onPress={onExcelDownload}
              disabled={isGenerating}
            >
              <Ionicons name="grid-outline" size={30} color={primaryColor} />
              <ThemedText style={styles.optionText}>Excel Format</ThemedText>
            </TouchableOpacity>
          </View>
          
          {isGenerating && (
            <ThemedText style={styles.generatingText}>Generating report...</ThemedText>
          )}
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalView: {
    width: '80%',
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  closeButton: {
    padding: 5,
  },
  optionsContainer: {
    marginTop: 10,
  },
  downloadOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderWidth: 1,
    borderRadius: 10,
    marginBottom: 15,
  },
  optionText: {
    marginLeft: 15,
    fontSize: 16,
  },
  generatingText: {
    textAlign: 'center',
    marginTop: 10,
    fontStyle: 'italic',
  },
}); 