import { supabase } from '@/lib/supabase';

export interface CreateStandardSubscriptionRequest {
  added_user_id: string;
  premium_user_id: string;
  subscription_id: string;
}

export interface CreateStandardSubscriptionResponse {
  success: boolean;
  message: string;
  subscription?: any;
  error?: string;
  details?: string;
}

/**
 * Creates a standard subscription for a user added by a premium user
 * The subscription period matches the premium user's subscription dates
 * Uses database function for better performance and reliability
 */
export async function createStandardSubscription(
  request: CreateStandardSubscriptionRequest
): Promise<CreateStandardSubscriptionResponse> {
  try {
    console.log('🔄 Creating standard subscription for added user:', request);

    // Call the database function directly
    const { data, error } = await supabase.rpc('create_standard_subscription_for_user', {
      p_added_user_id: request.added_user_id,
      p_premium_user_id: request.premium_user_id,
      p_subscription_id: request.subscription_id
    });

    if (error) {
      console.error('❌ Error calling create_standard_subscription_for_user function:', error);
      throw new Error(error.message || 'Failed to create standard subscription');
    }

    console.log('✅ Standard subscription creation response:', data);

    // The database function returns a JSON object
    return data as CreateStandardSubscriptionResponse;

  } catch (error) {
    console.error('Error in createStandardSubscription:', error);
    return {
      success: false,
      message: 'Failed to create standard subscription',
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Checks if a user needs a standard subscription created
 * Returns true if the user doesn't have an active subscription
 */
export async function shouldCreateStandardSubscription(userId: string): Promise<boolean> {
  try {
    const { data: existingSubscription, error } = await supabase
      .from('user_subscriptions')
      .select('id, status')
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error checking existing subscription:', error);
      return false;
    }

    // Return true if no active subscription exists
    return !existingSubscription;
  } catch (error) {
    console.error('Error in shouldCreateStandardSubscription:', error);
    return false;
  }
}
