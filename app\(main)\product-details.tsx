import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/lib/supabase';
import { MaterialIcons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import {
    ActivityIndicator,
    Dimensions,
    FlatList,
    Image,
    Pressable,
    ScrollView,
    StyleSheet,
    Text,
    View
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width } = Dimensions.get('window');

interface Product {
  id: number;
  categories: number;
  name: string;
  description: string;
  image_url: string;
  images: string[];
  price: number;
  original_price: number;
  unit: string;
  brand: string;
  stock_quantity: number;
  is_active: boolean;
  weight?: number;
  dimensions?: any;
  sku?: string;
}

interface CartItem {
  id: number;
  name: string;
  price: number;
  original_price?: number;
  quantity: number;
  unit: string;
  brand?: string;
}

export default function ProductDetailsScreen() {
  const { user } = useAuth();
  const insets = useSafeAreaInsets();
  const params = useLocalSearchParams();
  const scrollViewRef = useRef<ScrollView>(null);
  
  const [product, setProduct] = useState<Product | null>(null);
  const [similarProducts, setSimilarProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [addingToCart, setAddingToCart] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Get params
  const productId = params.productId as string;
  const categoryId = params.categoryId as string;
  const categoryName = params.categoryName as string;

  // Theme colors - Force light mode
  const backgroundColor = '#ffffff';
  const textColor = '#000000';
  const secondaryTextColor = '#666666';
  const cardBackground = '#f8f9fa';
  const primaryColor = '#f97316';

  // Load product details
  const loadProduct = async () => {
    try {
      const { data, error } = await supabase
        .from('shop_products')
        .select('*')
        .eq('id', parseInt(productId))
        .single();

      if (error) throw error;

      if (data) {
        const processedProduct = {
          ...data,
          price: parseFloat(data.price) || 0,
          original_price: parseFloat(data.original_price) || 0,
          images: data.images || []
        };
        setProduct(processedProduct);
      }
    } catch (error) {
      console.error('Error loading product:', error);
    }
  };

  // Load similar products
  const loadSimilarProducts = async () => {
    try {
      const { data, error } = await supabase
        .from('shop_products')
        .select('*')
        .eq('categories', parseInt(categoryId))
        .neq('id', parseInt(productId))
        .eq('is_active', true)
        .limit(6)
        .order('name');

      if (error) throw error;

      if (data) {
        const processedProducts = data.map(product => ({
          ...product,
          price: parseFloat(product.price) || 0,
          original_price: parseFloat(product.original_price) || 0,
          images: product.images || []
        }));
        setSimilarProducts(processedProducts);
      }
    } catch (error) {
      console.error('Error loading similar products:', error);
    }
  };

  // Load cart from Supabase
  const loadCart = async () => {
    if (!user) return;

    try {
      const { data: cartData, error: cartError } = await supabase
        .from('shop_cart')
        .select('product_id, quantity')
        .eq('user_id', user.id);

      if (cartError) {
        console.log('Cart loading failed, using empty cart:', cartError);
        setCart([]);
        return;
      }

      if (cartData && cartData.length > 0) {
        const productIds = cartData.map(item => item.product_id);
        const { data: productsData, error: productsError } = await supabase
          .from('shop_products')
          .select('id, name, price, original_price, unit, brand')
          .in('id', productIds);

        if (productsError) {
          console.log('Product loading failed, using empty cart:', productsError);
          setCart([]);
          return;
        }

        if (productsData) {
          const cartItems: CartItem[] = cartData.map(cartItem => {
            const product = productsData.find(p => p.id === cartItem.product_id);
            return {
              id: cartItem.product_id,
              name: product?.name || 'Unknown Product',
              price: parseFloat(product?.price) || 0,
              original_price: parseFloat(product?.original_price) || 0,
              quantity: cartItem.quantity,
              unit: product?.unit || 'piece',
              brand: product?.brand || ''
            };
          });
          setCart(cartItems);
        }
      } else {
        setCart([]);
      }
    } catch (error) {
      console.log('Cart loading failed completely, using empty cart:', error);
      setCart([]);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([loadProduct(), loadSimilarProducts()]);
      if (user) {
        await loadCart();
      }
      setLoading(false);
    };

    if (productId) {
      loadData();
    }
  }, [productId, user]);

  // Get current quantity of item in cart
  const getCartQuantity = (productId: number): number => {
    const cartItem = cart.find(item => item.id === productId);
    return cartItem ? cartItem.quantity : 0;
  };

  // Get all product images
  const getProductImages = (): string[] => {
    if (!product) return [];

    const images = [];
    if (product.image_url) {
      images.push(product.image_url);
    }
    if (product.images && Array.isArray(product.images)) {
      images.push(...product.images);
    }

    return images.length > 0 ? images : ['https://via.placeholder.com/400'];
  };

  // Add to cart function
  const addToCart = async (product: Product) => {
    setAddingToCart(true);

    const existingItem = cart.find(item => item.id === product.id);
    const newQuantity = existingItem ? existingItem.quantity + 1 : 1;

    if (existingItem) {
      setCart(cart.map(item =>
        item.id === product.id
          ? { ...item, quantity: newQuantity }
          : item
      ));
    } else {
      const newCartItem: CartItem = {
        id: product.id,
        name: product.name,
        price: product.price,
        quantity: 1,
        unit: product.unit,
        brand: product.brand
      };
      setCart([...cart, newCartItem]);
    }

    // Save to Supabase
    await saveCartItem(product.id, newQuantity);

    // Reset the adding state after a short delay
    setTimeout(() => {
      setAddingToCart(false);
    }, 500);

    console.log(`Added ${product.name} to cart`);
  };

  // Update cart quantity function
  const updateCartQuantity = async (productId: number, quantity: number) => {
    if (quantity <= 0) {
      // Remove item from cart if quantity is 0 or less
      setCart(cart.filter(item => item.id !== productId));
      await removeCartItem(productId);
    } else {
      setCart(cart.map(item =>
        item.id === productId
          ? { ...item, quantity }
          : item
      ));
      await saveCartItem(productId, quantity);
    }
  };

  // Save cart item to Supabase
  const saveCartItem = async (productId: number, quantity: number) => {
    if (!user) return;

    try {
      // First, try to update existing item
      const { data: existingItem, error: selectError } = await supabase
        .from('shop_cart')
        .select('id')
        .eq('user_id', user.id)
        .eq('product_id', productId)
        .single();

      if (selectError && selectError.code !== 'PGRST116') {
        console.error('Error checking existing cart item:', selectError);
        return;
      }

      if (existingItem) {
        // Update existing item
        const { error: updateError } = await supabase
          .from('shop_cart')
          .update({
            quantity: quantity,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', user.id)
          .eq('product_id', productId);

        if (updateError) {
          console.error('Error updating cart item:', updateError);
        }
      } else {
        // Insert new item
        const { error: insertError } = await supabase
          .from('shop_cart')
          .insert({
            user_id: user.id,
            product_id: productId,
            quantity: quantity,
            added_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (insertError) {
          console.error('Error inserting cart item:', insertError);
        }
      }
    } catch (error) {
      console.error('Error saving cart item:', error);
    }
  };

  // Remove cart item from Supabase
  const removeCartItem = async (productId: number) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('shop_cart')
        .delete()
        .eq('user_id', user.id)
        .eq('product_id', productId);

      if (error) {
        console.error('Error removing cart item:', error);
      }
    } catch (error) {
      console.error('Error removing cart item:', error);
    }
  };

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor, justifyContent: 'center', alignItems: 'center' }]}>
        <ActivityIndicator size="large" color={primaryColor} />
        <Text style={[styles.loadingText, { color: secondaryTextColor }]}>Loading product...</Text>
      </View>
    );
  }

  if (!product) {
    return (
      <View style={[styles.container, { backgroundColor, justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={[styles.errorText, { color: textColor }]}>Product not found</Text>
        <Pressable style={[styles.backToCategory, { backgroundColor: primaryColor }]} onPress={() => router.back()}>
          <Text style={styles.backToCategoryText}>Go Back</Text>
        </Pressable>
      </View>
    );
  }

  const productImages = getProductImages();

  return (
    <View style={[styles.container, { backgroundColor }]}>
      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top + 10 }]}>
        <Pressable style={styles.backButton} onPress={() => router.back()}>
          <MaterialIcons name="arrow-back" size={24} color={textColor} />
        </Pressable>
        <Text style={[styles.headerTitle, { color: textColor }]} numberOfLines={1}>
          {product.name}
        </Text>
        <View style={styles.headerRight}>
          <Pressable
            style={styles.cartButton}
            onPress={() => {
              router.push({
                pathname: '/(main)/shop',
                params: { activeTab: 'cart' }
              });
            }}
          >
            <MaterialIcons name="shopping-cart" size={24} color={textColor} />
            {cart.length > 0 && (
              <View style={[styles.cartBadge, { backgroundColor: primaryColor }]}>
                <Text style={styles.cartBadgeText}>
                  {cart.reduce((total, item) => total + item.quantity, 0)}
                </Text>
              </View>
            )}
          </Pressable>
        </View>
      </View>

      <ScrollView ref={scrollViewRef} style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Product Images */}
        <View style={styles.imageContainer}>
          <ScrollView
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onMomentumScrollEnd={(event) => {
              const index = Math.round(event.nativeEvent.contentOffset.x / width);
              setCurrentImageIndex(index);
            }}
          >
            {productImages.map((imageUrl, index) => (
              <Image
                key={index}
                source={{ uri: imageUrl }}
                style={styles.productImage}
                resizeMode="cover"
              />
            ))}
          </ScrollView>
          
          {/* Image Indicators */}
          {productImages.length > 1 && (
            <View style={styles.imageIndicators}>
              {productImages.map((_, index) => (
                <View
                  key={index}
                  style={[
                    styles.indicator,
                    {
                      backgroundColor: index === currentImageIndex ? primaryColor : '#e0e0e0'
                    }
                  ]}
                />
              ))}
            </View>
          )}
        </View>

        {/* Product Info */}
        <View style={styles.productInfoContainer}>
          <View style={styles.productHeader}>
            <Text style={[styles.productName, { color: textColor }]}>
              {product.name}
            </Text>
            <Text style={[styles.productBrand, { color: secondaryTextColor }]}>
              by {product.brand}
            </Text>
          </View>

          <View style={styles.priceSection}>
            <Text style={[styles.price, { color: primaryColor }]}>
              ₹{product.price.toFixed(2)}
            </Text>
            {product.original_price > product.price && (
              <Text style={[styles.originalPrice, { color: secondaryTextColor }]}>
                ₹{product.original_price.toFixed(2)}
              </Text>
            )}
            <Text style={[styles.unit, { color: secondaryTextColor }]}>
              per {product.unit}
            </Text>
          </View>

          {product.description && (
            <View style={styles.descriptionSection}>
              <Text style={[styles.sectionTitle, { color: textColor }]}>Description</Text>
              <Text style={[styles.description, { color: secondaryTextColor }]}>
                {product.description}
              </Text>
            </View>
          )}

          {/* Product Details */}
          <View style={styles.detailsSection}>
            <Text style={[styles.sectionTitle, { color: textColor }]}>Product Details</Text>
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: secondaryTextColor }]}>Brand:</Text>
              <Text style={[styles.detailValue, { color: textColor }]}>{product.brand}</Text>
            </View>
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: secondaryTextColor }]}>Unit:</Text>
              <Text style={[styles.detailValue, { color: textColor }]}>{product.unit}</Text>
            </View>
            {product.sku && (
              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: secondaryTextColor }]}>SKU:</Text>
                <Text style={[styles.detailValue, { color: textColor }]}>{product.sku}</Text>
              </View>
            )}
            {product.weight && (
              <View style={styles.detailRow}>
                <Text style={[styles.detailLabel, { color: secondaryTextColor }]}>Weight:</Text>
                <Text style={[styles.detailValue, { color: textColor }]}>{product.weight} kg</Text>
              </View>
            )}
            <View style={styles.detailRow}>
              <Text style={[styles.detailLabel, { color: secondaryTextColor }]}>Stock:</Text>
              <Text style={[styles.detailValue, { color: product.stock_quantity > 0 ? '#10b981' : '#ef4444' }]}>
                {product.stock_quantity > 0 ? `${product.stock_quantity} available` : 'Out of stock'}
              </Text>
            </View>
          </View>
        </View>

        {/* Similar Products */}
        {similarProducts.length > 0 && (
          <View style={styles.similarProductsSection}>
            <Text style={[styles.sectionTitle, { color: textColor }]}>Similar Products</Text>
            <FlatList
              data={similarProducts}
              renderItem={({ item }) => (
                <Pressable
                  style={[styles.similarProductCard, { backgroundColor: cardBackground }]}
                  onPress={() => {
                    router.push({
                      pathname: '/(main)/product-details',
                      params: {
                        productId: item.id.toString(),
                        categoryId: categoryId,
                        categoryName: categoryName
                      }
                    });
                  }}
                >
                  <Image
                    source={{ uri: item.image_url || 'https://via.placeholder.com/150' }}
                    style={styles.similarProductImage}
                    resizeMode="cover"
                  />
                  <View style={styles.similarProductInfo}>
                    <Text style={[styles.similarProductName, { color: textColor }]} numberOfLines={2}>
                      {item.name}
                    </Text>
                    <Text style={[styles.similarProductBrand, { color: secondaryTextColor }]}>
                      {item.brand}
                    </Text>
                    <View style={styles.similarProductPriceContainer}>
                      <Text style={[styles.similarProductPrice, { color: primaryColor }]}>
                        ₹{item.price.toFixed(2)}
                      </Text>
                      {item.original_price > item.price && (
                        <Text style={[styles.similarProductOriginalPrice, { color: secondaryTextColor }]}>
                          ₹{item.original_price.toFixed(2)}
                        </Text>
                      )}
                    </View>
                  </View>
                </Pressable>
              )}
              keyExtractor={(item) => item.id.toString()}
              numColumns={2}
              columnWrapperStyle={styles.similarProductRow}
              scrollEnabled={false}
              showsVerticalScrollIndicator={false}
            />
          </View>
        )}

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Bottom Cart Section */}
      <View style={[styles.bottomSection, { backgroundColor, paddingBottom: insets.bottom + 16 }]}>
        <View style={styles.priceBottomContainer}>
          <Text style={[styles.bottomPrice, { color: primaryColor }]}>
            ₹{product.price.toFixed(2)}
          </Text>
          {product.original_price > product.price && (
            <Text style={[styles.bottomOriginalPrice, { color: secondaryTextColor }]}>
              ₹{product.original_price.toFixed(2)}
            </Text>
          )}
        </View>

        {getCartQuantity(product.id) > 0 ? (
          // Quantity Counter
          <View style={[styles.bottomQuantityCounter, { borderColor: primaryColor }]}>
            <Pressable
              style={[styles.bottomQuantityButton, { backgroundColor: primaryColor }]}
              onPress={() => updateCartQuantity(product.id, getCartQuantity(product.id) - 1)}
            >
              <Text style={styles.bottomQuantityButtonText}>-</Text>
            </Pressable>
            <Text style={[styles.bottomQuantityText, { color: textColor }]}>
              {getCartQuantity(product.id)}
            </Text>
            <Pressable
              style={[styles.bottomQuantityButton, { backgroundColor: primaryColor }]}
              onPress={() => updateCartQuantity(product.id, getCartQuantity(product.id) + 1)}
            >
              <Text style={styles.bottomQuantityButtonText}>+</Text>
            </Pressable>
          </View>
        ) : (
          // Add to Cart Button
          <Pressable
            style={[
              styles.bottomAddButton,
              {
                backgroundColor: addingToCart ? '#d97706' : primaryColor,
                opacity: addingToCart ? 0.8 : 1
              }
            ]}
            onPress={() => addToCart(product)}
            disabled={addingToCart || product.stock_quantity <= 0}
          >
            <Text style={styles.bottomAddButtonText}>
              {addingToCart ? 'Adding...' : product.stock_quantity <= 0 ? 'Out of Stock' : 'Add to Cart'}
            </Text>
          </Pressable>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginHorizontal: 16,
  },
  headerRight: {
    width: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cartButton: {
    padding: 8,
    position: 'relative',
  },
  cartBadge: {
    position: 'absolute',
    top: 2,
    right: 2,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  cartBadgeText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  imageContainer: {
    position: 'relative',
    height: 400,
  },
  productImage: {
    width: width,
    height: 400,
  },
  imageIndicators: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  productInfoContainer: {
    padding: 20,
  },
  productHeader: {
    marginBottom: 16,
  },
  productName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    lineHeight: 32,
  },
  productBrand: {
    fontSize: 16,
    fontWeight: '500',
  },
  priceSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  price: {
    fontSize: 28,
    fontWeight: 'bold',
    marginRight: 12,
  },
  originalPrice: {
    fontSize: 18,
    textDecorationLine: 'line-through',
    marginRight: 12,
  },
  unit: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  descriptionSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
  },
  detailsSection: {
    marginBottom: 24,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  detailLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '400',
  },
  similarProductsSection: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  similarProductRow: {
    justifyContent: 'space-between',
  },
  similarProductCard: {
    width: (width - 60) / 2,
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  similarProductImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    marginBottom: 8,
  },
  similarProductInfo: {
    flex: 1,
  },
  similarProductName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  similarProductBrand: {
    fontSize: 12,
    marginBottom: 6,
  },
  similarProductPriceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  similarProductPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 8,
  },
  similarProductOriginalPrice: {
    fontSize: 12,
    textDecorationLine: 'line-through',
  },
  bottomSpacing: {
    height: 100,
  },
  bottomSection: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  priceBottomContainer: {
    flex: 1,
  },
  bottomPrice: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  bottomOriginalPrice: {
    fontSize: 16,
    textDecorationLine: 'line-through',
    marginTop: 2,
  },
  bottomAddButton: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: 'center',
    minWidth: 150,
  },
  bottomAddButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  bottomQuantityCounter: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 2,
    borderRadius: 12,
    overflow: 'hidden',
    minWidth: 150,
  },
  bottomQuantityButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomQuantityButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
  },
  bottomQuantityText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    paddingVertical: 12,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  errorText: {
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 20,
  },
  backToCategory: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  backToCategoryText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});
