-- Migration: Add signature column to user_subscriptions table and update RLS policies
-- This ensures payment authenticity by storing Razorpay signatures

-- Add signature column to user_subscriptions table
ALTER TABLE user_subscriptions ADD COLUMN IF NOT EXISTS razorpay_signature VARCHAR(255);
ALTER TABLE user_subscriptions ADD COLUMN IF NOT EXISTS payment_id VARCHAR(255);
ALTER TABLE user_subscriptions ADD COLUMN IF NOT EXISTS order_id VARCHAR(255);

-- Create payments table for detailed payment logging (if not exists)
CREATE TABLE IF NOT EXISTS payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    plan_id VARCHAR(50) NOT NULL,
    payment_id VARCHAR(255) NOT NULL,
    order_id VARCHAR(255) NOT NULL,
    razorpay_signature VARCHAR(255),
    amount INTEGER NOT NULL, -- Amount in paise
    currency VARCHAR(3) DEFAULT 'INR',
    status VARCHAR(20) DEFAULT 'completed',
    verified_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Enable RLS for payments table
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for payments table
CREATE POLICY "Users can view their own payments" ON payments
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own payments" ON payments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_payment_id ON payments(payment_id);
CREATE INDEX IF NOT EXISTS idx_payments_order_id ON payments(order_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_payment_id ON user_subscriptions(payment_id);

-- Add updated_at trigger for payments table
CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON payments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Update RLS policy for user_subscriptions to allow service role updates
-- This allows the API to update subscriptions during payment verification
CREATE POLICY "Service role can manage all subscriptions" ON user_subscriptions
    FOR ALL USING (
        auth.role() = 'service_role'
        OR auth.uid() = user_id
    );

-- Drop the old restrictive policies and recreate them
DROP POLICY IF EXISTS "Users can insert their own subscriptions" ON user_subscriptions;
DROP POLICY IF EXISTS "Users can update their own subscriptions" ON user_subscriptions;

-- Recreate policies with service role access
CREATE POLICY "Users can insert their own subscriptions" ON user_subscriptions
    FOR INSERT WITH CHECK (
        auth.role() = 'service_role'
        OR auth.uid() = user_id
    );

CREATE POLICY "Users can update their own subscriptions" ON user_subscriptions
    FOR UPDATE USING (
        auth.role() = 'service_role'
        OR auth.uid() = user_id
    ); 