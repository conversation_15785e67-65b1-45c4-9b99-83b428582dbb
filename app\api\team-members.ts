import { mcp_supabase_execute_sql } from '@/lib/mcp-supabase';

// Supabase project ID
const PROJECT_ID = 'vsnhscndlifvaptwdfsw';

/**
 * Fetch team members for a site
 */
export async function fetchTeamMembers(siteId: string) {
  const query = `
    SELECT 
      sm.id, 
      sm.user_id, 
      sm.role, 
      sm.category, 
      sm.joined_at,
      p.full_name,
      p.profile_image_url,
      p.phone_number
    FROM 
      site_members sm
    INNER JOIN 
      profiles p ON sm.user_id = p.user_id
    WHERE 
      sm.site_id = '${siteId}'
  `;
  
  try {
    const result = await mcp_supabase_execute_sql({
      project_id: PROJECT_ID,
      query: query
    });
    
    if (result.error) {
      throw new Error(result.error.message);
    }
    
    return result.data || [];
  } catch (error) {
    console.error('Error fetching team members:', error);
    throw error;
  }
}

/**
 * Delete a team member
 */
export async function deleteTeamMember(memberId: string) {
  const query = `
    DELETE FROM site_members
    WHERE id = '${memberId}'
  `;
  
  try {
    const result = await mcp_supabase_execute_sql({
      project_id: PROJECT_ID,
      query: query
    });
    
    if (result.error) {
      throw new Error(result.error.message);
    }
    
    return true;
  } catch (error) {
    console.error('Error deleting team member:', error);
    throw error;
  }
}

/**
 * Get site details
 */
export async function fetchSiteDetails(siteId: string) {
  const query = `
    SELECT id, name, owner_id 
    FROM sites
    WHERE id = '${siteId}'
  `;
  
  try {
    const result = await mcp_supabase_execute_sql({
      project_id: PROJECT_ID,
      query: query
    });
    
    if (result.error) {
      throw new Error(result.error.message);
    }
    
    return result.data && result.data.length > 0 ? result.data[0] : null;
  } catch (error) {
    console.error('Error fetching site details:', error);
    throw error;
  }
}

// Default export for Expo Router API routes
export default function TeamMembersAPI() {
  return null; // This is not a React component but an API utility
} 