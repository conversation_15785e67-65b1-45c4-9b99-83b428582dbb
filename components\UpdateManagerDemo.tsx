import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Pressable,
  StyleSheet,
  Alert,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useColorScheme } from '@/hooks/useColorScheme';
import { comprehensiveUpdateManager, ComprehensiveUpdateInfo } from '@/lib/comprehensiveUpdateManager';
import { PlayStoreUpdatePrompt } from './PlayStoreUpdatePrompt';

export function UpdateManagerDemo() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const [isInitialized, setIsInitialized] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [updateInfo, setUpdateInfo] = useState<ComprehensiveUpdateInfo | null>(null);
  const [showPrompt, setShowPrompt] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  const colors = {
    background: isDark ? '#1a1a1a' : '#ffffff',
    text: isDark ? '#ffffff' : '#000000',
    textSecondary: isDark ? '#a1a1a1' : '#6b7280',
    border: isDark ? '#333333' : '#e5e7eb',
    primary: '#f97316',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
  };

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)]);
  };

  useEffect(() => {
    initializeUpdateManager();
  }, []);

  const initializeUpdateManager = async () => {
    try {
      addLog('Initializing update manager...');
      const initialized = await comprehensiveUpdateManager.initialize();
      setIsInitialized(initialized);
      
      if (initialized) {
        addLog('✅ Update manager initialized successfully');
        comprehensiveUpdateManager.addUpdateEventListeners();
      } else {
        addLog('❌ Failed to initialize update manager');
      }
    } catch (error) {
      addLog(`❌ Error initializing: ${error}`);
    }
  };

  const checkForUpdates = async (forceCheck = false) => {
    if (!isInitialized) {
      Alert.alert('Error', 'Update manager not initialized');
      return;
    }

    setIsChecking(true);
    try {
      addLog(`Checking for updates${forceCheck ? ' (forced)' : ''}...`);
      const update = await comprehensiveUpdateManager.checkForUpdates(forceCheck);
      
      if (update) {
        addLog(`✅ Update found: v${update.version || update.versionCode} (${update.source})`);
        setUpdateInfo(update);
        setShowPrompt(true);
      } else {
        addLog('ℹ️ No updates available');
        Alert.alert('No Updates', 'Your app is up to date!');
      }
    } catch (error) {
      addLog(`❌ Error checking updates: ${error}`);
      Alert.alert('Error', 'Failed to check for updates');
    } finally {
      setIsChecking(false);
    }
  };

  const startUpdateFlow = async () => {
    if (!updateInfo) return;

    try {
      addLog(`Starting ${updateInfo.updateType} update flow...`);
      const success = await comprehensiveUpdateManager.startUpdateFlow(updateInfo);
      
      if (success) {
        addLog('✅ Update flow started successfully');
        setShowPrompt(false);
      } else {
        addLog('❌ Failed to start update flow');
      }
    } catch (error) {
      addLog(`❌ Error starting update: ${error}`);
    }
  };

  const completeFlexibleUpdate = async () => {
    try {
      addLog('Completing flexible update...');
      const success = await comprehensiveUpdateManager.completeFlexibleUpdate();
      
      if (success) {
        addLog('✅ App will restart to complete update');
      } else {
        addLog('❌ Failed to complete update');
      }
    } catch (error) {
      addLog(`❌ Error completing update: ${error}`);
    }
  };

  const getCurrentVersion = () => {
    const version = comprehensiveUpdateManager.getCurrentVersion();
    addLog(`Current version: ${version.version} (${version.versionCode})`);
    Alert.alert(
      'Current Version',
      `Version: ${version.version}\nBuild: ${version.versionCode}`
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Text style={[styles.title, { color: colors.text }]}>
        In-App Update Manager Demo
      </Text>

      {/* Status */}
      <View style={[styles.statusContainer, { borderColor: colors.border }]}>
        <View style={styles.statusRow}>
          <Ionicons 
            name={isInitialized ? "checkmark-circle" : "close-circle"} 
            size={20} 
            color={isInitialized ? colors.success : colors.error} 
          />
          <Text style={[styles.statusText, { color: colors.text }]}>
            Manager: {isInitialized ? 'Initialized' : 'Not Initialized'}
          </Text>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <Pressable
          style={[styles.button, { backgroundColor: colors.primary }]}
          onPress={() => checkForUpdates(false)}
          disabled={!isInitialized || isChecking}
        >
          {isChecking ? (
            <ActivityIndicator color="#ffffff" size="small" />
          ) : (
            <Ionicons name="refresh" size={20} color="#ffffff" />
          )}
          <Text style={styles.buttonText}>Check Updates</Text>
        </Pressable>

        <Pressable
          style={[styles.button, { backgroundColor: colors.warning }]}
          onPress={() => checkForUpdates(true)}
          disabled={!isInitialized || isChecking}
        >
          <Ionicons name="refresh-circle" size={20} color="#ffffff" />
          <Text style={styles.buttonText}>Force Check</Text>
        </Pressable>

        <Pressable
          style={[styles.button, { backgroundColor: colors.success }]}
          onPress={completeFlexibleUpdate}
          disabled={!isInitialized}
        >
          <Ionicons name="download-done" size={20} color="#ffffff" />
          <Text style={styles.buttonText}>Complete Update</Text>
        </Pressable>

        <Pressable
          style={[styles.button, { backgroundColor: colors.textSecondary }]}
          onPress={getCurrentVersion}
        >
          <Ionicons name="information-circle" size={20} color="#ffffff" />
          <Text style={styles.buttonText}>Version Info</Text>
        </Pressable>
      </View>

      {/* Logs */}
      <View style={[styles.logsContainer, { borderColor: colors.border }]}>
        <Text style={[styles.logsTitle, { color: colors.text }]}>Activity Logs</Text>
        <ScrollView style={styles.logsScroll} showsVerticalScrollIndicator={false}>
          {logs.map((log, index) => (
            <Text key={index} style={[styles.logText, { color: colors.textSecondary }]}>
              {log}
            </Text>
          ))}
          {logs.length === 0 && (
            <Text style={[styles.logText, { color: colors.textSecondary, fontStyle: 'italic' }]}>
              No activity yet...
            </Text>
          )}
        </ScrollView>
      </View>

      {/* Update Prompt */}
      {showPrompt && updateInfo && (
        <PlayStoreUpdatePrompt
          visible={showPrompt}
          updateInfo={{
            version: updateInfo.version || `Build ${updateInfo.versionCode}`,
            versionCode: updateInfo.versionCode || 0,
            mandatory: updateInfo.mandatory || false,
            releaseNotes: updateInfo.releaseNotes,
            updateType: updateInfo.updateType,
          }}
          onUpdate={startUpdateFlow}
          onDismiss={updateInfo.updateType !== 'immediate' ? () => setShowPrompt(false) : undefined}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 20,
  },
  statusContainer: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '500',
  },
  buttonContainer: {
    gap: 12,
    marginBottom: 20,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  logsContainer: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
  },
  logsTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  logsScroll: {
    flex: 1,
  },
  logText: {
    fontSize: 12,
    fontFamily: 'monospace',
    marginBottom: 4,
    lineHeight: 16,
  },
});
