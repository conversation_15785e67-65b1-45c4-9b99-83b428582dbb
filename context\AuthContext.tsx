import { Session, User } from '@supabase/supabase-js';
import { router } from 'expo-router';
import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { supabase } from '../lib/supabase';

type Profile = {
  id: string;
  user_id: string;
  full_name: string | null;
  email: string | null;
  city: string | null;
  role: string | null;
  phone_number: string | null;
  profile_image_url: string | null;
};

type AuthContextType = {
  session: Session | null;
  user: User | null;
  profile: Profile | null;
  loading: boolean;
  signOut: () => Promise<void>;
  refreshProfile: () => Promise<void>;
  clearProfile: () => void;
  isProfileComplete: () => boolean;
  checkIfProfileNeeded: () => Promise<boolean>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const initialized = useRef(false);
  const hasRedirected = useRef(false);

  // Helper function to check if profile is complete
  const isProfileComplete = (): boolean => {
    return !!profile && !!profile.full_name;
  };
  
  // Check if user needs to create or complete a profile
  const checkIfProfileNeeded = async (): Promise<boolean> => {
    if (!user) return false;
    
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();
        
      if (error || !data) {
        // No profile found, need to create one
        return true;
      }
      
      // Profile exists but may be incomplete
      return !data.full_name;
    } catch (error) {
      console.error('Error checking profile status:', error);
      return false;
    }
  };

  async function refreshProfile() {
    try {
      if (!user) {
        console.log('No user available, cannot refresh profile');
        return;
      }

      console.log('Refreshing profile for user:', user.id);
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.log('Profile lookup error:', error.message);
        console.log('No profile found for user, setting profile to null');
        setProfile(null);
        return;
      }

      if (!data) {
        console.log('No profile data returned, setting profile to null');
        setProfile(null);
        return;
      }

      console.log('Profile data loaded successfully:', data.id);
      setProfile(data as Profile);
    } catch (error) {
      console.error('Error in refreshProfile:', error);
      setProfile(null);
    }
  }

  // Function to explicitly clear the profile data
  function clearProfile() {
    console.log('AuthContext: Explicitly clearing profile');
    setProfile(null);
  }

  async function signOut() {
    try {
      console.log('AuthContext: Signing out user');
      // Clear profile first
      clearProfile();
      
      // First navigate away from protected routes
      router.replace('/auth/phone');
      
      // Then perform the sign out
      await supabase.auth.signOut();
      console.log('AuthContext: User signed out successfully');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  }

  const handleRouting = async () => {
    // Avoid multiple redirects
    if (hasRedirected.current) {
      return;
    }

    // If still loading, don't redirect yet
    if (loading) {
      return;
    }

    hasRedirected.current = true;

    try {
      if (!session) {
        // No authenticated session, go to auth flow
        console.log('AuthContext: No session, redirecting to auth flow');
        router.replace('/auth/phone');
        return;
      }

      if (!profile) {
        // User logged in but no profile, go to profile creation
        console.log('AuthContext: Has session but no profile, navigating to profile creation');
        router.replace('/auth/create-profile');
        return;
      }

      // User is fully authenticated with profile
      console.log('AuthContext: User authenticated with profile, navigating to main app');
      router.replace('/(tabs)');
    } catch (error) {
      console.error('Error during routing:', error);
    }
  };

  useEffect(() => {
    // Don't auto-redirect when loading
    if (loading) {
      return;
    }
    
    handleRouting();
  }, [session, profile, loading]);

  useEffect(() => {
    // Prevent multiple initializations
    if (initialized.current) return;
    initialized.current = true;
    
    console.log('AuthContext: Initializing...');
    
    const initializeAuth = async () => {
      try {
        // First, get the current session
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          throw sessionError;
        }
        
        const currentSession = sessionData?.session;
        console.log('AuthContext: Session status:', currentSession ? 'Authenticated' : 'Unauthenticated');
        
        // Validate the session is still active and not expired
        if (currentSession) {
          // Check if session is expired or will expire very soon
          const nowTime = Math.floor(Date.now() / 1000);
          if (currentSession.expires_at && currentSession.expires_at <= nowTime) {
            console.log('AuthContext: Session has expired, signing out');
            await supabase.auth.signOut();
            setSession(null);
            setUser(null);
            setProfile(null);
            setLoading(false);
            return;
          }
          
          // Verify the session is still valid by attempting to get the user
          try {
            const { error: verifyError } = await supabase.auth.getUser();
            if (verifyError) {
              console.log('AuthContext: Session verification failed, signing out', verifyError.message);
              await supabase.auth.signOut();
              setSession(null);
              setUser(null);
              setProfile(null);
              setLoading(false);
              return;
            }
          } catch (verifyErr) {
            console.error('Error verifying session:', verifyErr);
            await supabase.auth.signOut();
            setSession(null);
            setUser(null);
            setProfile(null);
            setLoading(false);
            return;
          }
        }
        
        // Update the session and user state
        setSession(currentSession);
        setUser(currentSession?.user ?? null);
        
        // If we have a session, also get the user profile
        if (currentSession?.user) {
          try {
            const { data: profileData, error: profileError } = await supabase
              .from('profiles')
              .select('*')
              .eq('user_id', currentSession.user.id)
              .single();
              
            if (!profileError && profileData) {
              console.log('Profile found for user');
              setProfile(profileData as Profile);
            } else {
              console.log('No profile found for user');
              setProfile(null);
            }
          } catch (err) {
            console.error('Error fetching profile:', err);
            setProfile(null);
          }
        }
        
        // Authentication initialization complete
        setLoading(false);
        
      } catch (error) {
        console.error('Authentication initialization error:', error);
        // Ensure we clear all auth state on any error and sign out
        try {
          await supabase.auth.signOut();
        } catch (signOutError) {
          console.error('Error signing out during error handling:', signOutError);
        }
        setSession(null);
        setUser(null);
        setProfile(null);
        setLoading(false);
      }
    };
    
    // Initialize auth state
    initializeAuth();
    
    // Set up the auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, currentSession) => {
        console.log('AuthContext: Auth state changed', { 
          event,
          hasSession: !!currentSession
        });
        
        // Always update session and user state
        setSession(currentSession);
        setUser(currentSession?.user ?? null);
        
        // Reset the redirection flag on new auth events
        hasRedirected.current = false;
        
        if (event === 'SIGNED_IN') {
          // Set loading true while we fetch profile
          setLoading(true);
          
          if (currentSession?.user) {
            try {
              const { data, error } = await supabase
                .from('profiles')
                .select('*')
                .eq('user_id', currentSession.user.id)
                .single();
                
              if (!error && data) {
                console.log('Profile found after sign-in');
                setProfile(data as Profile);
              } else {
                console.log('No profile found after sign-in');
                setProfile(null);
              }
            } catch (err) {
              console.error('Error fetching profile after sign-in:', err);
              setProfile(null);
            } finally {
              setLoading(false);
            }
          } else {
            setLoading(false);
          }
        } else if (event === 'SIGNED_OUT') {
          // Clear profile and ensure loading is false
          setProfile(null);
          setLoading(false);
        }
      }
    );
    
    // Clean up subscription
    return () => {
      console.log('Cleaning up auth subscription');
      subscription.unsubscribe();
    };
  }, []);

  const value = {
    session,
    user,
    profile,
    loading,
    signOut,
    refreshProfile,
    clearProfile,
    isProfileComplete,
    checkIfProfileNeeded,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 