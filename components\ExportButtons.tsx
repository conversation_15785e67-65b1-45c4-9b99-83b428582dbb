import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { Text } from 'react-native-paper';

type ExportButtonsProps = {
  onPdfExport: () => void;
  onExcelExport: () => void;
};

const ExportButtons: React.FC<ExportButtonsProps> = ({ onPdfExport, onExcelExport }) => {
  return (
    <View style={styles.container}>
      <TouchableOpacity style={[styles.button, styles.pdfButton]} onPress={onPdfExport}>
        <Ionicons name="document-text" size={20} color="white" />
        <Text style={styles.buttonText}>PDF</Text>
      </TouchableOpacity>
      <TouchableOpacity style={[styles.button, styles.excelButton]} onPress={onExcelExport}>
        <Ionicons name="grid" size={20} color="white" />
        <Text style={styles.buttonText}>Excel</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    gap: 10,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 8,
  },
  pdfButton: {
    backgroundColor: '#ef4444',
  },
  excelButton: {
    backgroundColor: '#16a34a',
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
  },
});

export default ExportButtons; 