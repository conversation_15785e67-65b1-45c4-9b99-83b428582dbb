package com.infratasks.app

import android.app.Activity
import android.content.IntentSender
import com.facebook.react.bridge.*
import com.facebook.react.modules.core.DeviceEventManagerModule
import com.google.android.play.core.appupdate.AppUpdateInfo
import com.google.android.play.core.appupdate.AppUpdateManager
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.appupdate.AppUpdateOptions
import com.google.android.play.core.install.InstallState
import com.google.android.play.core.install.InstallStateUpdatedListener
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.InstallStatus
import com.google.android.play.core.install.model.UpdateAvailability
import com.google.android.play.core.tasks.Task

class InAppUpdateModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    private var appUpdateManager: AppUpdateManager? = null
    private val UPDATE_REQUEST_CODE = 500
    
    private val installStateUpdatedListener = InstallStateUpdatedListener { state ->
        when (state.installStatus()) {
            InstallStatus.DOWNLOADED -> {
                // Update downloaded, notify JS
                sendEvent("onUpdateDownloaded", null)
            }
            InstallStatus.DOWNLOADING -> {
                val progress = Arguments.createMap().apply {
                    putDouble("progress", state.bytesDownloaded().toDouble() / state.totalBytesToDownload().toDouble())
                }
                sendEvent("onUpdateDownloading", progress)
            }
            InstallStatus.FAILED -> {
                sendEvent("onUpdateFailed", Arguments.createMap().apply {
                    putString("error", "Update installation failed")
                })
            }
            InstallStatus.INSTALLED -> {
                sendEvent("onUpdateInstalled", null)
            }
            else -> {
                // Handle other states if needed
            }
        }
    }

    override fun getName(): String {
        return "InAppUpdateModule"
    }

    @ReactMethod
    fun initialize(promise: Promise) {
        try {
            appUpdateManager = AppUpdateManagerFactory.create(reactApplicationContext)
            appUpdateManager?.registerListener(installStateUpdatedListener)
            promise.resolve(true)
        } catch (e: Exception) {
            promise.reject("INIT_ERROR", "Failed to initialize app update manager", e)
        }
    }

    @ReactMethod
    fun checkForUpdate(promise: Promise) {
        val appUpdateManager = this.appUpdateManager
        if (appUpdateManager == null) {
            promise.reject("NOT_INITIALIZED", "App update manager not initialized")
            return
        }

        val appUpdateInfoTask: Task<AppUpdateInfo> = appUpdateManager.appUpdateInfo

        appUpdateInfoTask.addOnSuccessListener { appUpdateInfo ->
            val result = Arguments.createMap()

            // Add detailed logging for debugging
            val currentVersionCode = try {
                reactApplicationContext.packageManager.getPackageInfo(
                    reactApplicationContext.packageName, 0
                ).versionCode
            } catch (e: Exception) {
                0
            }

            result.putInt("currentVersionCode", currentVersionCode)

            when (appUpdateInfo.updateAvailability()) {
                UpdateAvailability.UPDATE_AVAILABLE -> {
                    result.putBoolean("updateAvailable", true)
                    result.putInt("availableVersionCode", appUpdateInfo.availableVersionCode())
                    result.putBoolean("immediateUpdateAllowed",
                        appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE))
                    result.putBoolean("flexibleUpdateAllowed",
                        appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.FLEXIBLE))
                    result.putInt("updatePriority", appUpdateInfo.updatePriority())
                    result.putInt("clientVersionStalenessDays", appUpdateInfo.clientVersionStalenessDays() ?: 0)

                    // Log detailed information for debugging
                    android.util.Log.d("InAppUpdate", "Update available: current=$currentVersionCode, available=${appUpdateInfo.availableVersionCode()}")
                }
                UpdateAvailability.UPDATE_NOT_AVAILABLE -> {
                    result.putBoolean("updateAvailable", false)
                    android.util.Log.d("InAppUpdate", "No update available: current=$currentVersionCode")
                }
                UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS -> {
                    result.putBoolean("updateAvailable", true)
                    result.putBoolean("updateInProgress", true)
                    android.util.Log.d("InAppUpdate", "Update in progress")
                }
                else -> {
                    result.putBoolean("updateAvailable", false)
                    android.util.Log.d("InAppUpdate", "Update availability: ${appUpdateInfo.updateAvailability()}")
                }
            }

            promise.resolve(result)
        }.addOnFailureListener { exception ->
            android.util.Log.e("InAppUpdate", "Failed to check for updates", exception)
            promise.reject("CHECK_ERROR", "Failed to check for updates: ${exception.message}", exception)
        }
    }

    @ReactMethod
    fun startImmediateUpdate(promise: Promise) {
        startUpdate(AppUpdateType.IMMEDIATE, promise)
    }

    @ReactMethod
    fun startFlexibleUpdate(promise: Promise) {
        startUpdate(AppUpdateType.FLEXIBLE, promise)
    }

    private fun startUpdate(updateType: Int, promise: Promise) {
        val appUpdateManager = this.appUpdateManager
        if (appUpdateManager == null) {
            promise.reject("NOT_INITIALIZED", "App update manager not initialized")
            return
        }

        val activity = currentActivity
        if (activity == null) {
            promise.reject("NO_ACTIVITY", "No current activity available")
            return
        }

        val appUpdateInfoTask: Task<AppUpdateInfo> = appUpdateManager.appUpdateInfo

        appUpdateInfoTask.addOnSuccessListener { appUpdateInfo ->
            if (appUpdateInfo.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE &&
                appUpdateInfo.isUpdateTypeAllowed(updateType)) {
                
                try {
                    appUpdateManager.startUpdateFlowForResult(
                        appUpdateInfo,
                        activity,
                        AppUpdateOptions.newBuilder(updateType).build(),
                        UPDATE_REQUEST_CODE
                    )
                    promise.resolve(true)
                } catch (e: IntentSender.SendIntentException) {
                    promise.reject("UPDATE_ERROR", "Failed to start update flow", e)
                }
            } else {
                promise.reject("UPDATE_NOT_AVAILABLE", "Update not available or not allowed for this type")
            }
        }.addOnFailureListener { exception ->
            promise.reject("UPDATE_ERROR", "Failed to start update", exception)
        }
    }

    @ReactMethod
    fun completeFlexibleUpdate(promise: Promise) {
        val appUpdateManager = this.appUpdateManager
        if (appUpdateManager == null) {
            promise.reject("NOT_INITIALIZED", "App update manager not initialized")
            return
        }

        try {
            appUpdateManager.completeUpdate()
            promise.resolve(true)
        } catch (e: Exception) {
            promise.reject("COMPLETE_ERROR", "Failed to complete update", e)
        }
    }

    @ReactMethod
    fun getUpdateInfo(promise: Promise) {
        val appUpdateManager = this.appUpdateManager
        if (appUpdateManager == null) {
            promise.reject("NOT_INITIALIZED", "App update manager not initialized")
            return
        }

        val appUpdateInfoTask: Task<AppUpdateInfo> = appUpdateManager.appUpdateInfo

        appUpdateInfoTask.addOnSuccessListener { appUpdateInfo ->
            val result = Arguments.createMap()
            result.putInt("availableVersionCode", appUpdateInfo.availableVersionCode())
            result.putInt("updateAvailability", appUpdateInfo.updateAvailability())
            result.putBoolean("immediateUpdateAllowed", 
                appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE))
            result.putBoolean("flexibleUpdateAllowed", 
                appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.FLEXIBLE))
            result.putInt("updatePriority", appUpdateInfo.updatePriority())
            result.putInt("clientVersionStalenessDays", appUpdateInfo.clientVersionStalenessDays() ?: 0)
            
            promise.resolve(result)
        }.addOnFailureListener { exception ->
            promise.reject("INFO_ERROR", "Failed to get update info", exception)
        }
    }

    private fun sendEvent(eventName: String, params: WritableMap?) {
        reactApplicationContext
            .getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
            .emit(eventName, params)
    }

    override fun onCatalystInstanceDestroy() {
        super.onCatalystInstanceDestroy()
        appUpdateManager?.unregisterListener(installStateUpdatedListener)
    }
}
