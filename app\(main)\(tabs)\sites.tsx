import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useSubscriptionAccess } from '@/hooks/useSubscriptionAccess';
import { supabase } from '@/lib/supabase';
import { Feather, Ionicons } from '@expo/vector-icons';
import { useIsFocused } from '@react-navigation/native';
import { format } from 'date-fns';
import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useCallback, useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Dimensions, FlatList, Image, RefreshControl, StyleSheet, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type Site = {
  id: string;
  name: string;
  organization_name: string;
  site_image_url: string | null;
  created_at: string;
  updated_at: string;
  status: 'active' | 'completed';
};

const { width, height } = Dimensions.get('window');

// Subscription popup styles
const subscriptionStyles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  popup: {
    width: Math.min(width - 40, 400),
    maxHeight: height * 0.8,
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  featuresList: {
    width: '100%',
    marginBottom: 32,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureText: {
    fontSize: 14,
    marginLeft: 12,
    flex: 1,
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  subscribeButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  subscribeButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default function SitesScreen() {
  const insets = useSafeAreaInsets();
  const [sites, setSites] = useState<Site[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [showSubscriptionPopup, setShowSubscriptionPopup] = useState(false);
  const [lastRefreshTime, setLastRefreshTime] = useState(0);
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const isFocused = useIsFocused();
  const subscriptionRef = useRef<any>(null);
  const refreshTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  
  // Use the new subscription access hook
  const { 
    isSubscribed, 
    isExpired, 
    loading: subscriptionLoading,
    refreshSubscription 
  } = useSubscriptionAccess();

  // Show access denied message for expired subscriptions
  const renderAccessDenied = () => (
    <ThemedView style={styles.accessDeniedContainer}>
      <Ionicons 
        name="lock-closed" 
        size={60} 
        color={colorScheme === 'dark' ? '#ef4444' : '#dc2626'} 
      />
      <ThemedText style={styles.accessDeniedTitle}>
        Access Restricted
      </ThemedText>
      <ThemedText style={styles.accessDeniedText}>
        {isExpired() ? 
          'Your subscription has expired. Please renew your subscription to access Sites.' :
          'You need an active subscription to access Sites.'
        }
      </ThemedText>
      <TouchableOpacity
        style={[styles.subscribeButton, { 
          backgroundColor: colors.primary
        }]}
        onPress={() => router.push('/(tabs)/subscription')}
      >
        <ThemedText style={[styles.subscribeButtonText, { color: 'white' }]}>
          {isExpired() ? 'Renew Subscription' : 'Get Subscription'}
        </ThemedText>
      </TouchableOpacity>
    </ThemedView>
  );

  // Function to load sites data
  const loadSites = async () => {
    console.log("Starting to load sites data");
    try {
      const { data: userData, error: userError } = await supabase.auth.getUser();
      
      if (userError) {
        console.error('Error getting user:', userError);
        return;
      }
      
      if (!userData.user) {
        console.log('No authenticated user found');
        return;
      }
      
      console.log("User authenticated, getting profile ID");
      
      // Get user profile ID
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('id')
        .eq('user_id', userData.user.id)
        .single();
      
      if (profileError) {
        console.error('Error fetching profile:', profileError);
        return;
      }
      
      if (!profileData) {
        console.log('No profile found for user');
        return;
      }
      
      console.log("Profile found, fetching sites");
      
      // First, get site IDs where user is a member
      const { data: memberSites, error: memberError } = await supabase
        .from('site_members')
        .select('site_id')
        .eq('user_id', userData.user.id);
        
      if (memberError) {
        console.error('Error fetching site memberships:', memberError);
      }
      
      // Extract site IDs to an array
      const siteIds = memberSites ? memberSites.map(site => site.site_id) : [];
      console.log("User is a member of these sites:", siteIds);
      
      // Get sites where user is either the owner or a member
      let query = supabase
        .from('sites')
        .select('*');
        
      if (siteIds.length > 0) {
        // User is a member of some sites
        query = query.or(`owner_id.eq.${profileData.id},id.in.(${siteIds.join(',')})`);
      } else {
        // User is only an owner
        query = query.eq('owner_id', profileData.id);
      }
      
      // Execute the query with order
      const { data, error } = await query.order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error loading sites:', error);
        return;
      }
      
      console.log("Sites data retrieved:", data ? data.length : 0, "sites found");
      
      setSites(data || []);
    } catch (error) {
      console.error('Unexpected error loading sites:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Set up real-time subscription
  useEffect(() => {
    const setupSubscription = async () => {
      console.log("Setting up real-time subscription");
      const { data: userData } = await supabase.auth.getUser();
      
      if (!userData.user) {
        console.log("No authenticated user found, can't set up subscription");
        return;
      }
      
      // Get profile info to help with subscription
      const { data: profileData } = await supabase
        .from('profiles')
        .select('id')
        .eq('user_id', userData.user.id)
        .single();
        
      if (!profileData) {
        console.log("No profile found, can't set up subscription properly");
      }
      
      // Subscribe to changes in both sites and site_members tables
      try {
        // First remove any existing subscription to avoid duplicates
        if (subscriptionRef.current) {
          console.log("Removing existing subscription");
          supabase.removeChannel(subscriptionRef.current);
        }
        
        console.log("Creating new subscription channel");
        
        // Create a unique channel name to prevent conflicts
        const channelName = `sites-and-members-changes-${userData.user.id}-${Date.now()}`;

        // Create a new channel with both tables AND user_subscriptions for immediate access
        subscriptionRef.current = supabase
          .channel(channelName)
          .on('postgres_changes', {
            event: '*',
            schema: 'public',
            table: 'sites'
          }, (payload) => {
            console.log("Sites table changed:", payload);
            loadSites();
          })
          .on('postgres_changes', {
            event: '*',
            schema: 'public',
            table: 'site_members',
            filter: `user_id=eq.${userData.user.id}`
          }, (payload) => {
            console.log("Site members table changed for current user:", payload);
            loadSites();
          })
          .on('postgres_changes', {
            event: '*',
            schema: 'public',
            table: 'user_subscriptions',
            filter: `user_id=eq.${userData.user.id}`
          }, (payload) => {
            console.log("User subscription changed:", payload);
            // Refresh subscription status immediately for instant access
            if (refreshSubscription) {
              refreshSubscription();
            }
            // Also reload sites if user now has access
            setTimeout(() => {
              if (isSubscribed()) {
                console.log("Subscription activated, loading sites");
                loadSites();
              }
            }, 500);
          })
          .subscribe((status) => {
            console.log("Subscription status:", status);
          });
        
        console.log("Subscription setup complete");
      } catch (error) {
        console.error("Error setting up real-time subscription:", error);
      }
    };
    
    setupSubscription();
    
    return () => {
      if (subscriptionRef.current) {
        console.log("Cleaning up subscription");
        supabase.removeChannel(subscriptionRef.current);
      }
    };
  }, [refreshSubscription, isSubscribed]);

  // Load sites when screen focuses and user has access
  useEffect(() => {
    if (isFocused && isSubscribed()) {
      console.log("Screen focused and user has access, loading sites");
      loadSites();
    }
  }, [isFocused, isSubscribed]);

  // Pull to refresh handler
  const onRefresh = useCallback(() => {
    if (isSubscribed()) {
    setRefreshing(true);
    loadSites();
    }
    // Refresh subscription status
    if (refreshSubscription) {
      refreshSubscription();
    }
  }, [isSubscribed, refreshSubscription]);

  // Render a site card
  const renderSiteCard = ({ item }: { item: Site }) => {
    const defaultImage = 'https://images.unsplash.com/photo-1541888946425-d81bb19240f5?q=80&w=2070&auto=format&fit=crop';
    
    return (
      <TouchableOpacity
        style={[
          styles.siteCard,
          { backgroundColor: colorScheme === 'dark' ? '#262626' : '#fff' }
        ]}
        onPress={() => {
          router.push({
            pathname: "/site/[id]",
            params: { id: item.id }
          });
        }}
      >
        <Image
          source={{ uri: item.site_image_url || defaultImage }}
          style={styles.siteImage}
          resizeMode="cover"
        />
        <View style={styles.siteContent}>
          <View style={styles.siteHeader}>
            <ThemedText style={styles.siteName}>{item.name}</ThemedText>
            <View style={[
              styles.statusBadge,
              { backgroundColor: item.status === 'active' ? '#dcfce7' : '#f1f5f9' }
            ]}>
              <ThemedText style={[
                styles.statusText,
                { color: item.status === 'active' ? '#166534' : '#475569' }
              ]}>
                {item.status === 'active' ? 'Active' : 'Completed'}
              </ThemedText>
            </View>
          </View>
          
          <ThemedText style={styles.organizationName}>{item.organization_name}</ThemedText>
          
          <View style={styles.dateContainer}>
            <View style={styles.dateItem}>
              <Feather name="calendar" size={14} color={colorScheme === 'dark' ? '#94a3b8' : '#64748b'} />
              <ThemedText style={styles.dateText}>
                Created: {format(new Date(item.created_at), 'dd MMM yyyy')}
              </ThemedText>
            </View>
            
            <View style={styles.dateItem}>
              <Feather name="clock" size={14} color={colorScheme === 'dark' ? '#94a3b8' : '#64748b'} />
              <ThemedText style={styles.dateText}>
                Updated: {format(new Date(item.updated_at), 'dd MMM yyyy')}
              </ThemedText>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  // Main render function
  const renderMainContent = () => {
    // Show loading while checking subscription
    if (subscriptionLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <ThemedText style={styles.loadingText}>Checking subscription...</ThemedText>
        </View>
      );
    }

    // Show access denied if user doesn't have subscription access
    if (!isSubscribed()) {
      return renderAccessDenied();
    }

    // Show main content if user has access
    return (
      <View style={styles.mainContent}>
        <View style={[styles.header, { marginTop: 10 }]}>
          <ThemedText style={styles.headerTitle}>Sites</ThemedText>
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: colors.primary }]}
            onPress={() => router.push('/create-site')}
          >
            <Ionicons name="add" size={24} color="white" />
          </TouchableOpacity>
        </View>
    
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <ThemedText style={styles.loadingText}>Loading sites...</ThemedText>
          </View>
        ) : sites.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons 
              name="business-outline" 
              size={80} 
              color={colorScheme === 'dark' ? '#6b7280' : '#9ca3af'} 
            />
            <ThemedText style={styles.emptyTitle}>No sites yet</ThemedText>
            <ThemedText style={styles.emptyText}>
              Create your first construction site to get started
            </ThemedText>
            <TouchableOpacity
              style={[styles.createFirstButton, { 
                backgroundColor: colors.primary
              }]}
              onPress={() => router.push('/create-site')}
            >
              <ThemedText style={[styles.createFirstButtonText, { color: 'white' }]}>
                Create First Site
              </ThemedText>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={sites}
            renderItem={renderSiteCard}
            keyExtractor={(item) => item.id}
            refreshControl={
              <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
            }
            contentContainerStyle={styles.listContainer}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    );
  };

  return (
    <ThemedView style={[styles.container, { paddingTop: insets.top }]}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      {renderMainContent()}
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  accessDeniedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  accessDeniedTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 12,
    textAlign: 'center',
  },
  accessDeniedText: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  subscribeButton: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
    alignItems: 'center',
  },
  subscribeButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  mainContent: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: 'bold',
  },
  addButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    opacity: 0.7,
  },
  createFirstButton: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
  },
  createFirstButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  listContainer: {
    paddingBottom: 20,
  },
  siteCard: {
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  siteImage: {
    width: '100%',
    height: 200,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  siteContent: {
    padding: 16,
  },
  siteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  siteName: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
    marginRight: 12,
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  organizationName: {
    fontSize: 14,
    opacity: 0.7,
    marginBottom: 12,
  },
  dateContainer: {
    gap: 4,
  },
  dateItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  dateText: {
    fontSize: 12,
    opacity: 0.6,
  },
}); 