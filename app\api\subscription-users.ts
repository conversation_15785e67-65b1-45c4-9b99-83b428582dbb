import { supabase } from '@/lib/supabase';

export interface SubscriptionUser {
  id: string;
  subscription_id: string;
  user_id: string;
  role: string;
  status: 'active' | 'invited' | 'removed';
  invited_by?: string;
  invited_at?: string;
  joined_at?: string;
  email?: string;
  full_name?: string;
}

export interface AddUserRequest {
  subscriptionId: string;
  phone: string;
  invitedBy: string;
  role?: string;
}

export interface UserProfile {
  user_id: string;
  email: string;
  full_name?: string;
  phone_number?: string;
}

/**
 * Fetch all subscription users for a given subscription
 */
export async function fetchSubscriptionUsers(subscriptionId: string): Promise<SubscriptionUser[]> {
  try {
    const { data: usersData, error: usersError } = await supabase
      .from('subscription_users')
      .select(`
        id,
        subscription_id,
        user_id,
        role,
        status,
        invited_by,
        invited_at,
        joined_at
      `)
      .eq('subscription_id', subscriptionId)
      .eq('status', 'active'); // Only fetch active users

    if (usersError) {
      console.error('Error fetching subscription users:', usersError);
      throw new Error('Failed to fetch subscription users');
    }

    if (!usersData || usersData.length === 0) {
      return [];
    }

    // Get user profiles for the subscription users
    const userIds = usersData.map(user => user.user_id);
    const { data: profilesData, error: profilesError } = await supabase
      .from('profiles')
      .select('user_id, email, full_name, phone_number')
      .in('user_id', userIds);

    if (profilesError) {
      console.error('Error fetching user profiles:', profilesError);
      // Continue without profile data
    }

    // Combine user data with profile data
    const usersWithProfiles: SubscriptionUser[] = usersData.map(user => ({
      ...user,
      email: profilesData?.find(p => p.user_id === user.user_id)?.email || '',
      full_name: profilesData?.find(p => p.user_id === user.user_id)?.full_name || '',
      phone: profilesData?.find(p => p.user_id === user.user_id)?.phone_number || ''
    }));

    return usersWithProfiles;
  } catch (error) {
    console.error('Error in fetchSubscriptionUsers:', error);
    throw error;
  }
}

/**
 * Find user profile by phone number
 */
export async function findUserByPhone(phone: string): Promise<UserProfile | null> {
  try {
    console.log('findUserByPhone: Searching for phone:', phone.trim());

    // Create phone variations to try (database stores as 91xxxxxxxxxx format without +)
    const phoneVariations = [
      phone.trim(), // Original input
      phone.trim().replace(/\s/g, ''), // Remove spaces
      phone.trim().replace(/[^\d]/g, ''), // Keep only digits
      phone.trim().replace(/^\+/, ''), // Remove leading +
      phone.trim().replace(/^\+91/, '91'), // Convert +91 to 91
    ];

    // If it's a 10-digit number, add 91 prefix (database format)
    const cleanPhone = phone.trim().replace(/[^\d]/g, '');
    if (/^[6-9]\d{9}$/.test(cleanPhone)) {
      phoneVariations.push(`91${cleanPhone}`);
    }

    // Remove duplicates
    const uniqueVariations = [...new Set(phoneVariations)];

    console.log('findUserByPhone: Trying phone variations:', uniqueVariations);

    for (const phoneVar of uniqueVariations) {
      console.log('findUserByPhone: Trying variation:', phoneVar);

      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('user_id, email, full_name, phone_number')
        .eq('phone_number', phoneVar)
        .single();

      console.log('findUserByPhone: Query result for', phoneVar, ':', { profileData, profileError });

      if (profileError && profileError.code !== 'PGRST116') {
        console.error('Error finding user by phone:', profileError);
        continue; // Try next variation
      }

      if (profileData) {
        console.log('findUserByPhone: User found with variation:', phoneVar, profileData);
        return profileData;
      }
    }

    console.log('findUserByPhone: No user found with any phone variation');
    return null;
  } catch (error) {
    console.error('Error in findUserByPhone:', error);
    throw error;
  }
}

/**
 * Check if user is already in subscription
 */
export async function isUserInSubscription(subscriptionId: string, userId: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('subscription_users')
      .select('id')
      .eq('subscription_id', subscriptionId)
      .eq('user_id', userId)
      .eq('status', 'active')
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error checking user in subscription:', error);
      throw new Error('Error checking user subscription status');
    }

    return !!data;
  } catch (error) {
    console.error('Error in isUserInSubscription:', error);
    throw error;
  }
}

/**
 * Add user to subscription
 */
export async function addUserToSubscription(request: AddUserRequest): Promise<SubscriptionUser> {
  try {
    // First, find the user by phone
    const userProfile = await findUserByPhone(request.phone);
    if (!userProfile) {
      throw new Error('User not found with this phone number');
    }

    // Check if user is already in subscription
    const isAlreadyMember = await isUserInSubscription(request.subscriptionId, userProfile.user_id);
    if (isAlreadyMember) {
      throw new Error('User is already part of this subscription');
    }

    // Add user to subscription_users table
    const { data, error } = await supabase
      .from('subscription_users')
      .insert({
        subscription_id: request.subscriptionId,
        user_id: userProfile.user_id,
        role: request.role || 'member',
        invited_by: request.invitedBy,
        invited_at: new Date().toISOString(),
        joined_at: new Date().toISOString(),
        status: 'active'
      })
      .select()
      .single();

    if (error) {
      console.error('Error adding user to subscription:', error);
      throw new Error('Failed to add user to subscription');
    }

    // Note: Standard subscription creation is now handled automatically by database trigger
    // when a user is added to a premium subscription. See trigger_create_standard_subscription_on_user_add
    console.log('✅ User added to subscription. Database trigger will handle standard subscription creation if needed.');

    // Return the user with profile data
    return {
      ...data,
      email: userProfile.email,
      full_name: userProfile.full_name || '',
      phone: userProfile.phone_number || ''
    };
  } catch (error) {
    console.error('Error in addUserToSubscription:', error);
    throw error;
  }
}

/**
 * Remove user from subscription (soft delete)
 */
export async function removeUserFromSubscription(subscriptionId: string, userId: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('subscription_users')
      .update({
        status: 'removed',
        updated_at: new Date().toISOString()
      })
      .eq('subscription_id', subscriptionId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error removing user from subscription:', error);
      throw new Error('Failed to remove user from subscription');
    }
  } catch (error) {
    console.error('Error in removeUserFromSubscription:', error);
    throw error;
  }
}

/**
 * Get active user count for a subscription
 */
export async function getActiveUserCount(subscriptionId: string): Promise<number> {
  try {
    const { count, error } = await supabase
      .from('subscription_users')
      .select('*', { count: 'exact', head: true })
      .eq('subscription_id', subscriptionId)
      .eq('status', 'active');

    if (error) {
      console.error('Error getting active user count:', error);
      throw new Error('Failed to get user count');
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getActiveUserCount:', error);
    throw error;
  }
}

/**
 * Update user role in subscription
 */
export async function updateUserRole(subscriptionId: string, userId: string, newRole: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('subscription_users')
      .update({
        role: newRole,
        updated_at: new Date().toISOString()
      })
      .eq('subscription_id', subscriptionId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating user role:', error);
      throw new Error('Failed to update user role');
    }
  } catch (error) {
    console.error('Error in updateUserRole:', error);
    throw error;
  }
}

/**
 * Get subscription user limits and current usage
 */
export async function getSubscriptionUserLimits(subscriptionId: string, planId: string) {
  try {
    const activeUserCount = await getActiveUserCount(subscriptionId);

    // Define plan limits
    const planLimits = {
      standard: { included: 1, additionalPrice: 249 },
      premium: { included: 5, additionalPrice: 249 }
    };

    const limits = planLimits[planId as keyof typeof planLimits] || planLimits.premium;
    const totalUsers = activeUserCount + 1; // +1 for owner
    const additionalUsers = Math.max(0, totalUsers - limits.included);
    const additionalCost = additionalUsers * limits.additionalPrice;

    return {
      includedUsers: limits.included,
      activeUsers: activeUserCount,
      totalUsers,
      additionalUsers,
      additionalPrice: limits.additionalPrice,
      additionalCost,
      canAddMore: true // For premium, always true; for standard, check limits
    };
  } catch (error) {
    console.error('Error in getSubscriptionUserLimits:', error);
    throw error;
  }
}
