# Welcome to your Expo app 👋

This is an [Expo](https://expo.dev) project created with [`create-expo-app`](https://www.npmjs.com/package/create-expo-app).

## Get started

1. Install dependencies

   ```bash
   npm install
   ```

2. Start the app

   ```bash
   npx expo start
   ```

In the output, you'll find options to open the app in a

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).

## Get a fresh project

When you're ready, run:

```bash
npm run reset-project
```

This command will move the starter code to the **app-example** directory and create a blank **app** directory where you can start developing.

## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.

## Join the community

Join our community of developers creating universal apps.

- [Expo on GitHub](https://github.com/expo/expo): View our open source platform and contribute.
- [Discord community](https://chat.expo.dev): Chat with Expo users and ask questions.

## Database Setup for Attendance Feature

### Labor Categories Table

The attendance feature requires a properly configured `labor_categories` table with the following structure:

```sql
CREATE TABLE labor_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Row Level Security (RLS) Policies

For the labor categories feature to work correctly, you need to set up the following RLS policies:

1. Enable RLS on the table:
```sql
ALTER TABLE labor_categories ENABLE ROW LEVEL SECURITY;
```

2. Create policies for viewing, inserting, updating, and deleting:
```sql
-- View policy (allows all authenticated users to view categories)
CREATE POLICY "Labor categories are viewable by authenticated users" 
ON labor_categories
FOR SELECT 
USING (auth.role() = 'authenticated');

-- Insert policy (only super admins and admins can insert)
CREATE POLICY "Labor categories can be inserted by admins" 
ON labor_categories
FOR INSERT 
WITH CHECK (auth.role() = 'authenticated');

-- Update policy (only super admins and admins can update)
CREATE POLICY "Labor categories can be updated by admins" 
ON labor_categories
FOR UPDATE 
USING (auth.role() = 'authenticated')
WITH CHECK (auth.role() = 'authenticated');

-- Delete policy (only super admins and admins can delete)
CREATE POLICY "Labor categories can be deleted by admins" 
ON labor_categories
FOR DELETE 
USING (auth.role() = 'authenticated');
```

Note: If you need site-specific categories, consider adding a `site_id` column to the table:

```sql
ALTER TABLE labor_categories ADD COLUMN site_id UUID REFERENCES sites(id) ON DELETE CASCADE;
CREATE INDEX idx_labor_categories_site_id ON labor_categories(site_id);
```

Then follow the instructions in `scripts/fix_labor_categories_rls.sql` to set up site-specific permissions.

## PDF Report Generation Features

The InfraTask app includes comprehensive report generation capabilities for various types of project data:

### Task Reports
- Generate detailed PDF reports of tasks with subcategories and progress information
- Filter tasks by status, category, and date range
- Visual progress indicators for task completion
- Task report export available from both site dashboard and individual task details screens

### Attendance Reports
- Generate PDF and Excel reports of worker attendance
- Filter attendance by date range, worker status, and labor category
- Calculate total payouts based on attendance status and daily wages
- View detailed attendance records per worker

### Material Reports
- Generate inventory reports of materials with stock levels and specifications
- Filter materials by stock status and date range

### Common Features
- Separate category filters for different report types (tasks, attendance)
- Date range picker with validation to ensure proper date ranges
- Visual indicators for selected filters
- Clear buttons to reset filters
- Active filter display to show currently applied filters

These reporting features enable construction managers to generate detailed documentation for project stakeholders, track progress, and maintain accurate records of site activities.
