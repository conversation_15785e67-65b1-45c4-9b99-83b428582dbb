import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { router } from 'expo-router';
import React from 'react';
import { Dimensions, Modal, StyleSheet, TouchableOpacity, View } from 'react-native';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

const { width, height } = Dimensions.get('window');

interface SubscriptionPopupProps {
  visible: boolean;
  onClose: () => void;
  feature: 'Sites' | 'Transactions';
}

export const SubscriptionPopup: React.FC<SubscriptionPopupProps> = ({
  visible,
  onClose,
  feature,
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const getFeatureConfig = () => {
    switch (feature) {
      case 'Sites':
        return {
          icon: 'office-building' as const,
          title: 'Sites Management',
          description: 'Access to Sites requires an active subscription',
          features: [
            'Create unlimited construction sites',
            'Manage site members and teams',
            'Track site progress and status',
            'Site-specific task management'
          ]
        };
      case 'Transactions':
        return {
          icon: 'cash-multiple' as const,
          title: 'Transaction Tracking',
          description: 'Access to Transactions requires an active subscription',
          features: [
            'Track income and expenses',
            'Financial reporting and analytics',
            'Link transactions between users',
            'Export financial data'
          ]
        };
      default:
        return {
          icon: 'star' as const,
          title: 'Premium Feature',
          description: 'This feature requires an active subscription',
          features: ['Premium features', 'Advanced functionality']
        };
    }
  };

  const config = getFeatureConfig();

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <BlurView intensity={80} style={styles.blurView} />
        <ThemedView style={[
          styles.popup,
          { borderColor: colors.primary }
        ]}>
          {/* Close button */}
          <TouchableOpacity
            style={[
              styles.closeButton,
              { backgroundColor: colorScheme === 'dark' ? '#262626' : '#fff' }
            ]}
            onPress={onClose}
          >
            <Ionicons 
              name="close" 
              size={24} 
              color={colorScheme === 'dark' ? '#fff' : '#000'} 
            />
          </TouchableOpacity>

          {/* Icon */}
          <View style={[
            styles.iconContainer,
            { backgroundColor: `${colors.primary}20` }
          ]}>
            <MaterialCommunityIcons 
              name={config.icon} 
              size={48} 
              color={colors.primary} 
            />
          </View>

          {/* Title */}
          <ThemedText style={styles.title}>
            {config.title}
          </ThemedText>

          {/* Description */}
          <ThemedText style={[styles.description, { opacity: 0.8 }]}>
            {config.description}
          </ThemedText>

          {/* Features list */}
          <View style={styles.featuresList}>
            {config.features.map((featureText, index) => (
              <View key={index} style={styles.featureItem}>
                <Ionicons 
                  name="checkmark-circle" 
                  size={20} 
                  color={colors.primary} 
                />
                <ThemedText style={[styles.featureText, { opacity: 0.8 }]}>
                  {featureText}
                </ThemedText>
              </View>
            ))}
          </View>

          {/* Action buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[
                styles.subscribeButton,
                { backgroundColor: colors.primary }
              ]}
              onPress={() => {
                onClose();
                router.push('/(tabs)/subscription');
              }}
            >
              <ThemedText style={[styles.subscribeButtonText, { color: 'white' }]}>
                View Subscription Plans
              </ThemedText>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.cancelButton,
                { borderColor: colorScheme === 'dark' ? '#ffffff20' : '#00000020' }
              ]}
              onPress={onClose}
            >
              <ThemedText style={[styles.cancelButtonText, { opacity: 0.6 }]}>
                Maybe Later
              </ThemedText>
            </TouchableOpacity>
          </View>
        </ThemedView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  blurView: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  popup: {
    width: Math.min(width - 40, 400),
    maxHeight: height * 0.8,
    borderRadius: 20,
    borderWidth: 2,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 10,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  featuresList: {
    width: '100%',
    marginBottom: 32,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureText: {
    fontSize: 14,
    marginLeft: 12,
    flex: 1,
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  subscribeButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
  },
  subscribeButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    borderWidth: 1,
  },
  cancelButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
}); 