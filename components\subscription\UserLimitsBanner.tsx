import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { UserLimits } from '@/hooks/useSubscription';

export interface UserLimitsBannerProps {
  userLimits: UserLimits;
  planName?: string;
}

const UserLimitsBanner: React.FC<UserLimitsBannerProps> = ({
  userLimits,
  planName = 'Premium'
}) => {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const getProgressPercentage = () => {
    return Math.min((userLimits.totalUsers / userLimits.includedUsers) * 100, 100);
  };

  const getBannerColor = () => {
    if (userLimits.additionalUsers > 0) {
      return '#f97316'; // Orange for additional users
    }
    if (userLimits.remainingIncludedSlots <= 1) {
      return '#f59e0b'; // Yellow for near limit
    }
    return colors.primary; // Primary color for normal usage
  };

  const getBannerMessage = () => {
    if (userLimits.additionalUsers > 0) {
      return `You have ${userLimits.additionalUsers} additional user${userLimits.additionalUsers > 1 ? 's' : ''} at ₹${userLimits.additionalCost}/month`;
    }
    if (userLimits.remainingIncludedSlots === 0) {
      return 'You\'ve reached your included user limit. Additional users will require payment.';
    }
    if (userLimits.remainingIncludedSlots === 1) {
      return `You can add ${userLimits.remainingIncludedSlots} more user within your included limit.`;
    }
    return `You can add ${userLimits.remainingIncludedSlots} more users within your included limit.`;
  };

  const getIconName = () => {
    if (userLimits.additionalUsers > 0) {
      return 'currency-inr';
    }
    if (userLimits.remainingIncludedSlots <= 1) {
      return 'alert';
    }
    return 'information';
  };

  const bannerColor = getBannerColor();
  const progressPercentage = getProgressPercentage();

  return (
    <View style={styles.container}>
      {/* User Count Summary */}
      <View style={[styles.summaryCard, { backgroundColor: colors.background, borderColor: colors.icon }]}>
        <View style={styles.summaryHeader}>
          <Text style={[styles.summaryTitle, { color: colors.text }]}>
            {planName} Plan Usage
          </Text>
          <Text style={[styles.summaryCount, { color: colors.text }]}>
            {userLimits.totalUsers} / {userLimits.includedUsers} included
          </Text>
        </View>
        
        {/* Progress Bar */}
        <View style={[styles.progressBarContainer, { backgroundColor: colors.icon + '20' }]}>
          <View 
            style={[
              styles.progressBar, 
              { 
                width: `${progressPercentage}%`,
                backgroundColor: bannerColor
              }
            ]} 
          />
        </View>
        
        <View style={styles.summaryDetails}>
          <Text style={[styles.summaryText, { color: colors.icon }]}>
            Included: {userLimits.includedUsers} users
          </Text>
          {userLimits.additionalUsers > 0 && (
            <Text style={[styles.summaryText, { color: bannerColor }]}>
              Additional: {userLimits.additionalUsers} users
            </Text>
          )}
        </View>
      </View>

      {/* Status Banner */}
      <View style={[styles.statusBanner, { backgroundColor: bannerColor + '15', borderColor: bannerColor + '30' }]}>
        <MaterialCommunityIcons name={getIconName()} size={20} color={bannerColor} />
        <Text style={[styles.statusText, { color: bannerColor }]}>
          {getBannerMessage()}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  summaryCard: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 12,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  summaryCount: {
    fontSize: 16,
    fontWeight: '700',
  },
  progressBarContainer: {
    height: 8,
    borderRadius: 4,
    marginBottom: 12,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
    minWidth: 8,
  },
  summaryDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryText: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
});

export default UserLimitsBanner;
