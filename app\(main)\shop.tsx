import LocationSelector from '@/components/LocationSelector';
import ShopBottomTabs from '@/components/ShopBottomTabs';
import ShopCart from '@/components/ShopCart';
import ShopCategory from '@/components/ShopCategory';
import SponsoredBanners from '@/components/SponsoredBanners';
import { ThemedView } from '@/components/ThemedView';
import { useAuth } from '@/context/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';
import { supabase } from '@/lib/supabase';
import { FontAwesome5, MaterialIcons } from '@expo/vector-icons';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Alert,
    Dimensions,
    Image,
    Pressable,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    View
} from 'react-native';

// Get screen width for styling
const { width } = Dimensions.get('window');

// Import components
const TopTabHeader = require('@/components/TopTabHeader').default;

// Import Razorpay and order functions
const RazorpayCheckout = require('react-native-razorpay').default;
const { createShopOrder, updateShopOrderPayment } = require('@/lib/razorpay-orders');

// Razorpay configuration
const isProduction = process.env.NODE_ENV === 'production';
const RAZORPAY_CONFIG = {
  key_id: isProduction
    ? process.env.EXPO_PUBLIC_RAZORPAY_KEY_ID_LIVE
    : process.env.EXPO_PUBLIC_RAZORPAY_KEY_ID,
  currency: 'INR',
  company_name: 'Infratask',
  company_logo: 'https://cdn.razorpay.com/logos/QpmNzEJUBnXmVL_medium.png',
  theme_color: '#f97316'
};

// Import Razorpay and order functions

// Category image mapping for subcategories
const getCategoryImage = (categoryName: string) => {
  // Static image mapping for subcategories
  const categoryImages: { [key: string]: any } = {
    'Cement': require('@/assets/category/Cement.png'),
    'Painting': require('@/assets/category/Painting.png'),
    'Waterproofing': require('@/assets/category/Waterproofing.png'),
    'Fevicol': require('@/assets/category/Fevicol.png'),
    'Plywood MDF & HDHMR': require('@/assets/category/Plywood_MDF_and_HDHMR.png'),
    'Kitchen Systems & Accessories': require('@/assets/category/Kitchen_Systems_and_Accessories.png'),
    'Overhead Tanks': require('@/assets/category/Overhead Tanks.png'),
    'Sanitary & Bath Fittings': require('@/assets/category/Sanitary_and_Bath_Fittings.png'),
    'Tiles': require('@/assets/category/Tiles.png'),
    'Conduits & GI Boxes': require('@/assets/category/Conduits_and_GI_Boxes.png'),
    'CPVC Pipes & Fittings': require('@/assets/category/CPVC_Pipes_and_Fittings.png'),
    'Door Locks & Hardware': require('@/assets/category/Door_Locks_and_Hardware.png'),
    'Hinges & Channels & handles': require('@/assets/category/Hinges_and_Channels_and_handles.png'),
    'Switches & Sockets': require('@/assets/category/Switches_and_Sockets.png'),
    'Wardrobe & Bed Fittings': require('@/assets/category/Wardrobe_and_Bed_Fittings.png'),
    'Wires': require('@/assets/category/Wires.png'),
    'Bricks': require('@/assets/category/Bricks.png'),
    'Steel': require('@/assets/category/Steel.png'),
    'Sand': require('@/assets/category/Sand.png'),
    'Tools': require('@/assets/category/Tools.png'),
  };

  // Try exact match first
  if (categoryImages[categoryName]) {
    console.log(`✅ Found exact match for category: "${categoryName}"`);
    return categoryImages[categoryName];
  }

  // Default fallback - return first available image
  console.log(`❌ No match found for category: "${categoryName}", using fallback`);
  return require('@/assets/category/Tools.png');
};

// Types
interface Category {
  id: number;
  name: string;
  description: string;
  icon: string;
  color: string;
  display_order: number;
  is_active: boolean;
}

interface Product {
  id: number;
  category_id: number;
  name: string;
  description: string;
  image_url: string;
  price: number;
  original_price: number;
  unit: string;
  brand: string;
  stock_quantity: number;
  is_active: boolean;
}

interface CartItem {
  id: number;
  name: string;
  price: number;
  original_price?: number;
  quantity: number;
  unit: string;
  brand?: string;
}

// Sample construction materials data (fallback)
const materialCategories = [
  {
    id: 'cement',
    name: 'Cement & Concrete',
    icon: 'industry',
    items: [
      { id: 1, name: 'Portland Cement 50kg', price: 450, unit: 'bag', image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=200' },
      { id: 2, name: 'Ready Mix Concrete', price: 3500, unit: 'cubic meter', image: 'https://images.unsplash.com/photo-1504307651254-35680f356dfd?w=200' },
      { id: 3, name: 'White Cement 40kg', price: 650, unit: 'bag', image: 'https://images.unsplash.com/photo-1581094794329-c8112a89af12?w=200' },
    ]
  },
  {
    id: 'bricks',
    name: 'Bricks & Blocks',
    icon: 'cube',
    items: [
      { id: 4, name: 'Red Clay Bricks', price: 8, unit: 'piece', image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=200' },
      { id: 5, name: 'Concrete Blocks', price: 45, unit: 'piece', image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=200' },
      { id: 6, name: 'AAC Blocks', price: 85, unit: 'piece', image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=200' },
    ]
  },
  {
    id: 'steel',
    name: 'Steel & Metal',
    icon: 'weight-hanging',
    items: [
      { id: 7, name: 'TMT Steel Bars 12mm', price: 65, unit: 'kg', image: 'https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=200' },
      { id: 8, name: 'Mild Steel Angle', price: 55, unit: 'kg', image: 'https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=200' },
      { id: 9, name: 'Galvanized Steel Sheets', price: 120, unit: 'sq ft', image: 'https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=200' },
    ]
  },
  {
    id: 'paint',
    name: 'Paint & Finishes',
    icon: 'paint-roller',
    items: [
      { id: 10, name: 'Exterior Wall Paint 20L', price: 2800, unit: 'bucket', image: 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=200' },
      { id: 11, name: 'Interior Emulsion 4L', price: 850, unit: 'bucket', image: 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=200' },
      { id: 12, name: 'Wood Stain 1L', price: 450, unit: 'bottle', image: 'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=200' },
    ]
  },
  {
    id: 'tools',
    name: 'Tools & Equipment',
    icon: 'tools',
    items: [
      { id: 13, name: 'Electric Drill Machine', price: 3500, unit: 'piece', image: 'https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=200' },
      { id: 14, name: 'Measuring Tape 5m', price: 250, unit: 'piece', image: 'https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=200' },
      { id: 15, name: 'Safety Helmet', price: 180, unit: 'piece', image: 'https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=200' },
    ]
  },
];

export default function ShopScreen() {
  const params = useLocalSearchParams();

  // State management
  const [activeTab, setActiveTab] = useState<'shop' | 'category' | 'cart'>(
    (params.activeTab as 'shop' | 'category' | 'cart') || 'shop'
  );
  const [categories, setCategories] = useState<Category[]>([]);
  const [products, setProducts] = useState<Product[]>([]);

  const [searchQuery, setSearchQuery] = useState('');
  const [cart, setCart] = useState<CartItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentLocation, setCurrentLocation] = useState<{
    address: string;
    coordinates?: { latitude: number; longitude: number };
    deliveryTime?: string;
  } | null>(null);

  const colorScheme = useColorScheme();
  const { user, profile } = useAuth();
  const primaryColor = '#f97316';

  // Force light mode for shop page
  const backgroundColor = '#fff';
  const cardBackground = '#fff';
  const textColor = '#000';
  const secondaryTextColor = '#666';
  const borderColor = '#e5e5e5';

  // Load subcategories from Supabase (instead of main categories)
  const loadCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('shop_subcategories')
        .select('*')
        .eq('is_active', true)
        .order('display_order');

      if (error) throw error;

      if (data && data.length > 0) {
        // Convert subcategories to category format for compatibility
        const subcategoriesAsCategories = data.map(sub => ({
          id: sub.id,
          name: sub.name,
          description: sub.description,
          icon: sub.icon,
          color: '#f97316',
          display_order: sub.display_order,
          is_active: sub.is_active
        }));
        setCategories(subcategoriesAsCategories);
        console.log('Loaded subcategories as categories:', subcategoriesAsCategories.length);
      }
    } catch (error) {
      console.error('Error loading subcategories:', error);
      // Fallback to sample data
      const fallbackCategories = materialCategories.map((cat, index) => ({
        id: index + 1,
        name: cat.name,
        description: cat.name,
        icon: cat.icon,
        color: '#f97316',
        display_order: index + 1,
        is_active: true
      }));
      setCategories(fallbackCategories);
      console.log('Using fallback categories:', fallbackCategories.length);
    }
  };

  // Load products from Supabase
  const loadProducts = async () => {
    try {
      const { data, error } = await supabase
        .from('shop_products')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;

      if (data) {
        // Convert price fields from string to number
        const processedProducts = data.map(product => ({
          ...product,
          price: parseFloat(product.price) || 0,
          original_price: parseFloat(product.original_price) || 0
        }));
        setProducts(processedProducts);
        console.log('Loaded products:', processedProducts.length);
      }
    } catch (error) {
      console.error('Error loading products:', error);
      // Fallback to sample data
      const fallbackProducts = materialCategories.flatMap((cat, catIndex) =>
        cat.items.map((item, itemIndex) => ({
          id: item.id,
          category_id: catIndex + 1,
          name: item.name,
          description: item.name,
          image_url: item.image || '',
          price: item.price,
          original_price: item.price * 1.1,
          unit: item.unit,
          brand: 'Sample Brand',
          stock_quantity: 100,
          is_active: true
        }))
      );
      setProducts(fallbackProducts);
      console.log('Using fallback products:', fallbackProducts.length);
    }
  };

  // Cart management functions
  const addToCart = async (product: Product) => {
    const existingItem = cart.find(item => item.id === product.id);
    const newQuantity = existingItem ? existingItem.quantity + 1 : 1;

    if (existingItem) {
      setCart(cart.map(item =>
        item.id === product.id
          ? { ...item, quantity: newQuantity }
          : item
      ));
    } else {
      const newCartItem: CartItem = {
        id: product.id,
        name: product.name,
        price: product.price,
        quantity: 1,
        unit: product.unit,
        brand: product.brand
      };
      setCart([...cart, newCartItem]);
    }

    // Save to Supabase
    saveCartItem(product.id, newQuantity).catch(err =>
      console.log('Cart save failed, continuing with local state:', err)
    );

    Alert.alert('Added to Cart', `${product.name} has been added to your cart!`);
  };

  const updateCartQuantity = (itemId: number, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(itemId);
      return;
    }

    setCart(cart.map(item =>
      item.id === itemId
        ? { ...item, quantity }
        : item
    ));

    // Save to Supabase
    saveCartItem(itemId, quantity).catch(err =>
      console.log('Cart update failed, continuing with local state:', err)
    );
  };

  const removeFromCart = (itemId: number) => {
    setCart(cart.filter(item => item.id !== itemId));

    // Remove from Supabase
    removeCartItem(itemId).catch(err =>
      console.log('Cart remove failed, continuing with local state:', err)
    );
  };

  const getTotalItems = () => {
    return cart.reduce((sum, item) => sum + item.quantity, 0);
  };

  const handleCheckout = async () => {
    if (!user) {
      Alert.alert('Error', 'Please login to continue with checkout');
      return;
    }

    if (cart.length === 0) {
      Alert.alert('Error', 'Your cart is empty');
      return;
    }

    if (!currentLocation?.address) {
      Alert.alert('Error', 'Please select a delivery address');
      return;
    }

    try {
      // Calculate total amount
      const totalAmount = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

      // Prepare cart items for order
      const cartItems = cart.map(item => ({
        product_id: item.id,
        quantity: item.quantity,
        unit_price: item.price,
        total_price: item.price * item.quantity
      }));

      // Create shop order
      console.log('Creating shop order...');
      const orderResponse = await createShopOrder({
        cart_items: cartItems,
        total_amount: totalAmount,
        delivery_address: currentLocation.address,
        delivery_time: currentLocation.deliveryTime || 'Standard delivery (30-45 mins)',
        notes: {
          order_type: 'shop',
          total_items: cart.length.toString(),
          created_via: 'mobile_app'
        }
      });

      console.log('Shop order created successfully:', orderResponse.order.id);

      // Prepare Razorpay checkout options
      const options = {
        description: `Order for ${cart.length} items`,
        image: RAZORPAY_CONFIG.company_logo,
        currency: orderResponse.order.currency,
        key: RAZORPAY_CONFIG.key_id,
        amount: orderResponse.order.amount,
        name: RAZORPAY_CONFIG.company_name,
        order_id: orderResponse.order.id,
        prefill: {
          email: user.email || '',
          contact: user.phone || '',
          name: user.user_metadata?.full_name || user.email || ''
        },
        theme: { color: RAZORPAY_CONFIG.theme_color }
      };

      // Open Razorpay checkout
      RazorpayCheckout.open(options).then(async (paymentData: any) => {
        // Payment successful
        console.log('Payment successful:', paymentData);

        try {
          // Update shop order with payment details
          await updateShopOrderPayment({
            razorpay_order_id: paymentData.razorpay_order_id,
            razorpay_payment_id: paymentData.razorpay_payment_id,
            razorpay_signature: paymentData.razorpay_signature,
            payment_status: 'success',
            order_id: orderResponse.shop_order.id
          });

          console.log('Order payment updated successfully');

          // Navigate to order confirmation page
          router.push({
            pathname: '/order-confirmation',
            params: {
              orderId: orderResponse.shop_order.id,
              orderNumber: orderResponse.shop_order.order_number,
              totalAmount: totalAmount.toString(),
              deliveryAddress: currentLocation.address,
              deliveryTime: currentLocation.deliveryTime || 'Standard delivery (30-45 mins)'
            }
          });

        } catch (updateError) {
          console.error('Error updating payment status:', updateError);
          Alert.alert('Warning', 'Payment successful but there was an issue updating the order. Please contact support.');
        }

      }).catch(async (error: any) => {
        // Payment failed or cancelled
        console.log('Payment failed/cancelled:', error);

        try {
          // Update order status to cancelled
          await updateShopOrderPayment({
            razorpay_order_id: orderResponse.order.id,
            payment_status: 'cancelled',
            order_id: orderResponse.shop_order.id
          });
        } catch (updateError) {
          console.error('Error updating cancelled payment:', updateError);
        }

        if (error.code === 'payment_cancelled') {
          Alert.alert('Payment Cancelled', 'You cancelled the payment. Your cart is still saved.');
        } else {
          Alert.alert('Payment Failed', 'There was an issue processing your payment. Please try again.');
        }
      });

    } catch (error) {
      console.error('Error during checkout:', error);
      Alert.alert('Error', 'Failed to process checkout. Please try again.');
    }
  };

  // Load cart from Supabase
  const loadCart = async () => {
    if (!user) return;

    console.log('Loading cart from Supabase for user:', user.id);
    try {
      // Simple approach: just get cart items without joins
      const { data: cartData, error: cartError } = await supabase
        .from('shop_cart')
        .select('product_id, quantity')
        .eq('user_id', user.id);

      if (cartError) {
        console.log('Cart loading failed, using empty cart:', cartError);
        setCart([]);
        return;
      }

      if (cartData && cartData.length > 0) {
        // Get product details separately to avoid join issues
        const productIds = cartData.map(item => item.product_id);
        const { data: productsData, error: productsError } = await supabase
          .from('shop_products')
          .select('id, name, price, original_price, unit, brand')
          .in('id', productIds);

        if (productsError) {
          console.log('Product loading failed, using empty cart:', productsError);
          setCart([]);
          return;
        }

        if (productsData) {
          const cartItems: CartItem[] = cartData.map(cartItem => {
            const product = productsData.find(p => p.id === cartItem.product_id);
            return {
              id: cartItem.product_id,
              name: product?.name || 'Unknown Product',
              price: parseFloat(product?.price) || 0,
              original_price: parseFloat(product?.original_price) || 0,
              quantity: cartItem.quantity,
              unit: product?.unit || 'piece',
              brand: product?.brand || ''
            };
          });
          console.log('Loaded cart items:', cartItems.length, cartItems);
          setCart(cartItems);
        }
      } else {
        setCart([]);
      }
    } catch (error) {
      console.log('Cart loading failed completely, using empty cart:', error);
      setCart([]);
    }
  };

  // Save cart item to Supabase
  const saveCartItem = async (productId: number, quantity: number) => {
    if (!user) return;

    try {
      // First, try to update existing item
      const { data: existingItem, error: selectError } = await supabase
        .from('shop_cart')
        .select('id')
        .eq('user_id', user.id)
        .eq('product_id', productId)
        .single();

      if (selectError && selectError.code !== 'PGRST116') {
        // PGRST116 is "not found" error, which is expected for new items
        console.error('Error checking existing cart item:', selectError);
        return;
      }

      if (existingItem) {
        // Update existing item
        const { error: updateError } = await supabase
          .from('shop_cart')
          .update({
            quantity: quantity,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', user.id)
          .eq('product_id', productId);

        if (updateError) {
          console.error('Error updating cart item:', updateError);
        }
      } else {
        // Insert new item
        const { error: insertError } = await supabase
          .from('shop_cart')
          .insert({
            user_id: user.id,
            product_id: productId,
            quantity: quantity,
            added_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (insertError) {
          console.error('Error inserting cart item:', insertError);
        }
      }
    } catch (error) {
      console.error('Error saving cart item:', error);
      // Continue without throwing to prevent app crashes
    }
  };

  // Remove cart item from Supabase
  const removeCartItem = async (productId: number) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('shop_cart')
        .delete()
        .eq('user_id', user.id)
        .eq('product_id', productId);

      if (error) {
        console.error('Error removing cart item:', error);
        // Continue without throwing to prevent app crashes
      }
    } catch (error) {
      console.error('Error removing cart item:', error);
      // Continue without throwing to prevent app crashes
    }
  };

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        await Promise.all([loadCategories(), loadProducts()]);
        if (user) {
          await loadCart();
        }
      } catch (error) {
        console.error('Error loading shop data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [user]);

  // Refresh cart when activeTab changes to ensure sync
  useEffect(() => {
    const refreshCartOnTabChange = async () => {
      if (user && (activeTab === 'cart' || activeTab === 'shop')) {
        console.log('Refreshing cart due to tab change:', activeTab);
        await loadCart();
      }
    };

    refreshCartOnTabChange();
  }, [activeTab, user]);

  // Handle activeTab parameter from navigation
  useEffect(() => {
    if (params.activeTab && params.activeTab !== activeTab) {
      setActiveTab(params.activeTab as 'shop' | 'category' | 'cart');
    }
  }, [params.activeTab]);

  // Add app state change listener to refresh cart when app becomes active
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active' && user && (activeTab === 'cart' || activeTab === 'shop')) {
        refreshCart();
      }
    };

    // For now, we'll rely on tab changes and manual refresh
    // In a production app, you might want to add AppState listener here

  }, [activeTab, user]);

  // Filter products based on search query only
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesSearch;
  });

  // Debug logging
  console.log('Shop Debug:', {
    totalProducts: products.length,
    filteredProducts: filteredProducts.length,
    categories: categories.length,
    loading
  });

  // Get cart item quantity for a product
  const getCartQuantity = (productId: number) => {
    const cartItem = cart.find(item => item.id === productId);
    return cartItem ? cartItem.quantity : 0;
  };

  // Manual refresh cart function
  const refreshCart = async () => {
    if (user) {
      await loadCart();
    }
  };

  if (loading) {
    return (
      <>
        <TopTabHeader />
        <ThemedView style={[styles.container, { backgroundColor, justifyContent: 'center', alignItems: 'center' }]}>
          <Text style={[styles.loadingText, { color: textColor }]}>Loading...</Text>
        </ThemedView>
      </>
    );
  }

  return (
    <>
      {/* Only show TopTabHeader for shop tab */}
      {activeTab === 'shop' && <TopTabHeader />}
      <ThemedView style={[styles.container, { backgroundColor }]}>
        {activeTab === 'shop' ? (
          <ScrollView
            style={styles.scrollContainer}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.scrollContent}
          >
            {/* Header with Profile and Location */}
            <View style={styles.headerContainer}>
              {/* Profile Image */}
              <Pressable
                style={styles.profileImageContainer}
                onPress={() => router.push('/(main)/(tabs)/profile')}
              >
                {profile?.profile_image_url ? (
                  <Image
                    source={{ uri: profile.profile_image_url }}
                    style={styles.profileImage}
                  />
                ) : (
                  <View style={[styles.profileImagePlaceholder, { backgroundColor: primaryColor }]}>
                    <Text style={styles.profileImageText}>
                      {profile?.full_name?.charAt(0) || user?.email?.charAt(0) || '?'}
                    </Text>
                  </View>
                )}
              </Pressable>

              {/* Location Selector */}
              <View style={styles.locationSelectorContainer}>
                <LocationSelector
                  onLocationChange={(location) => {
                    setCurrentLocation(location);
                    console.log('Location changed:', location);
                  }}
                  initialLocation={currentLocation || undefined}
                />
              </View>
            </View>

            {/* Search Bar */}
            <View style={styles.searchContainer}>
              <MaterialIcons name="search" size={18} color={secondaryTextColor} />
              <TextInput
                style={[styles.searchInput, { color: textColor }]}
                placeholder="Search construction materials..."
                placeholderTextColor={secondaryTextColor}
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
            </View>

            {/* Sponsored Banners */}
            <SponsoredBanners />

            {/* Categories Grid */}
            <View style={styles.categoriesGridContainer}>
              {categories.map((category, index) => {
                if (index % 4 === 0) {
                  const categoryGroup = categories.slice(index, index + 4);
                  return (
                    <View key={`row-${index}`} style={styles.categoryRow}>
                      {categoryGroup.map((cat) => (
                        <Pressable
                          key={cat.id}
                          style={styles.categoryGridItem}
                          onPress={() => {
                            router.push({
                              pathname: '/(main)/category-details',
                              params: {
                                categoryId: cat.id.toString(),
                                categoryName: cat.name
                              }
                            });
                          }}
                        >
                          <View style={styles.categoryImageContainer}>
                            <Image
                              source={getCategoryImage(cat.name)}
                              style={styles.categoryImage}
                              resizeMode="contain"
                              defaultSource={require('@/assets/category/Tools.png')}
                              onError={(error) => {
                                console.log(`❌ Image load error for category "${cat.name}":`, error.nativeEvent?.error || 'Unknown error');
                              }}
                              onLoad={() => {
                                console.log(`✅ Image loaded successfully for category: "${cat.name}"`);
                              }}
                            />
                          </View>
                          <View style={styles.categoryTextContainer}>
                            <FontAwesome5
                              name={cat.icon as any}
                              size={10}
                              color={secondaryTextColor}
                              style={styles.categoryIcon}
                            />
                            <Text style={[styles.categoryGridText, { color: textColor }]} numberOfLines={2}>
                              {cat.name}
                            </Text>
                          </View>
                        </Pressable>
                      ))}
                    </View>
                  );
                }
                return null;
              })}
            </View>


          </ScrollView>
        ) : activeTab === 'category' ? (
          <ShopCategory
            onCategorySelect={(categoryId, categoryName) => {
              // Switch to shop tab and filter by category
              setActiveTab('shop');
              // You can add category filtering logic here
            }}
          />
        ) : (
          <ShopCart
            cartItems={cart}
            onUpdateQuantity={updateCartQuantity}
            onRemoveItem={removeFromCart}
            onCheckout={handleCheckout}
            onAddToCart={addToCart}
          />
        )}

      </ThemedView>

      {/* Bottom Tab Navigation */}
      <ShopBottomTabs
        activeTab={activeTab}
        onTabChange={async (tab) => {
          setActiveTab(tab);
          // Refresh cart when switching to cart or shop tab
          if ((tab === 'cart' || tab === 'shop') && user) {
            await refreshCart();
          }
        }}
        cartItemCount={getTotalItems()}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 100,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 12,
  },
  profileImageContainer: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  profileImage: {
    width: 44,
    height: 44,
    borderRadius: 22,
    borderWidth: 2,
    borderColor: '#f97316',
  },
  profileImagePlaceholder: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#f97316',
  },
  profileImageText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  locationSelectorContainer: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 10,
    marginBottom: 16,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#e9ecef',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 15,
    paddingVertical: 0,
    textAlignVertical: 'center',
    includeFontPadding: false,
  },
  categoryContainer: {
    marginBottom: 16,
  },
  categoryContent: {
    paddingHorizontal: 4,
  },
  categoryTab: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 12,
    borderRadius: 16,
    backgroundColor: '#f8f9fa',
    borderWidth: 1,
    borderColor: '#e9ecef',
    minWidth: 90,
  },
  activeCategoryTab: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  categoryImageContainer: {
    width: 60,
    height: 60,
    marginBottom: 4,
    borderRadius: 12,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  categoryImage: {
    width: 48,
    height: 48,
  },
  activeCategoryImageContainer: {
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: '#f97316',
  },
  categoryTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 2,
    paddingHorizontal: 2,
    flexWrap: 'wrap',
  },
  categoryIcon: {
    marginRight: 3,
  },
  categoryTabText: {
    fontSize: 13,
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: 15,
    flexShrink: 1,
  },
  categoriesGridContainer: {
    marginBottom: 16,
  },
  categoryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    gap: 4,
  },
  categoryGridItem: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  categoryGridText: {
    fontSize: 10,
    fontWeight: '600',
    textAlign: 'center',
    lineHeight: 12,
    flexShrink: 1,
    marginTop: 4,
  },
  materialsContainer: {
    flex: 1,
  },
  materialsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  materialCard: {
    width: '48%',
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  materialImage: {
    width: '100%',
    height: 120,
    resizeMode: 'cover',
  },
  materialInfo: {
    padding: 12,
  },
  materialName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  materialPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  addToCartButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    position: 'relative',
  },
  addToCartText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  quantityText: {
    marginHorizontal: 12,
    fontSize: 16,
    fontWeight: '600',
  },
  cartSummary: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e5e5e5',
  },
  cartInfo: {
    flex: 1,
  },
  cartItemCount: {
    fontSize: 14,
    fontWeight: '500',
  },
  cartTotal: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 2,
  },
  checkoutButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  checkoutButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  // New styles for the redesigned components
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
  },
  productsContainer: {
    paddingBottom: 20,
  },
  productRow: {
    justifyContent: 'space-between',
    paddingHorizontal: 4,
  },
  productCard: {
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    overflow: 'hidden',
  },
  productImage: {
    width: '100%',
    height: 120,
    resizeMode: 'cover',
  },
  productImagePlaceholder: {
    width: '100%',
    height: 120,
    alignItems: 'center',
    justifyContent: 'center',
  },
  productInfo: {
    padding: 12,
  },
  productName: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
    lineHeight: 18,
  },
  productBrand: {
    fontSize: 12,
    marginBottom: 6,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  productUnit: {
    fontSize: 12,
    marginLeft: 2,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
});
