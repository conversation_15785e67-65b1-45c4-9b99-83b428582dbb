#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up In-App Updates for Infratask');
console.log('==========================================\n');

// Check if we're in the right directory
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ Error: package.json not found. Please run this script from the project root.');
  process.exit(1);
}

const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
if (packageJson.name !== 'infratask') {
  console.error('❌ Error: This script should be run from the Infratask project root.');
  process.exit(1);
}

console.log('✅ Project validation passed\n');

// Check dependencies
console.log('📦 Checking dependencies...');
const requiredDeps = [
  'expo-updates',
  'expo-application',
  '@react-native-async-storage/async-storage'
];

const missingDeps = requiredDeps.filter(dep => !packageJson.dependencies[dep]);
if (missingDeps.length > 0) {
  console.log('⚠️  Missing dependencies:', missingDeps.join(', '));
  console.log('   Run: npm install ' + missingDeps.join(' '));
} else {
  console.log('✅ All required dependencies are installed');
}

// Check app.json configuration
console.log('\n🔧 Checking app.json configuration...');
const appJsonPath = path.join(process.cwd(), 'app.json');
if (fs.existsSync(appJsonPath)) {
  const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
  
  // Check expo-updates plugin
  const hasUpdatesPlugin = appJson.expo.plugins?.some(plugin => 
    plugin === 'expo-updates' || (Array.isArray(plugin) && plugin[0] === 'expo-updates')
  );
  
  if (hasUpdatesPlugin) {
    console.log('✅ expo-updates plugin configured');
  } else {
    console.log('⚠️  expo-updates plugin not found in app.json');
  }
  
  // Check updates configuration
  if (appJson.expo.updates) {
    console.log('✅ Updates configuration found');
  } else {
    console.log('⚠️  Updates configuration not found in app.json');
  }
} else {
  console.log('❌ app.json not found');
}

// Check Android configuration
console.log('\n🤖 Checking Android configuration...');
const androidBuildGradlePath = path.join(process.cwd(), 'android', 'app', 'build.gradle');
if (fs.existsSync(androidBuildGradlePath)) {
  const buildGradle = fs.readFileSync(androidBuildGradlePath, 'utf8');
  
  if (buildGradle.includes('com.google.android.play:app-update')) {
    console.log('✅ Play Core dependencies found in build.gradle');
  } else {
    console.log('⚠️  Play Core dependencies not found in build.gradle');
  }
} else {
  console.log('⚠️  Android build.gradle not found');
}

// Check native module files
console.log('\n📱 Checking native module files...');
const nativeModuleFiles = [
  'android/app/src/main/java/com/infratasks/app/InAppUpdateModule.kt',
  'android/app/src/main/java/com/infratasks/app/InAppUpdatePackage.kt'
];

nativeModuleFiles.forEach(filePath => {
  if (fs.existsSync(path.join(process.cwd(), filePath))) {
    console.log(`✅ ${filePath}`);
  } else {
    console.log(`⚠️  ${filePath} not found`);
  }
});

// Check TypeScript files
console.log('\n📝 Checking TypeScript implementation files...');
const tsFiles = [
  'lib/playStoreUpdates.ts',
  'lib/nativeInAppUpdate.ts',
  'lib/comprehensiveUpdateManager.ts',
  'hooks/usePlayStoreUpdates.ts',
  'components/PlayStoreUpdatePrompt.tsx'
];

tsFiles.forEach(filePath => {
  if (fs.existsSync(path.join(process.cwd(), filePath))) {
    console.log(`✅ ${filePath}`);
  } else {
    console.log(`⚠️  ${filePath} not found`);
  }
});

// Print setup instructions
console.log('\n📋 Setup Instructions:');
console.log('======================\n');

console.log('1. 🏗️  Build and Deploy:');
console.log('   - Run: eas build --platform android --profile production');
console.log('   - Upload to Google Play Console (Internal Testing track)');
console.log('   - Increment version code in app.json for testing\n');

console.log('2. 🧪 Testing Native Updates:');
console.log('   - Install app from Google Play Store (not sideloaded)');
console.log('   - Ensure device version < Play Console version');
console.log('   - Test with Internal Testing track first\n');

console.log('3. 🔄 Testing Expo Updates:');
console.log('   - Run: eas update --branch production');
console.log('   - Updates will be delivered via Expo\'s CDN\n');

console.log('4. 📱 Integration:');
console.log('   - The dashboard already includes update checking');
console.log('   - Updates are checked automatically on app launch');
console.log('   - Both native and Expo updates are supported\n');

console.log('5. 🐛 Debugging:');
console.log('   - Check logs in Metro bundler');
console.log('   - Use UpdateManagerDemo component for testing');
console.log('   - Monitor Google Play Console for update metrics\n');

// Print current version info
const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
console.log('📊 Current Version Info:');
console.log(`   Version: ${appJson.expo.version}`);
console.log(`   Android Version Code: ${appJson.expo.android.versionCode}`);
console.log(`   Package: ${appJson.expo.android.package}\n`);

console.log('🎉 Setup complete! Your app now supports both native Play Core and Expo updates.');
console.log('   For testing, increment the version code and build a new version.');
console.log('   The update system will automatically detect and prompt for updates.\n');

console.log('📚 For more information, refer to:');
console.log('   - https://developer.android.com/guide/playcore/in-app-updates');
console.log('   - https://docs.expo.dev/versions/latest/sdk/updates/');
console.log('   - https://docs.expo.dev/eas-update/introduction/\n');

console.log('✨ Happy updating! ✨');
