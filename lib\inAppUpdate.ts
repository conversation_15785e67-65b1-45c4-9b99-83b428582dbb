import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Application from 'expo-application';
import * as Linking from 'expo-linking';
import { Alert, Platform } from 'react-native';
import { supabase } from './supabase';

export interface AppVersion {
  version: string;
  versionCode: number;
  mandatory: boolean;
  releaseNotes?: string;
  downloadUrl?: string;
}

export class InAppUpdateManager {
  private static instance: InAppUpdateManager;
  private lastCheckTime: number = 0;
  private readonly CHECK_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
  private readonly DISMISSED_UPDATES_KEY = 'dismissedUpdates';
  private readonly PROMPTED_UPDATES_KEY = 'promptedUpdates';

  private constructor() {}

  static getInstance(): InAppUpdateManager {
    if (!InAppUpdateManager.instance) {
      InAppUpdateManager.instance = new InAppUpdateManager();
    }
    return InAppUpdateManager.instance;
  }

  /**
   * Get current app version information
   */
  getCurrentVersion(): { version: string; versionCode: number } {
    const version = Application.nativeApplicationVersion || '1.0.0';
    const versionCode = Application.nativeBuildVersion ? parseInt(Application.nativeBuildVersion) : 1;
    
    return { version, versionCode };
  }

  /**
   * Save dismissed update version to AsyncStorage
   */
  private async saveDismissedUpdate(versionCode: number): Promise<void> {
    try {
      const dismissedUpdates = await this.getDismissedUpdates();
      if (!dismissedUpdates.includes(versionCode)) {
        dismissedUpdates.push(versionCode);
        await AsyncStorage.setItem(this.DISMISSED_UPDATES_KEY, JSON.stringify(dismissedUpdates));
      }
    } catch (error) {
      console.error('Error saving dismissed update:', error);
    }
  }

  /**
   * Get dismissed updates from AsyncStorage
   */
  private async getDismissedUpdates(): Promise<number[]> {
    try {
      const dismissedUpdates = await AsyncStorage.getItem(this.DISMISSED_UPDATES_KEY);
      return dismissedUpdates ? JSON.parse(dismissedUpdates) : [];
    } catch (error) {
      console.error('Error getting dismissed updates:', error);
      return [];
    }
  }

  /**
   * Save prompted update version to AsyncStorage
   */
  private async savePromptedUpdate(versionCode: number): Promise<void> {
    try {
      const promptedUpdates = await this.getPromptedUpdates();
      if (!promptedUpdates.includes(versionCode)) {
        promptedUpdates.push(versionCode);
        await AsyncStorage.setItem(this.PROMPTED_UPDATES_KEY, JSON.stringify(promptedUpdates));
      }
    } catch (error) {
      console.error('Error saving prompted update:', error);
    }
  }

  /**
   * Get prompted updates from AsyncStorage
   */
  private async getPromptedUpdates(): Promise<number[]> {
    try {
      const promptedUpdates = await AsyncStorage.getItem(this.PROMPTED_UPDATES_KEY);
      return promptedUpdates ? JSON.parse(promptedUpdates) : [];
    } catch (error) {
      console.error('Error getting prompted updates:', error);
      return [];
    }
  }

  /**
   * Check if update has been dismissed or already prompted
   */
  private async isUpdateDismissedOrPrompted(versionCode: number): Promise<boolean> {
    try {
      const dismissedUpdates = await this.getDismissedUpdates();
      const promptedUpdates = await this.getPromptedUpdates();
      return dismissedUpdates.includes(versionCode) || promptedUpdates.includes(versionCode);
    } catch (error) {
      console.error('Error checking dismissed/prompted updates:', error);
      return false;
    }
  }

  /**
   * Clear old dismissed/prompted updates that are older than current version
   */
  private async clearOldUpdateRecords(): Promise<void> {
    try {
      const currentVersion = this.getCurrentVersion();
      
      // Clear dismissed updates older than current version
      const dismissedUpdates = await this.getDismissedUpdates();
      const validDismissedUpdates = dismissedUpdates.filter(versionCode => versionCode > currentVersion.versionCode);
      await AsyncStorage.setItem(this.DISMISSED_UPDATES_KEY, JSON.stringify(validDismissedUpdates));
      
      // Clear prompted updates older than current version
      const promptedUpdates = await this.getPromptedUpdates();
      const validPromptedUpdates = promptedUpdates.filter(versionCode => versionCode > currentVersion.versionCode);
      await AsyncStorage.setItem(this.PROMPTED_UPDATES_KEY, JSON.stringify(validPromptedUpdates));
    } catch (error) {
      console.error('Error clearing old update records:', error);
    }
  }

  /**
   * Check for app updates from remote source (Supabase)
   */
  async checkForUpdates(): Promise<AppVersion | null> {
    try {
      // Only check if enough time has passed since last check
      const now = Date.now();
      if (now - this.lastCheckTime < this.CHECK_INTERVAL) {
        return null;
      }

      const currentVersion = this.getCurrentVersion();
      
      // Clean up old records first
      await this.clearOldUpdateRecords();
      
      // Query the app_versions table for the latest version
      const { data: latestVersion, error } = await supabase
        .from('app_versions')
        .select('*')
        .eq('platform', Platform.OS === 'android' ? 'android' : 'ios')
        .eq('is_active', true)
        .order('version_code', { ascending: false })
        .limit(1)
        .single();

      if (error || !latestVersion) {
        console.log('No version information found or error:', error);
        return null;
      }

      this.lastCheckTime = now;

      // Check if update is needed
      if (latestVersion.version_code > currentVersion.versionCode) {
        // Check if this update has been dismissed or already prompted
        const isAlreadyHandled = await this.isUpdateDismissedOrPrompted(latestVersion.version_code);
        
        if (isAlreadyHandled && !latestVersion.mandatory) {
          // Don't show non-mandatory updates that have been dismissed or prompted
          return null;
        }
        
        return {
          version: latestVersion.version,
          versionCode: latestVersion.version_code,
          mandatory: latestVersion.mandatory || false,
          releaseNotes: latestVersion.release_notes,
          downloadUrl: latestVersion.download_url
        };
      }

      return null;
    } catch (error) {
      console.error('Error checking for updates:', error);
      return null;
    }
  }

  /**
   * Mark update as prompted to prevent repeated prompts
   */
  async markUpdateAsPrompted(versionCode: number): Promise<void> {
    await this.savePromptedUpdate(versionCode);
  }

  /**
   * Mark update as dismissed
   */
  async markUpdateAsDismissed(versionCode: number): Promise<void> {
    await this.saveDismissedUpdate(versionCode);
  }

  /**
   * Show update prompt to user
   */
  showUpdatePrompt(updateInfo: AppVersion): void {
    const { version, mandatory, releaseNotes } = updateInfo;
    
    const title = mandatory ? 'Update Required' : 'Update Available';
    const message = `A new version (${version}) is available.\n\n${releaseNotes || 'Bug fixes and improvements.'}`;
    
    const buttons = mandatory 
      ? [{ text: 'Update Now', onPress: () => this.openPlayStore() }]
      : [
          { 
            text: 'Later', 
            style: 'cancel' as const,
            onPress: () => this.markUpdateAsDismissed(updateInfo.versionCode)
          },
          { text: 'Update Now', onPress: () => this.openPlayStore() }
        ];

    Alert.alert(title, message, buttons, { cancelable: !mandatory });
  }

  /**
   * Open Play Store for app update
   */
  private openPlayStore(): void {
    const packageName = 'com.infratasks.app';
    const playStoreUrl = `market://details?id=${packageName}`;
    const fallbackUrl = `https://play.google.com/store/apps/details?id=${packageName}`;

    Linking.canOpenURL(playStoreUrl)
      .then((supported) => {
        if (supported) {
          Linking.openURL(playStoreUrl);
        } else {
          Linking.openURL(fallbackUrl);
        }
      })
      .catch(() => {
        Linking.openURL(fallbackUrl);
      });
  }

  /**
   * Force check for updates (ignoring time interval)
   */
  async forceCheckForUpdates(): Promise<AppVersion | null> {
    this.lastCheckTime = 0;
    return this.checkForUpdates();
  }

  /**
   * Check and show update prompt if available
   */
  async checkAndPromptForUpdate(): Promise<void> {
    try {
      const updateInfo = await this.checkForUpdates();
      if (updateInfo) {
        // Mark as prompted before showing
        await this.markUpdateAsPrompted(updateInfo.versionCode);
        this.showUpdatePrompt(updateInfo);
      }
    } catch (error) {
      console.error('Error in checkAndPromptForUpdate:', error);
    }
  }
}

// Export singleton instance
export const updateManager = InAppUpdateManager.getInstance(); 