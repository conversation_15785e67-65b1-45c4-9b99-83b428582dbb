import { useColorScheme } from '@/hooks/useColorScheme';
import { LinearGradient } from 'expo-linear-gradient';
import { router, usePathname } from 'expo-router';
import React from 'react';
import { Dimensions, Image, Pressable, StyleSheet, Text, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Get screen dimensions for responsive design
const { width: screenWidth } = Dimensions.get('window');

export default function TopTabHeader() {
  const insets = useSafeAreaInsets();
  const colorScheme = useColorScheme();
  const pathname = usePathname();
  const primaryColor = '#f97316';

  // Responsive calculations
  const isSmallScreen = screenWidth < 350;
  const isMediumScreen = screenWidth >= 350 && screenWidth < 400;
  const isLargeScreen = screenWidth >= 400;

  // Determine which tab is active based on the current path
  const isShopActive = pathname.includes('/shop');
  const isInfrataskActive = !isShopActive;

  // Force light mode for header when shop tab is active, otherwise use system theme
  const isDark = colorScheme === 'dark';
  const forceHeaderLightMode = isShopActive;

  // Header styling - force light mode when shop is active
  const headerBackgroundColor = forceHeaderLightMode ? '#fff' : (isDark ? '#000' : '#fff');
  const headerTextColor = forceHeaderLightMode ? '#000' : (isDark ? '#fff' : '#000');
  const headerTabContainerBg = forceHeaderLightMode ? '#f3f4f6' : (isDark ? '#1a1a1a' : '#f3f4f6');
  const headerBorderColor = forceHeaderLightMode ? '#e5e5e5' : (isDark ? '#333' : '#e5e5e5');

  const handleInfrataskPress = () => {
    router.push('/(main)/(tabs)');
  };

  const handleShopPress = () => {
    router.push('/(main)/shop');
  };

  return (
    <View style={[
      styles.container,
      {
        paddingTop: insets.top + (isSmallScreen ? 8 : 10),
        paddingHorizontal: isSmallScreen ? 4 : 8,
        backgroundColor: headerBackgroundColor,
        borderBottomColor: headerBorderColor
      }
    ]}>
      {/* Logo and Tabs Container */}
      <View style={styles.headerContent}>
        {/* Logo */}
        <View style={[
          styles.logoContainer,
          {
            width: isSmallScreen ? 32 : isMediumScreen ? 36 : 40,
            height: isSmallScreen ? 36 : isMediumScreen ? 40 : 44
          }
        ]}>
          <Image
            source={require('@/assets/images/tab logo.png')}
            style={[
              styles.logo,
              {
                width: isSmallScreen ? 24 : isMediumScreen ? 28 : 32,
                height: isSmallScreen ? 24 : isMediumScreen ? 28 : 32
              }
            ]}
            resizeMode="contain"
          />
        </View>

        {/* Tabs Container */}
        <View style={[
          styles.tabContainer,
          {
            backgroundColor: headerTabContainerBg,
            marginLeft: isSmallScreen ? 4 : 8,
            marginRight: isSmallScreen ? 4 : 8,
            padding: isSmallScreen ? 2 : 4
          }
        ]}>
        {/* Infratask Tab */}
        <Pressable
          style={[styles.tab, isInfrataskActive && styles.activeTab]}
          onPress={handleInfrataskPress}
        >
          <LinearGradient
            colors={isInfrataskActive ? ['#f97316', '#dc2626'] : ['transparent', 'transparent']}
            style={[
              styles.gradientTab,
              {
                paddingVertical: isSmallScreen ? 8 : isMediumScreen ? 10 : 12,
                paddingHorizontal: isSmallScreen ? 12 : isMediumScreen ? 16 : 20,
                minHeight: isSmallScreen ? 36 : isMediumScreen ? 40 : 44
              }
            ]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <Text style={[
              styles.tabText,
              {
                color: isInfrataskActive ? '#fff' : headerTextColor,
                fontSize: isSmallScreen ? 14 : isMediumScreen ? 16 : 18,
                fontWeight: '600',
                fontStyle: 'italic'
              }
            ]}>
              Infratask
            </Text>
          </LinearGradient>
        </Pressable>

        {/* Infratask Shop Tab */}
        <Pressable
          style={[styles.tab, isShopActive && styles.activeTab]}
          onPress={handleShopPress}
        >
          <LinearGradient
            colors={isShopActive ? ['#f97316', '#dc2626'] : ['transparent', 'transparent']}
            style={[
              styles.gradientTab,
              {
                paddingVertical: isSmallScreen ? 8 : isMediumScreen ? 10 : 12,
                paddingHorizontal: isSmallScreen ? 12 : isMediumScreen ? 16 : 20,
                minHeight: isSmallScreen ? 36 : isMediumScreen ? 40 : 44
              }
            ]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <Text style={[
              styles.tabText,
              {
                color: isShopActive ? '#fff' : headerTextColor,
                fontSize: isSmallScreen ? 14 : isMediumScreen ? 16 : 18,
                fontWeight: '600',
                fontStyle: 'italic'
              }
            ]}>
              {isSmallScreen ? 'Shop' : 'Infratask Shop'}
            </Text>
          </LinearGradient>
        </Pressable>
      </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderBottomWidth: 1,
    paddingBottom: 12,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  logoContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    // Dynamic width and height set via responsive props
  },
  logo: {
    // Dynamic width and height set via responsive props
  },
  tabContainer: {
    flex: 1,
    flexDirection: 'row',
    borderRadius: 25,
    // Dynamic padding and margins set via responsive props
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  tab: {
    flex: 1,
    borderRadius: 20,
    overflow: 'hidden',
  },
  activeTab: {
    shadowColor: '#f97316',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  tabText: {
    // Dynamic fontSize, fontWeight, and fontStyle set via responsive props
  },
  gradientTab: {
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    // Dynamic padding and minHeight set via responsive props
  },
});
