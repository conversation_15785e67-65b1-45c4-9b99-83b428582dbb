-- Migration: Add is_original column to transactions table
-- This helps distinguish between original transactions and auto-generated linked transactions
-- Date: 2025-01-17

-- Add is_original column to transactions table
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS is_original BOOLEAN DEFAULT true;

-- Create index for better performance on is_original queries
CREATE INDEX IF NOT EXISTS idx_transactions_is_original ON transactions(is_original);

-- Create compound index for site_id + is_original queries
CREATE INDEX IF NOT EXISTS idx_transactions_site_original ON transactions(site_id, is_original);

-- Add comment to document the column purpose
COMMENT ON COLUMN transactions.is_original IS 'TRUE for original transactions created by users, FALSE for auto-generated linked transactions';

-- Update existing transactions to be marked as original (backward compatibility)
-- This ensures existing transactions continue to show up
UPDATE transactions SET is_original = true WHERE is_original IS NULL;
