import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { supabase } from '@/lib/supabase';
import { MaterialIcons } from '@expo/vector-icons';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    ScrollView,
    StyleSheet,
    TouchableOpacity,
    useColorScheme,
    View
} from 'react-native';

type Material = {
  id: string;
  name: string;
  specifications: string | null;
  stock_level: number;
  minimum_stock_level: number;
  unit_of_measure: string;
  price: number;
  category: string | null;
  received_date: string;
  created_at: string;
  updated_at: string;
  site_id: string;
  used_stock?: number;
};

type UserRole = 'Super Admin' | 'Admin' | 'Member' | null;

export default function MaterialDetailsScreen() {
  const { id, materialId } = useLocalSearchParams();
  const colorScheme = useColorScheme();
  const [material, setMaterial] = useState<Material | null>(null);
  const [loading, setLoading] = useState(true);
  const [userRole, setUserRole] = useState<UserRole>(null);

  // Fetch user role and material data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Get current user
        const { data: userData } = await supabase.auth.getUser();
        if (!userData.user) {
          router.replace('/');
          return;
        }
        
        // Get user role
        const { data: memberData, error: memberError } = await supabase
          .from('site_members')
          .select('role')
          .eq('site_id', id)
          .eq('user_id', userData.user.id)
          .single();
        
        if (!memberData) {
          console.error('Error fetching user role:', memberError);
          Alert.alert('Access Denied', 'You do not have permission to access this site.');
          router.back();
          return;
        }
        
        setUserRole(memberData.role as UserRole);
        
        // Get material details
        const { data: materialData, error: materialError } = await supabase
          .from('materials')
          .select('*')
          .eq('id', materialId)
          .eq('site_id', id)
          .single();
        
        if (materialError || !materialData) {
          console.error('Error fetching material:', materialError);
          Alert.alert('Error', 'Failed to load material details.');
          router.back();
          return;
        }
        
        setMaterial(materialData);
      } catch (error) {
        console.error('Error in fetchData:', error);
        Alert.alert('Error', 'Something went wrong while loading material details.');
        router.back();
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [id, materialId]);

  // Handle edit material
  const handleEditMaterial = () => {
    if (!material) return;
    router.push(`/site/${id}/edit-material?materialId=${material.id}`);
  };
  
  // Handle delete material
  const handleDeleteMaterial = () => {
    if (!material) return;
    
    Alert.alert(
      'Confirm Deletion',
      'Are you sure you want to delete this material? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              
              const { error } = await supabase
                .from('materials')
                .delete()
                .eq('id', material.id);
              
              if (error) {
                console.error('Error deleting material:', error);
                Alert.alert('Error', 'Failed to delete material.');
                setLoading(false);
                return;
              }
              
              Alert.alert(
                'Success',
                'Material deleted successfully!',
                [{ text: 'OK', onPress: () => router.back() }]
              );
            } catch (error) {
              console.error('Error in handleDeleteMaterial:', error);
              Alert.alert('Error', 'Something went wrong while deleting the material.');
              setLoading(false);
            }
          },
        },
      ]
    );
  };
  
  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return `${date.toLocaleDateString()} at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  };

  if (loading) {
    return (
      <ThemedView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#f97316" />
      </ThemedView>
    );
  }

  if (!material) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText>Material not found</ThemedText>
      </ThemedView>
    );
  }

  const isLowStock = material.stock_level <= material.minimum_stock_level;

  return (
    <ThemedView style={styles.container}>
      <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
      
      <Stack.Screen
        options={{
          title: 'Material Details',
          headerBackTitle: 'Back',
          headerRight: (userRole === 'Admin' || userRole === 'Super Admin') ? () => (
            <TouchableOpacity onPress={handleEditMaterial}>
              <MaterialIcons name="edit" size={24} color="#f97316" />
            </TouchableOpacity>
          ) : undefined,
        }}
      />
      
      <ScrollView 
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.headerContainer}>
          <View style={styles.titleContainer}>
            <ThemedText style={styles.titleText}>{material.name}</ThemedText>
            {isLowStock && (
              <View style={styles.lowStockBadge}>
                <ThemedText style={styles.lowStockText}>Low Stock</ThemedText>
              </View>
            )}
          </View>
          
          {material.category && (
            <View style={styles.categoryBadge}>
              <ThemedText style={styles.categoryText}>{material.category}</ThemedText>
            </View>
          )}
        </View>
        
        <View style={styles.detailCard}>
          <ThemedText style={styles.sectionTitle}>Inventory Details</ThemedText>
          
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <ThemedText style={styles.detailLabel}>Current Stock</ThemedText>
              <ThemedText 
                style={[
                  styles.detailValue, 
                  isLowStock && styles.lowStockValue
                ]}
              >
                {material.stock_level} {material.unit_of_measure}
              </ThemedText>
            </View>
            
            <View style={styles.detailItem}>
              <ThemedText style={styles.detailLabel}>Minimum Stock</ThemedText>
              <ThemedText style={styles.detailValue}>
                {material.minimum_stock_level} {material.unit_of_measure}
              </ThemedText>
            </View>
          </View>
          
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <ThemedText style={styles.detailLabel}>Used Stock</ThemedText>
              <ThemedText style={styles.detailValue}>
                {material.used_stock || 0} {material.unit_of_measure}
              </ThemedText>
            </View>
            
            <View style={styles.detailItem}>
              <ThemedText style={styles.detailLabel}>Balance Stock</ThemedText>
              <ThemedText 
                style={[
                  styles.detailValue, 
                  ((material.stock_level - (material.used_stock || 0)) < material.minimum_stock_level) && styles.lowStockValue,
                  ((material.stock_level - (material.used_stock || 0)) >= material.minimum_stock_level + 10) && styles.goodStockValue
                ]}
              >
                {material.stock_level - (material.used_stock || 0)} {material.unit_of_measure}
              </ThemedText>
            </View>
          </View>
          
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <ThemedText style={styles.detailLabel}>Unit Price</ThemedText>
              <ThemedText style={styles.detailValue}>
                ₹{material.price.toFixed(2)}
              </ThemedText>
            </View>
          </View>
          
          <View style={styles.detailRow}>
            <View style={styles.detailItem}>
              <ThemedText style={styles.detailLabel}>Total Value</ThemedText>
              <ThemedText style={styles.detailValue}>
                ₹{(material.price * material.stock_level).toFixed(2)}
              </ThemedText>
            </View>
            
            <View style={styles.detailItem}>
              <ThemedText style={styles.detailLabel}>Date Received</ThemedText>
              <ThemedText style={styles.detailValue}>
                {formatDate(material.received_date)}
              </ThemedText>
            </View>
          </View>

          <View style={[styles.detailRow, styles.lastRow]}>
            <View style={styles.fullWidthDetailItem}>
              <ThemedText style={styles.detailLabel}>Last Updated</ThemedText>
              <ThemedText style={styles.detailValue}>
                {formatDateTime(material.updated_at)}
              </ThemedText>
            </View>
          </View>
        </View>
        
        {material.specifications && (
          <View style={styles.detailCard}>
            <ThemedText style={styles.sectionTitle}>Specifications</ThemedText>
            <ThemedText style={styles.specificationsText}>
              {material.specifications}
            </ThemedText>
          </View>
        )}
        
        {/* Delete button (only for Admin and Super Admin) */}
        {(userRole === 'Admin' || userRole === 'Super Admin') && (
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={handleDeleteMaterial}
          >
            <MaterialIcons name="delete" size={18} color="#fff" />
            <ThemedText style={styles.deleteButtonText}>Delete Material</ThemedText>
          </TouchableOpacity>
        )}
      </ScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollViewContent: {
    padding: 16,
  },
  headerContainer: {
    marginBottom: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#0f172a',
    flex: 1,
  },
  lowStockBadge: {
    backgroundColor: '#fef2f2',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginLeft: 8,
  },
  lowStockText: {
    color: '#ef4444',
    fontSize: 12,
    fontWeight: 'bold',
  },
  categoryBadge: {
    backgroundColor: '#f1f5f9',
    alignSelf: 'flex-start',
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
  },
  categoryText: {
    color: '#64748b',
    fontSize: 14,
  },
  detailCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#0f172a',
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  detailItem: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 16,
    color: '#0f172a',
    fontWeight: '500',
  },
  lowStockValue: {
    color: '#ef4444',
    fontWeight: '600',
  },
  goodStockValue: {
    color: '#16a34a',
    fontWeight: '600',
  },
  lastRow: {
    marginBottom: 0,
    borderTopWidth: 1,
    borderTopColor: '#f1f5f9',
    paddingTop: 16,
  },
  fullWidthDetailItem: {
    flex: 1,
  },
  specificationsText: {
    fontSize: 16,
    color: '#334155',
    lineHeight: 24,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ef4444',
    borderRadius: 8,
    padding: 16,
    marginVertical: 16,
  },
  deleteButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
}); 